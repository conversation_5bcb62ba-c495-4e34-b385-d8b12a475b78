/**
 * Mongoose事务组件库
 *
 * 简洁实用的事务处理解决方案，专注于：
 * - 功能性：完整的事务ACID特性支持
 * - 易用性：简单直观的API设计
 * - 安全性：类型安全和错误处理
 *
 * 核心组件：
 * - TransactionManager: 事务管理器
 * - BaseRepository: Repository基类
 * - BaseService: Service基类
 * - TransactionUtils: 工具函数
 */

// 核心组件
export { TransactionManager, TransactionUtils } from './transaction-manager';
export { BaseRepository_deprecated } from './base_repository_deprecated';
export { BaseService_deprecated } from './base_service_deprecated';
export { TransactionConfigManager } from './transaction-config';

// 类型定义
export type { TransactionOptions, TransactionOperation } from './transaction-manager';
export type { TransactionExecutionOptions } from './transaction-config';
export type {
  QueryOptionsExtended,
  PaginationOptions,
} from './base_repository_deprecated';
