import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { MicroserviceClientService } from '@libs/service-mesh';
import { HeroDefinition, GameConfigFacade } from '@libs/game-config';
import { HeroDocument } from '@character/common/schemas/hero.schema';
import { HeroRepository } from '@character/common/repositories/hero.repository';
import { HeroQuality } from '@character/common/types';
import { HeroPosition } from '@libs/game-constants';
import { CharacterService } from '../../character/character.service';
import { LineupService } from '../../lineup/lineup.service';
import {
  CreateHeroDto,
  UpdateHeroDto,
  TrainHeroDto,
  EvolveHeroDto,
  LevelUpHeroDto,
  UpgradeSkillDto,
  MarketOperationDto,
  HeroInfoDto,
  TrainResultDto,
  EvolveResultDto,
  LevelUpResultDto,
  GetHeroListDto
} from '@character/modules/hero/dto/hero.dto';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { GAME_CONSTANTS } from '@libs/game-constants';

import { XResult, XResultUtils } from '@libs/common/types/result.type';
import { CreatePayloadDto } from '@character/modules/hero/dto/hero-payload.dto';
// 保留MICROSERVICE_NAMES用于Activity服务等外部微服务调用
import { MICROSERVICE_NAMES } from "@libs/shared";

/**
 * 统一的球员业务逻辑层
 * 继承BaseService，提供专业的Result模式错误处理和业务逻辑管理
 * 提供完整的球员管理功能，包括创建、查询、更新、训练、升星、突破等核心功能
 *
 * 🎯 核心功能：
 * - 球员配置管理（静态配置查询、位置筛选、品质分类）
 * - 球员实例管理（创建、查询、更新、删除、批量操作）
 * - 球员训练系统（阶段性培养、特训替换、冷却管理）
 * - 球员升星系统（材料消耗、成功率计算、属性加成）
 * - 球员突破系统（潜能提升、费用计算、撤销机制）
 * - 球员状态管理（疲劳值、治疗状态、合约管理、退役检查）
 *
 * 🚀 性能优化：
 * - 继承BaseService的业务操作框架和性能监控
 * - 使用Repository层的Result模式错误处理
 * - 智能缓存机制优化球员数据访问
 * - 批量操作减少数据库访问次数
 * - 微服务调用的错误处理和重试机制
 *
 * 🔗 服务依赖：
 * - CharacterService：角色信息、金币检查、货币扣除（直接注入）
 * - Activity服务：任务触发、成就系统
 * - Market服务：市场交易、价格计算
 *
 * 📊 数据结构映射：
 * - HeroDocument: 球员实例数据（属性、状态、训练、升星等）
 * - HeroDefinition: 球员配置数据（基础属性、成长配置、技能等）
 * - HeroInfoDto: 客户端展示数据（包含适应性、统计数据等）
 */
@Injectable()
export class HeroService extends BaseService {
  constructor(
    private readonly heroRepository: HeroRepository,
    private readonly gameConfig: GameConfigFacade,
    @Inject(forwardRef(() => CharacterService))
    private readonly characterService: CharacterService,
    @Inject(forwardRef(() => LineupService))
    private readonly formationService: LineupService,
    microserviceClient: MicroserviceClientService,
  ) {
    super('HeroService', microserviceClient);
  }

  // ==================== 球员配置管理 ====================

  /**
   * 获取球员配置信息
   * 修复：使用GameConfigFacade获取静态配置，而不是通过Repository
   * Controller层已验证参数，无需重复验证
   */
  async getHeroConfig(heroId: number): Promise<XResult<any>> {
    try {
      const heroDefinition = await this.gameConfig.hero.get(heroId);
      if (!heroDefinition) {
        return XResultUtils.error('球员配置不存在', 'HERO_CONFIG_NOT_FOUND');
      }

      const heroConfigDto = this.toHeroConfigDto(heroDefinition);
      return XResultUtils.ok(heroConfigDto);
    } catch (error) {
      this.logger.error('获取球员配置信息失败', error);
      return XResultUtils.error('获取球员配置信息失败', 'GET_HERO_CONFIG_ERROR');
    }
  }

  /**
   * 根据位置获取球员配置
   * 修复：使用GameConfigFacade的filter方法进行高效筛选
   * Controller层已验证参数，无需重复验证
   */
  async getHeroConfigsByPosition(position: HeroPosition, limit: number = 50): Promise<XResult<any[]>> {
    try {
      // 使用GameConfigFacade的filter方法根据位置筛选
      // 注意：HeroDefinition中位置字段是position1和position2
      const heroDefinitions = await this.gameConfig.hero.filter(hero =>
        hero.position1 === position || hero.position2 === position
      );

      // 应用数量限制并按评分排序
      const limitedHeroes = heroDefinitions
        .sort((a, b) => b.rating - a.rating) // 按评分降序排序
        .slice(0, limit);

      const heroConfigDtos = limitedHeroes.map(heroDef => this.toHeroConfigDto(heroDef));
      return XResultUtils.ok(heroConfigDtos);
    } catch (error) {
      this.logger.error('根据位置获取球员配置失败', error);
      return XResultUtils.error('根据位置获取球员配置失败', 'GET_HERO_CONFIGS_BY_POSITION_ERROR');
    }
  }

  // ==================== 球员实例管理 ====================

  /**
   * 创建新球员
   * 修复：使用GameConfigFacade获取静态配置，而不是通过Repository
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async createHero(createDto: CreatePayloadDto): Promise<XResult<HeroDocument>> {
    return this.executeBusinessOperation(async () => {
      // 检查球员配置是否存在
      const heroDefinition = await this.gameConfig.hero.get(createDto.resId);
      if (!heroDefinition) {
        return XResultUtils.error('球员配置不存在', 'HERO_CONFIG_NOT_FOUND');
      }

      // 生成球员ID
      const heroId = this.generateHeroId();

      // 根据配置表ID和品质初始化属性
      const initialAttributes = this.generateInitialAttributes(createDto.resId, createDto.quality);

      // 🔧 从HeroDefinition获取初始技能
      const initialSkills = this.initializeHeroSkills(heroDefinition?.skillList || []);

      // 创建球员数据
      const heroData = {
        heroId,
        characterId: createDto.characterId,
        resId: createDto.resId,
        name: createDto.name,
        position: createDto.position,
        quality: createDto.quality,
        level: createDto.level || 1,
        nationality: createDto.nationality || '',
        club: createDto.club || '',
        attributes: initialAttributes,
        baseAttributes: { ...initialAttributes },
        skills: initialSkills.skills,           // 技能对象数组
        activeSkills: initialSkills.activeSkills, // 激活的技能ID数组
        obtainTime: Date.now(),
        obtainType: createDto.obtainType || 1,
        energy: 100,
        morale: 100,
        marketValue: this.calculateMarketValue(createDto.quality, createDto.level || 1),
      };

      // 🔧 直接使用createOne方法，因为heroData包含额外的技能字段
      // heroRepository.create只接受CreateHeroDto，但我们需要传递完整的heroData
      const createResult = await this.heroRepository.createOne(heroData);
      if (XResultUtils.isFailure(createResult)) {
        return XResultUtils.error(`创建球员失败: ${createResult.message}`, createResult.code);
      }

      this.logger.log(`球员创建成功: ${heroId}, 角色: ${createDto.characterId}`);
      return XResultUtils.ok(createResult.data);
    }, { reason: 'create_hero', metadata: { characterId: createDto.characterId, resId: createDto.resId } });
  }

  /**
   * 创建新球员（支持事务）
   * 基于createHero方法，添加session支持以确保事务一致性
   *
   * @param createDto 创建球员DTO
   * @param session 数据库事务session
   */
  async createHeroWithSession(createDto: CreatePayloadDto, session: any): Promise<XResult<HeroDocument>> {
    // 检查球员配置是否存在
    const heroDefinition = await this.gameConfig.hero.get(createDto.resId);
    if (!heroDefinition) {
      return XResultUtils.error('球员配置不存在', 'HERO_CONFIG_NOT_FOUND');
    }

    // 生成球员ID
    const heroId = this.generateHeroId();

    // 根据配置表ID和品质初始化属性
    const initialAttributes = this.generateInitialAttributes(createDto.resId, createDto.quality);

    // 创建球员数据
    const heroData = {
      heroId,
      characterId: createDto.characterId,
      resId: createDto.resId,
      name: createDto.name,
      position: createDto.position,
      quality: createDto.quality,
      level: createDto.level || 1,
      nationality: createDto.nationality || '',
      club: createDto.club || '',
      attributes: initialAttributes,
      baseAttributes: { ...initialAttributes },
      obtainTime: Date.now(),
      obtainType: createDto.obtainType || 1,
      energy: 100,
      morale: 100,
      marketValue: this.calculateMarketValue(createDto.quality, createDto.level || 1),
    };

    // 使用session创建球员（BaseRepository的createOne方法支持session）
    const createResult = await this.heroRepository.createOne(heroData, session);
    if (XResultUtils.isFailure(createResult)) {
      return XResultUtils.error(`创建球员失败: ${createResult.message}`, createResult.code);
    }

    this.logger.log(`球员创建成功(事务): ${heroId}, 角色: ${createDto.characterId}`);
    return XResultUtils.ok(createResult.data);
  }

  /**
   * 批量获取球员信息
   * Controller层已验证参数，无需重复验证
   */
   async getBatchHeroes(heroIds: string[]): Promise<XResult<HeroInfoDto[]>> {
    const heroesResult = await this.heroRepository.findByIds(heroIds);
    if (XResultUtils.isFailure(heroesResult)) {
      return XResultUtils.error(`批量获取球员信息失败: ${heroesResult.message}`, heroesResult.code);
    }

    const heroes = heroesResult.data || [];
    // 过滤掉null/undefined的球员，只返回存在的球员信息
    const validHeroes = heroes.filter(hero => hero != null);

    if (validHeroes.length === 0) {
      this.logger.warn(`批量获取球员信息：请求${heroIds.length}个球员，但没有找到任何有效球员`);
      return XResultUtils.ok([]);
    }

    if (validHeroes.length < heroIds.length) {
      const foundIds = validHeroes.map(hero => hero.heroId);
      const missingIds = heroIds.filter(id => !foundIds.includes(id));
      this.logger.warn(`批量获取球员信息：请求${heroIds.length}个球员，找到${validHeroes.length}个，缺失球员ID: ${missingIds.join(', ')}`);
    }

    const heroInfoDtos = validHeroes.map(hero => this.toHeroInfoDto(hero));
    return XResultUtils.ok(heroInfoDtos);
  }

  /**
   * 获取球员信息
   * Controller层已验证参数，无需重复验证
   */
  async getHeroInfo(heroId: string): Promise<XResult<HeroInfoDto>> {
    const heroResult = await this.heroRepository.findById(heroId);
    if (XResultUtils.isFailure(heroResult)) {
      return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
    }

    const hero = heroResult.data;
    if (!hero) {
      return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
    }

    const heroInfoDto = this.toHeroInfoDto(hero);
    return XResultUtils.ok(heroInfoDto);
  }

  /**
   * 更新球员信息
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async updateHero(heroId: string, updateDto: UpdateHeroDto): Promise<XResult<HeroDocument>> {
    return this.executeBusinessOperation(async () => {
      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      const updateResult = await this.heroRepository.update(heroId, updateDto);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员信息失败: ${updateResult.message}`, updateResult.code);
      }

      this.logger.log(`球员信息更新成功: ${heroId}`);
      return XResultUtils.ok(updateResult.data);
    }, { reason: 'update_hero', metadata: { heroId } });
  }

  /**
   * 获取球员列表
   * Controller层已验证参数，无需重复验证
   */
  async getHeroList(query: GetHeroListDto): Promise<XResult<any>> {
    const result = await this.heroRepository.findWithPagination(query, query.characterId);
    if (XResultUtils.isFailure(result)) {
      return XResultUtils.error(`获取球员列表失败: ${result.message}`, result.code);
    }

    const paginationData = result.data;
    this.logger.log(`获取球员列表: ${query.characterId}, 总数: ${paginationData.total}`);

    const heroListData = {
      list: paginationData.data.map(hero => this.toHeroInfoDto(hero)),
      total: paginationData.total,
      page: paginationData.page,
      limit: paginationData.limit,
      pages: paginationData.pages,
      hasNext: paginationData.hasNext,
      hasPrev: paginationData.hasPrev,
    };

    return XResultUtils.ok(heroListData);
  }

  /**
   * 设置球员治疗状态
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async setHeroTreatStatus(heroId: string, isTreat: boolean): Promise<XResult<HeroDocument>> {
    return this.executeBusinessOperation(async () => {
      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      const updateResult = await this.heroRepository.updateById(heroId, { isTreat });
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员治疗状态失败: ${updateResult.message}`, updateResult.code);
      }

      const updatedHeroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(updatedHeroResult)) {
        return XResultUtils.error(`获取更新后球员信息失败: ${updatedHeroResult.message}`, updatedHeroResult.code);
      }

      this.logger.log(`球员治疗状态更新: ${heroId}, 治疗中: ${isTreat}`);
      return XResultUtils.ok(updatedHeroResult.data);
    }, { reason: 'set_hero_treat_status', metadata: { heroId, isTreat } });
  }

  /**
   * 设置球员训练状态
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async setHeroTrainStatus(heroId: string, isTrain: boolean, isLockTrain?: boolean): Promise<XResult<HeroDocument>> {
    return this.executeBusinessOperation(async () => {
      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      const updateData: any = { isTrain };
      if (isLockTrain !== undefined) {
        updateData.isLockTrain = isLockTrain;
      }

      const updateResult = await this.heroRepository.updateById(heroId, updateData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员训练状态失败: ${updateResult.message}`, updateResult.code);
      }

      const updatedHeroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(updatedHeroResult)) {
        return XResultUtils.error(`获取更新后球员信息失败: ${updatedHeroResult.message}`, updatedHeroResult.code);
      }

      this.logger.log(`球员训练状态更新: ${heroId}, 训练中: ${isTrain}, 锁定: ${isLockTrain}`);
      return XResultUtils.ok(updatedHeroResult.data);
    }, { reason: 'set_hero_train_status', metadata: { heroId, isTrain, isLockTrain } });
  }

  /**
   * 更新球员疲劳值
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async updateHeroFatigue(heroId: string, fatigueChange: number): Promise<XResult<HeroDocument>> {
    return this.executeBusinessOperation(async () => {
      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      // 更新疲劳值，确保在0-100范围内
      const newFatigue = Math.max(0, Math.min(100, hero.fatigue + fatigueChange));

      const updateResult = await this.heroRepository.updateById(heroId, {
        fatigue: newFatigue,
        reTimeFatigue: Date.now()
      });
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员疲劳值失败: ${updateResult.message}`, updateResult.code);
      }

      const updatedHeroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(updatedHeroResult)) {
        return XResultUtils.error(`获取更新后球员信息失败: ${updatedHeroResult.message}`, updatedHeroResult.code);
      }

      this.logger.log(`球员疲劳值更新: ${heroId}, 变化: ${fatigueChange}, 当前: ${newFatigue}`);
      return XResultUtils.ok(updatedHeroResult.data);
    }, { reason: 'update_hero_fatigue', metadata: { heroId, fatigueChange } });
  }

  /**
   * 球员突破（潜能系统）
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async breakthroughHero(heroId: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      const currentBreakthroughCount = hero.breakthrough.length;

      // 检查是否可以突破（最多10次）
      if (currentBreakthroughCount >= 10) {
        return XResultUtils.error('球员突破次数已达上限', 'HERO_BREAKTHROUGH_LIMIT');
      }

      // 计算突破费用
      const breakthroughCost = this.calculateBreakthroughCost(currentBreakthroughCount);

      // 检查金币是否足够（基于old项目逻辑）
      const goldCheckResult = await this.checkCharacterGold(hero.characterId, breakthroughCost);
      if (XResultUtils.isFailure(goldCheckResult)) {
        return XResultUtils.error(`检查金币失败: ${goldCheckResult.message}`, goldCheckResult.code);
      }

      if (!goldCheckResult.data.sufficient) {
        return XResultUtils.failure('金币不足', 'INSUFFICIENT_GOLD', {
          required: breakthroughCost,
          current: goldCheckResult.data.current,
          deficit: breakthroughCost - goldCheckResult.data.current,
        });
      }

      // 扣除金币
      const deductResult = await this.deductCharacterGold(hero.characterId, breakthroughCost);
      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`金币扣除失败: ${deductResult.message}`, deductResult.code);
      }

      if (!deductResult.data) {
        return XResultUtils.error('金币扣除失败', 'DEDUCT_GOLD_FAILED');
      }

      // 根据球员品质和突破次数计算突破值（1-7）
      const breakthroughValue = this.calculateBreakthroughValue(hero.quality, currentBreakthroughCount + 1);

      // 计算新的潜能值
      const oldPotential = this.calculateCurrentPotential(hero.breakthrough);
      const newPotential = oldPotential + breakthroughValue;

      // 更新突破记录
      const newBreakthrough = [...hero.breakthrough, breakthroughValue];

      // 更新潜能上限
      const newOldBreakOut = Math.max(hero.oldBreakOut, 30 + newPotential);

      // 计算属性上限提升
      const attributeLimits = this.calculateAttributeLimits(hero.resId, newPotential);

      // 更新球员数据
      const updateResult = await this.heroRepository.update(heroId, {
        breakthrough: newBreakthrough,
        oldBreakOut: newOldBreakOut,
      });

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员突破数据失败: ${updateResult.message}`, updateResult.code);
      }

      // 重新计算属性（如果球员在阵容中，需要更新阵容属性）
      // TODO: 通知Character服务更新阵容属性

      this.logger.log(`球员突破成功: ${heroId}, 突破值: ${breakthroughValue}, 新潜能: ${newPotential}`);

      return XResultUtils.ok({
        success: true,
        heroId,
        breakthroughValue,
        oldPotential,
        newPotential,
        breakthroughCount: newBreakthrough.length,
        cost: breakthroughCost,
        attributeLimits,
        canBreakthroughAgain: newBreakthrough.length < 10,
      });
    }, { reason: 'breakthrough_hero', metadata: { heroId } });
  }

  /**
   * 撤销球员突破
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async revertBreakthrough(heroId: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      // 检查是否有突破记录
      if (hero.breakthrough.length === 0) {
        return XResultUtils.error('没有突破记录可撤销', 'NO_BREAKTHROUGH_TO_REVERT');
      }

      // 检查是否有非完美突破（值不为7）
      const nonPerfectIndex = hero.breakthrough.findIndex(value => value !== 7);
      if (nonPerfectIndex === -1) {
        return XResultUtils.error('所有突破都是完美突破，无法撤销', 'ALL_BREAKTHROUGH_PERFECT');
      }

      // 计算撤销费用
      const revertCost = this.calculateRevertCost();

      // TODO: 检查金币是否足够（需要与Character服务通信）

      // 移除最后一次突破
      const breakthroughCopy = [...hero.breakthrough];
      const revertedValue = breakthroughCopy.pop();
      const newBreakthrough = breakthroughCopy;

      // 重新计算潜能值
      const newPotential = this.calculateCurrentPotential(newBreakthrough);
      const newOldBreakOut = Math.max(30, 30 + newPotential);

      // 更新球员数据
      const updateResult = await this.heroRepository.update(heroId, {
        breakthrough: newBreakthrough,
        oldBreakOut: newOldBreakOut,
      });

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员突破数据失败: ${updateResult.message}`, updateResult.code);
      }

      this.logger.log(`球员突破撤销成功: ${heroId}, 撤销值: ${revertedValue}, 新潜能: ${newPotential}`);

      return XResultUtils.ok({
        success: true,
        heroId,
        revertedValue,
        newPotential,
        breakthroughCount: newBreakthrough.length,
        cost: revertCost,
      });
    }, { reason: 'revert_breakthrough', metadata: { heroId } });
  }

  /**
   * 训练球员（阶段性培养系统）
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async trainHero(trainDto: TrainHeroDto): Promise<XResult<TrainResultDto>> {
    return this.executeBusinessOperation(async () => {
      const heroResult = await this.heroRepository.findById(trainDto.heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      // 检查是否可以训练
      if (!hero.canTrain) {
        return XResultUtils.error('球员训练冷却中', 'HERO_TRAINING_COOLDOWN');
      }

      // 检查球员是否在训练中或治疗中
      if (!hero.canTrain || hero.isTreat) {
        return XResultUtils.error('球员正在训练或治疗中', 'HERO_IN_TRAINING');
      }

      const count = trainDto.count || 1;
      const trainingTime = Date.now();

      // 根据训练类型执行不同的训练逻辑
      let trainingResult;
      switch (trainDto.trainingType) {
        case 1: // 初级训练
        case 2: // 中级训练
        case 3: // 高级训练
          trainingResult = await this.executeGeneralTraining(hero, trainDto, count);
          break;
        case 4: // 定向训练
          trainingResult = await this.executeTargetedTraining(hero, trainDto, count);
          break;
        default:
          return XResultUtils.error('无效的训练类型', 'INVALID_TRAINING_TYPE');
      }

      if (XResultUtils.isFailure(trainingResult)) {
        return XResultUtils.error(`训练执行失败: ${trainingResult.message}`, trainingResult.code);
      }

      // 更新球员训练数据
      const updateData = {
        'training.trainingCount': (hero.training?.trainingCount || 0) + count,
        'training.lastTrainingTime': trainingTime,
        'training.trainingCooldown': this.calculateTrainingCooldown(trainDto.trainingType),
        ...trainingResult.data.updateData,
      };

      const updateResult = await this.heroRepository.update(trainDto.heroId, updateData);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员训练数据失败: ${updateResult.message}`, updateResult.code);
      }

      this.logger.log(`球员训练完成: ${trainDto.heroId}, 类型: ${trainDto.trainingType}, 次数: ${count}`);

      const trainResultDto: TrainResultDto = {
        success: true,
        heroId: trainDto.heroId,
        trainingType: trainDto.trainingType,
        trainingMethod: trainDto.trainingMethod,
        attributeChanges: trainingResult.data.attributeChanges,
        goldCost: trainingResult.data.goldCost,
        itemCost: trainingResult.data.itemCost,
        trainCount: count,
        currentStage: trainingResult.data.currentStage,
        reachedStageLimit: trainingResult.data.reachedStageLimit,
        nextTrainTime: trainingTime + this.calculateTrainingCooldown(trainDto.trainingType),
        trainingTime,
      };

      return XResultUtils.ok(trainResultDto);
    }, { reason: 'train_hero', metadata: { heroId: trainDto.heroId, trainingType: trainDto.trainingType } });
  }

  /**
   * 球员升星
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async evolveHero(evolveDto: EvolveHeroDto): Promise<XResult<EvolveResultDto>> {
    return this.executeBusinessOperation(async () => {
      const heroResult = await this.heroRepository.findById(evolveDto.heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      const oldStar = hero.evolution?.star || 0;

      // 检查是否可以升星
      if (!hero.canEvolve) {
        return XResultUtils.error('球员无法升星', 'HERO_CANNOT_EVOLVE');
      }

      // 检查是否已达到最高星级
      if (oldStar >= 9) {
        return XResultUtils.error('球员已达到最高星级', 'HERO_MAX_STAR');
      }

      // 验证材料球员数量
      const requiredMaterialCount = hero.evolutionMaterialCount || 1;
      if (evolveDto.materialHeroIds.length < requiredMaterialCount && !evolveDto.useUniversalCard) {
        return XResultUtils.failure('升星材料不足', 'INSUFFICIENT_MATERIAL', {
          required: requiredMaterialCount,
          provided: evolveDto.materialHeroIds.length
        });
      }

      // 验证材料球员
      const materialHeroResults = await Promise.all(
        evolveDto.materialHeroIds.map(id => this.heroRepository.findById(id))
      );

      for (let i = 0; i < materialHeroResults.length; i++) {
        const materialHeroResult = materialHeroResults[i];
        if (XResultUtils.isFailure(materialHeroResult)) {
          return XResultUtils.error(`获取材料球员信息失败: ${materialHeroResult.message}`, materialHeroResult.code);
        }

        const materialHero = materialHeroResult.data;
        if (!materialHero || materialHero.isLocked || materialHero.isInLineup) {
          return XResultUtils.failure('无效的材料球员', 'INVALID_MATERIAL_HERO', {
            heroId: evolveDto.materialHeroIds[i],
            reason: !materialHero ? '球员不存在' : materialHero.isLocked ? '球员已锁定' : '球员在阵容中'
          });
        }
      }

      // 计算升星成功率
      const successRate = hero.evolutionSuccessRate;

      // 计算消耗的金币
      const goldCost = this.calculateEvolutionGoldCost(oldStar, evolveDto.useProtection);

      // TODO: 检查金币是否足够（需要与Character服务通信）

      // 执行升星逻辑
      let isEvolutionSuccess = false;
      let newStar = oldStar;

      if (evolveDto.useProtection) {
        // 使用保护道具，必定成功
        isEvolutionSuccess = true;
        newStar = oldStar + 1;
      } else {
        // 概率升星
        const random = Math.random() * 100;
        if (random <= successRate) {
          isEvolutionSuccess = true;
          newStar = oldStar + 1;
        } else {
          // 升星失败，可能降星
          const degradeRate = this.calculateDegradeRate(oldStar);
          const degradeRandom = Math.random() * 100;
          if (degradeRandom <= degradeRate && oldStar > 0) {
            newStar = Math.max(0, oldStar - 1);
          } else {
            newStar = oldStar; // 保持原星级
          }
        }
      }

      const evolutionTime = Date.now();

      // 计算属性加成
      const attributeBonus = this.calculateEvolutionAttributeBonus(hero, newStar);

      // 更新球员数据
      const updateData = {
        'evolution.star': newStar,
        'evolution.evolutionCount': hero.evolution.evolutionCount + 1,
        'evolution.lastEvolutionTime': evolutionTime,
        'evolution.consumedHeroes': [...hero.evolution.consumedHeroes, ...evolveDto.materialHeroIds],
        'evolution.isMaxStar': newStar >= 9,
      };

      // 应用属性加成
      Object.keys(attributeBonus).forEach(attr => {
        updateData[`attributes.${attr}`] = hero.attributes[attr] + attributeBonus[attr];
      });

      await this.heroRepository.update(evolveDto.heroId, updateData);

      // 删除材料球员
      await Promise.all(
        evolveDto.materialHeroIds.map(id => this.heroRepository.softDelete(id))
      );

      this.logger.log(`球员升星完成: ${evolveDto.heroId}, ${oldStar} -> ${newStar}, 成功: ${isEvolutionSuccess}`);

      const evolveResultDto: EvolveResultDto = {
        success: true,
        heroId: evolveDto.heroId,
        oldStar,
        newStar,
        isEvolutionSuccess,
        successRate,
        attributeBonus,
        consumedHeroes: evolveDto.materialHeroIds,
        consumedItems: evolveDto.items || [],
        consumedGold: goldCost,
        newOverallRating: hero.calculateOverallRating(),
        evolutionTime,
      };

      return XResultUtils.ok(evolveResultDto);
    }, { reason: 'evolve_hero', metadata: { heroId: evolveDto.heroId} });
  }

  /**
   * 生成球员ID
   */
  private generateHeroId(): string {
    return `hero_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 初始化球员技能
   * 基于old项目第2443-2449行的技能数据结构
   *
   * @param skillList 技能ID列表，来自HeroDefinition.skillList
   * @returns 包含skills和activeSkills的对象
   */
  private initializeHeroSkills(skillList: number[]): { skills: any[], activeSkills: number[] } {
    try {
      const skills: any[] = [];
      const activeSkills: number[] = [];

      // 基于old项目第2443行：skillList结构
      for (const skillId of skillList) {
        if (typeof skillId === 'number' && skillId > 0) {
          // 基于old项目第2446行：isActivate字段
          const isActivate = skills.length < 4 ? 1 : 0; // 前4个技能自动激活
          this.logger.log(`初始化技能: ${skillId}, 激活: ${isActivate}`);
          // 创建技能对象 - 基于old项目的数据结构
          const skillObj = {
            skillId,           // 基于old项目第2447行
            isActivate,        // 基于old项目第2446行：1=激活，0=未激活
            level: 1,          // 技能等级
          };

          skills.push(skillObj);

          // 基于old项目第2446行：只有isActivate === 1的技能才加入activeSkills
          if (isActivate === 1) {
            activeSkills.push(skillId);
            this.logger.log(`技能激活: ${skillId}`);
          }
        }
      }

      this.logger.debug(`初始化技能完成: 总技能数=${skills.length}, 激活技能数=${activeSkills.length}`);
      return { skills, activeSkills };
    } catch (error) {
      this.logger.error('初始化球员技能失败', error);
      return { skills: [], activeSkills: [] };
    }
  }



  /**
   * 计算球员位置适应性
   * 基于old项目的calcHeroMatchRate方法实现
   */
  private calculatePositionAdaptability(hero: HeroDocument): any {
    // 位置映射表（基于old项目的TEAM_FORMATION_CONFIG_POSITION_TO_ID）
    const positionMapping = {
      'GK': 1, 'DC': 2, 'DL': 3, 'DR': 4, 'DM': 5, 'MC': 6,
      'ML': 7, 'MR': 8, 'AM': 9, 'ST': 10, 'WL': 11, 'WR': 12
    };

    // 基础适应性配置表（基于old项目的PositionMatch.json）
    const positionMatchConfig = {
      1: { GK: 1.0, DC: 0.3, DL: 0.3, DR: 0.3, DM: 0.3, MC: 0.3, ML: 0.3, MR: 0.3, AM: 0.3, ST: 0.3, WL: 0.3, WR: 0.3 }, // 门将
      2: { GK: 0.3, DC: 1.0, DL: 0.8, DR: 0.8, DM: 0.85, MC: 0.7, ML: 0.5, MR: 0.5, AM: 0.3, ST: 0.3, WL: 0.3, WR: 0.3 }, // 中后卫
      3: { GK: 0.3, DC: 0.8, DL: 1.0, DR: 0.8, DM: 0.6, MC: 0.6, ML: 0.95, MR: 0.5, AM: 0.4, ST: 0.3, WL: 0.9, WR: 0.3 }, // 左后卫
      4: { GK: 0.3, DC: 0.8, DL: 0.8, DR: 1.0, DM: 0.6, MC: 0.6, ML: 0.5, MR: 0.95, AM: 0.4, ST: 0.3, WL: 0.3, WR: 0.9 }, // 右后卫
      5: { GK: 0.3, DC: 0.9, DL: 0.6, DR: 0.6, DM: 1.0, MC: 0.9, ML: 0.7, MR: 0.7, AM: 0.95, ST: 0.4, WL: 0.4, WR: 0.4 }, // 后腰
      6: { GK: 0.3, DC: 0.8, DL: 0.6, DR: 0.6, DM: 0.9, MC: 1.0, ML: 0.8, MR: 0.8, AM: 0.9, ST: 0.5, WL: 0.5, WR: 0.5 }, // 中前卫
      7: { GK: 0.3, DC: 0.3, DL: 0.8, DR: 0.3, DM: 0.7, MC: 0.7, ML: 1.0, MR: 0.8, AM: 0.5, ST: 0.4, WL: 1.0, WR: 0.5 }, // 左前卫
      8: { GK: 0.3, DC: 0.3, DL: 0.3, DR: 0.8, DM: 0.7, MC: 0.7, ML: 0.8, MR: 1.0, AM: 0.5, ST: 0.4, WL: 0.5, WR: 1.0 }, // 右前卫
      9: { GK: 0.3, DC: 0.4, DL: 0.4, DR: 0.4, DM: 1.0, MC: 1.0, ML: 0.8, MR: 0.8, AM: 1.0, ST: 0.95, WL: 0.8, WR: 0.8 }, // 前腰
      10: { GK: 0.3, DC: 0.3, DL: 0.3, DR: 0.3, DM: 0.3, MC: 0.3, ML: 0.3, MR: 0.3, AM: 0.8, ST: 1.0, WL: 0.9, WR: 0.9 }, // 中锋
      11: { GK: 0.3, DC: 0.3, DL: 0.7, DR: 0.3, DM: 0.4, MC: 0.5, ML: 0.85, MR: 0.4, AM: 0.5, ST: 1.0, WL: 1.0, WR: 0.95 }, // 左边锋
      12: { GK: 0.3, DC: 0.3, DL: 0.3, DR: 0.7, DM: 0.4, MC: 0.5, ML: 0.4, MR: 0.85, AM: 0.5, ST: 1.0, WL: 0.95, WR: 1.0 } // 右边锋
    };

    // 获取球员主位置对应的ID
    const heroPositionId = positionMapping[hero.position] || 10; // 默认为中锋
    const adaptabilityConfig = positionMatchConfig[heroPositionId];

    if (!adaptabilityConfig) {
      this.logger.warn(`未找到位置${hero.position}(ID:${heroPositionId})的适应性配置，使用默认值`);
      // 返回默认适应性（所有位置50%）- 使用标准HeroPosition格式
      return {
        GK: 50, DC: 50, DL: 50, DR: 50, DM: 50, MC: 50,
        ML: 50, MR: 50, AM: 50, ST: 50, WL: 50, WR: 50
      };
    }

    // 转换为百分制并返回（字段名必须与Character服务Schema一致 - 大写格式）
    return {
      GK: Math.round(adaptabilityConfig.GK * 100),   // 门将 (严格对应old项目)
      DL: Math.round(adaptabilityConfig.DL * 100),   // 左后卫 (严格对应old项目)
      DC: Math.round(adaptabilityConfig.DC * 100),   // 中后卫 (严格对应old项目)
      DR: Math.round(adaptabilityConfig.DR * 100),   // 右后卫 (严格对应old项目)
      DM: Math.round(adaptabilityConfig.DM * 100),   // 后腰 (严格对应old项目)
      MC: Math.round(adaptabilityConfig.MC * 100),   // 中前卫 (严格对应old项目)
      ML: Math.round(adaptabilityConfig.ML * 100),   // 左前卫 (严格对应old项目)
      MR: Math.round(adaptabilityConfig.MR * 100),   // 右前卫 (严格对应old项目)
      AM: Math.round(adaptabilityConfig.AM * 100),   // 前腰 (严格对应old项目)
      ST: Math.round(adaptabilityConfig.ST * 100),   // 中锋 (严格对应old项目)
      WL: Math.round(adaptabilityConfig.WL * 100),   // 左边锋 (严格对应old项目)
      WR: Math.round(adaptabilityConfig.WR * 100)    // 右边锋 (严格对应old项目)
    };
  }

  /**
   * 创建初始球员
   * 基于old项目createInitBallerAndTeam逻辑，为新角色批量创建初始球员
   *
   * 🔄 业务流程：
   * 1. 获取CreateBaller配置表
   * 2. 筛选Mode为INIT_BALLER的配置
   * 3. 批量创建初始球员
   * 4. 验证球员数量（至少11个）
   * 5. 返回创建的球员列表
   *
   * @param characterId 角色ID
   * @param qualified 角色资质，影响球员品质
   */
  async createInitialHeroes(characterId: string, qualified: number): Promise<XResult<any[]>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`创建初始球员: ${characterId}, 资质: ${qualified}`);

      // 1. 获取CreateBaller配置表
      const createBallerConfigs = await this.gameConfig.createBaller?.getAll() || [];
      if (createBallerConfigs.length === 0) {
        this.logger.error('CreateBaller配置表为空');
        return XResultUtils.ok([]); // 返回空数组而不是错误，允许继续初始化
      }

      const createdHeroes = [];
      let heroCount = 0;

      // 2. 基于old项目逻辑：只创建Mode为INIT_BALLER的球员
      for (const config of createBallerConfigs) {
        if (config.mode === 1) { // commonEnum.CREATE_INIT_BALLER_MODE.INIT_BALLER = 1
          const heroCreateResult = await this.createSingleInitialHero(
            characterId,
            config.ballerId,
            qualified
          );

          if (XResultUtils.isSuccess(heroCreateResult)) {
            createdHeroes.push(heroCreateResult.data);
            heroCount++;
            this.logger.log(`初始球员创建成功: ${heroCreateResult.data.heroId}, ResId: ${config.ballerId}`);
          } else {
            this.logger.error(`初始球员创建失败: ResId=${config.ballerId}`, heroCreateResult.message);
          }
        }
      }

      // 3. 检查创建的球员数量（基于old项目：至少需要11个球员）
      if (heroCount < 11) {
        this.logger.warn(`初始球员数量不足: ${heroCount}/11, 配置表可能有误`);
        // 不返回错误，允许继续创建角色，但记录警告
      }

      this.logger.log(`初始球员创建完成: ${characterId}, 成功创建: ${heroCount}个`);
      return XResultUtils.ok(createdHeroes);
    }, {
      reason: "create_initial_heroes",
      metadata: { characterId, qualified, expectedCount: 11 }
    });
  }

  /**
   * 创建初始球员（支持事务）
   * 基于old项目createInitBallerAndTeam逻辑，为新角色批量创建初始球员
   * 🎯 性能优化：使用批量插入替代循环创建，显著减少数据库操作时间
   *
   * 🔄 业务流程：
   * 1. 获取CreateBaller配置表
   * 2. 筛选Mode为INIT_BALLER的配置
   * 3. 批量准备球员数据
   * 4. 一次性批量插入数据库
   * 5. 验证球员数量（至少11个）
   * 6. 返回创建的球员列表
   *
   * @param characterId 角色ID
   * @param qualified 角色资质，影响球员品质
   * @param session 数据库事务session
   */
  async createInitialHeroesWithSession(characterId: string, qualified: number, session: any): Promise<XResult<any[]>> {
    this.logger.log(`创建初始球员(事务): ${characterId}, 资质: ${qualified}`);

    // 1. 获取CreateBaller配置表
    const createBallerConfigs = await this.gameConfig.createBaller?.getAll() || [];
    if (createBallerConfigs.length === 0) {
      this.logger.error('CreateBaller配置表为空');
      return XResultUtils.ok([]); // 返回空数组而不是错误，允许继续初始化
    }

    // 2. 筛选Mode为INIT_BALLER的配置并准备批量数据
    const heroDataList: any[] = [];
    const initBallerConfigs = createBallerConfigs.filter(config => config.mode === 1);

    this.logger.log(`准备创建 ${initBallerConfigs.length} 个初始球员`);

    // 3. 批量准备球员数据（避免循环数据库操作）
    for (const config of initBallerConfigs) {
      try {
        // 获取球员配置
        const heroDefinition = await this.gameConfig.hero.get(config.ballerId);
        if (!heroDefinition) {
          this.logger.warn(`球员配置不存在，跳过: ResId=${config.ballerId}`);
          continue;
        }

        // 根据资质确定球员品质
        const quality = this.calculateHeroQualityByQualified(qualified);

        // 生成球员ID
        const heroId = this.generateHeroId();

        // 根据配置表ID和品质初始化属性
        const initialAttributes = this.generateInitialAttributes(config.ballerId, quality);

        // 🔧 从HeroDefinition获取初始技能
        const initialSkills = this.initializeHeroSkills(heroDefinition?.skillList || []);

        // 准备球员数据
        const heroData = {
          heroId,
          characterId,
          resId: config.ballerId,
          name: heroDefinition.cnName || heroDefinition.name,
          position: this.convertPositionFromConfig(heroDefinition.position1),
          quality,
          level: 1,
          nationality: '',
          club: '',
          attributes: initialAttributes,
          baseAttributes: { ...initialAttributes },
          skills: initialSkills.skills,           // 技能对象数组
          activeSkills: initialSkills.activeSkills, // 激活的技能ID数组
          obtainTime: Date.now(),
          obtainType: 1, // 初始获得
          energy: 100,
          morale: 100,
          marketValue: this.calculateMarketValue(quality, 1),
          isNewer: 1, // 新手球员标记
        };

        heroDataList.push(heroData);
      } catch (error) {
        this.logger.error(`准备球员数据失败: ResId=${config.ballerId}`, error);
        // 继续处理其他球员，不中断整个流程
      }
    }

    // 4. 检查准备的球员数量
    if (heroDataList.length < 11) {
      this.logger.error(`准备的初始球员数量不足: ${heroDataList.length}/11`);
      return XResultUtils.error(`初始球员数量不足: ${heroDataList.length}/11`, 'INSUFFICIENT_INITIAL_HEROES');
    }

    // 5. 批量插入数据库（一次性操作，大幅提升性能）
    const batchCreateResult = await this.heroRepository.createMany(heroDataList, session);
    if (XResultUtils.isFailure(batchCreateResult)) {
      this.logger.error(`批量创建初始球员失败: ${batchCreateResult.message}`);
      return XResultUtils.error(`批量创建初始球员失败: ${batchCreateResult.message}`, batchCreateResult.code);
    }

    const createdHeroes = batchCreateResult.data || [];
    this.logger.log(`初始球员批量创建完成(事务): ${characterId}, 成功创建: ${createdHeroes.length}个`);

    return XResultUtils.ok(createdHeroes);
  }

  /**
   * 创建单个初始球员
   * 基于old项目addHeroNewer方法
   *
   * @param characterId 角色ID
   * @param resId 球员配置ID
   * @param qualified 角色资质
   */
  private async createSingleInitialHero(characterId: string, resId: number, qualified: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 获取球员配置
      const heroDefinition = await this.gameConfig.hero.get(resId);
      if (!heroDefinition) {
        return XResultUtils.error(`球员配置不存在: ResId=${resId}`, 'HERO_CONFIG_NOT_FOUND');
      }

      // 根据资质确定球员品质
      const quality = this.calculateHeroQualityByQualified(qualified);

      // 使用createHero方法创建球员
      const createDto: CreatePayloadDto = {
        characterId,
        resId,
        source: 'initial_creation',
        name: heroDefinition.cnName || heroDefinition.name, // 优先使用中文名
        position: this.convertPositionFromConfig(heroDefinition.position1), // 转换位置
        quality,
        level: 1,
        nationality: '', // HeroDefinition中没有nationality字段，使用空字符串
        club: '',
        isNewer: 1, // 新手球员标记
        obtainType: 1, // 初始获得
      };

      const result = await this.createHero(createDto);

      if (XResultUtils.isSuccess(result)) {
        return XResultUtils.ok(result.data);
      } else {
        this.logger.error(`创建初始球员失败: ResId=${resId}, ${result.message}`);
        return XResultUtils.error(`创建初始球员失败: ${result.message}`, result.code);
      }
    }, {
      reason: "create_single_initial_hero",
      metadata: { characterId, resId, qualified }
    });
  }

  /**
   * 创建单个初始球员（支持事务）
   * 基于old项目addHeroNewer方法
   *
   * @param characterId 角色ID
   * @param resId 球员配置ID
   * @param qualified 角色资质
   * @param session 数据库事务session
   */
  private async createSingleInitialHeroWithSession(characterId: string, resId: number, qualified: number, session: any): Promise<XResult<any>> {
    // 获取球员配置
    const heroDefinition = await this.gameConfig.hero.get(resId);
    if (!heroDefinition) {
      return XResultUtils.error(`球员配置不存在: ResId=${resId}`, 'HERO_CONFIG_NOT_FOUND');
    }

    // 根据资质确定球员品质
    const quality = this.calculateHeroQualityByQualified(qualified);

    // 使用createHeroWithSession方法创建球员
    const createDto: CreatePayloadDto = {
      characterId,
      resId,
      source: 'initial_creation',
      name: heroDefinition.cnName || heroDefinition.name, // 优先使用中文名
      position: this.convertPositionFromConfig(heroDefinition.position1), // 转换位置
      quality,
      level: 1,
      nationality: '', // HeroDefinition中没有nationality字段，使用空字符串
      club: '',
      isNewer: 1, // 新手球员标记
      obtainType: 1, // 初始获得
    };

    const result = await this.createHeroWithSession(createDto, session);

    if (XResultUtils.isSuccess(result)) {
      return XResultUtils.ok(result.data);
    } else {
      this.logger.error(`创建初始球员失败(事务): ResId=${resId}, ${result.message}`);
      return XResultUtils.error(`创建初始球员失败: ${result.message}`, result.code);
    }
  }

  /**
   * 根据角色资质计算球员品质
   * 基于old项目逻辑
   *
   * @param qualified 角色资质 (1-100)
   * @returns 球员品质 (1-6)
   */
  private calculateHeroQualityByQualified(qualified: number): HeroQuality {
    // 基于old项目的品质计算逻辑
    if (qualified >= 90) return HeroQuality.RED;     // 红色 (6)
    if (qualified >= 80) return HeroQuality.ORANGE;  // 橙色 (5)
    if (qualified >= 70) return HeroQuality.PURPLE;  // 紫色 (4)
    if (qualified >= 60) return HeroQuality.BLUE;    // 蓝色 (3)
    if (qualified >= 50) return HeroQuality.GREEN;   // 绿色 (2)
    return HeroQuality.WHITE;                        // 白色 (1)
  }

  /**
   * 将配置表中的位置转换为HeroPosition枚举
   * 基于HeroDefinition.position1字段
   *
   * @param configPosition 配置表中的位置值
   * @returns HeroPosition枚举值
   */
  private convertPositionFromConfig(configPosition: string | number): HeroPosition {
    // 如果是字符串，直接映射
    if (typeof configPosition === 'string') {
      const positionMap: Record<string, HeroPosition> = {
        'GK': HeroPosition.GK,
        'DC': HeroPosition.DC,
        'DL': HeroPosition.DL,
        'DR': HeroPosition.DR,
        'DM': HeroPosition.DM,
        'MC': HeroPosition.MC,
        'ML': HeroPosition.ML,
        'MR': HeroPosition.MR,
        'AM': HeroPosition.AM,
        'ST': HeroPosition.ST,
        'WL': HeroPosition.WL,
        'WR': HeroPosition.WR,
      };
      return positionMap[configPosition] || HeroPosition.MC; // 默认中场
    }

    // 如果是数字，按索引映射
    const indexMap: Record<number, HeroPosition> = {
      0: HeroPosition.GK,   // 门将
      1: HeroPosition.DC,   // 中后卫
      2: HeroPosition.DL,   // 左后卫
      3: HeroPosition.DR,   // 右后卫
      4: HeroPosition.MC,   // 中场
      5: HeroPosition.ML,   // 左中场
      6: HeroPosition.MR,   // 右中场
      7: HeroPosition.WL,   // 左边锋
      8: HeroPosition.WR,   // 右边锋
      9: HeroPosition.ST,   // 前锋
      10: HeroPosition.AM,  // 前腰
      11: HeroPosition.DM   // 后腰
    };
    return indexMap[configPosition] || HeroPosition.MC; // 默认中场
  }

  /**
   * 根据配置表ID和品质生成初始属性
   * 修复：返回完整的AttributeDetail对象结构，而不是简单数字值
   */
  private generateInitialAttributes(resId: number, quality: HeroQuality): any {
    // 基础属性值根据品质决定
    const baseValue = {
      [HeroQuality.WHITE]: 30,
      [HeroQuality.GREEN]: 40,
      [HeroQuality.BLUE]: 50,
      [HeroQuality.PURPLE]: 60,
      [HeroQuality.ORANGE]: 70,
      [HeroQuality.RED]: 80,
    }[quality] || 30;

    // 随机浮动 ±5
    const randomVariation = () => baseValue + Math.floor(Math.random() * 11) - 5;

    // 创建AttributeDetail对象的辅助函数
    const createAttributeDetail = (value: number) => ({
      cur: value,           // 当前值 (最终计算值)
      base: value,          // 基础值 (培养值) - 初始时与cur相同
      skill: 0,             // 技能附加值
      qualificate: 0,       // 特训加成
      upgradeStar: 0,       // 升星加成
      handbook: 0,          // 图鉴加成
      skillAct: 0,          // 技能加成系数
      trainer: 0,           // 教练属性加成
      groundTrain: 0,       // 球场训练加成
      attackTactics: 0,     // 进攻战术加成
      defTactics: 0,        // 防守战术加成
      trainerSkillAtt: 0,   // 教练技能加成
      beliefSkillAtt: 0,    // 信仰技能加成
    });

    return {
      speed: createAttributeDetail(randomVariation()),
      jumping: createAttributeDetail(randomVariation()),
      strength: createAttributeDetail(randomVariation()),
      stamina: createAttributeDetail(randomVariation()),
      explosiveForce: createAttributeDetail(randomVariation()),
      finishing: createAttributeDetail(randomVariation()),
      dribbling: createAttributeDetail(randomVariation()),
      passing: createAttributeDetail(randomVariation()),
      longPassing: createAttributeDetail(randomVariation()),
      longShots: createAttributeDetail(randomVariation()),
      heading: createAttributeDetail(randomVariation()),
      volleys: createAttributeDetail(randomVariation()),
      standingTackle: createAttributeDetail(randomVariation()),
      slidingTackle: createAttributeDetail(randomVariation()),
      penalties: createAttributeDetail(randomVariation()),
      cornerKick: createAttributeDetail(randomVariation()),
      freeKick: createAttributeDetail(randomVariation()),
      attack: createAttributeDetail(randomVariation()),
      save: createAttributeDetail(randomVariation()),
      resistanceDamage: createAttributeDetail(randomVariation())
    };
  }

  /**
   * 计算市场价值
   */
  private calculateMarketValue(quality: HeroQuality, level: number): number {
    const baseValue = {
      [HeroQuality.WHITE]: 1000,
      [HeroQuality.GREEN]: 5000,
      [HeroQuality.BLUE]: 20000,
      [HeroQuality.PURPLE]: 50000,
      [HeroQuality.ORANGE]: 100000,
      [HeroQuality.RED]: 200000,
    }[quality] || 1000;

    return baseValue * (1 + level * 0.1);
  }

  /**
   * 计算属性提升
   */
  private calculateAttributeIncrease(quality: HeroQuality, count: number): number {
    const baseIncrease = {
      [HeroQuality.WHITE]: 1,
      [HeroQuality.GREEN]: 1,
      [HeroQuality.BLUE]: 2,
      [HeroQuality.PURPLE]: 2,
      [HeroQuality.ORANGE]: 3,
      [HeroQuality.RED]: 3,
    }[quality] || 1;

    return baseIncrease * count;
  }

  /**
   * 计算升星属性加成
   */
  private calculateEvolutionBonus(quality: HeroQuality, star: number): any {
    const bonus = star * 2; // 每星+2属性

    return {
      speed: bonus, jumping: bonus, strength: bonus, stamina: bonus, explosiveForce: bonus,
      finishing: bonus, dribbling: bonus, passing: bonus, longPassing: bonus, longShots: bonus,
      heading: bonus, volleys: bonus, standingTackle: bonus, slidingTackle: bonus,
      penalties: bonus, cornerKick: bonus, freeKick: bonus, attack: bonus, save: bonus, resistanceDamage: bonus
    };
  }



  /**
   * 转换为球员配置DTO
   * 现在使用GameConfigFacade返回的数据而不是废弃的HeroDefinitionDocument
   */
  private toHeroConfigDto(heroDefinition: any): any {
    return {
      heroId: heroDefinition.heroId,
      modeId: heroDefinition.modeId,
      name: heroDefinition.name,
      cnName: heroDefinition.cnName,
      enName: heroDefinition.enName,
      position: heroDefinition.position,
      quality: heroDefinition.quality,
      rating: heroDefinition.rating,
      avatar: heroDefinition.avatar,
      faceIcon: heroDefinition.faceIcon,
      nationality: heroDefinition.nationality,
      club: heroDefinition.club,
      age: heroDefinition.age,
      height: heroDefinition.height,
      weight: heroDefinition.weight,
      baseAttributes: heroDefinition.baseAttributes,
      growthConfig: heroDefinition.growthConfig,
      defaultSkills: heroDefinition.defaultSkills,
      learnableSkills: heroDefinition.learnableSkills,
      obtainConfig: heroDefinition.obtainConfig,
      isActive: heroDefinition.isActive,
      isLimited: heroDefinition.isLimited,
      limitEndTime: heroDefinition.limitEndTime,
      isExpired: heroDefinition.isExpired,
      isAvailable: heroDefinition.isAvailable,
      isGoalkeeper: heroDefinition.isGoalkeeper,
      totalAttributes: heroDefinition.totalAttributes,
    };
  }

  /**
   * 转换为DTO
   */
  private toHeroInfoDto(hero: HeroDocument): HeroInfoDto {
    // 计算球员位置适应性
    const adaptability = this.calculatePositionAdaptability(hero);

    return {
      heroId: hero.heroId,
      uid: hero.heroId, // 添加uid字段，与Character服务兼容
      characterId: hero.characterId,
      resId: hero.resId,
      name: hero.name,
      position: hero.position,
      quality: hero.quality,
      level: hero.level,
      exp: hero.exp,
      avatar: hero.avatar,
      nationality: hero.nationality,
      club: hero.club,
      attributes: hero.attributes,
      skills: hero.skills,
      activeSkills: hero.activeSkills, // 🔧 添加激活技能ID列表，战斗系统需要
      evolution: hero.evolution,
      overallRating: hero.totalPower,
      isLocked: hero.isLocked,
      isInFormation: hero.isInLineup,
      energy: hero.energy,
      morale: hero.morale,
      equipments: hero.equipments,
      contractDays: hero.contractDays,
      salary: hero.salary,
      marketValue: hero.marketValue,
      isOnMarket: hero.isOnMarket,
      adaptability, // 添加适应性字段
      stats: {
        matchesPlayed: hero.matchesPlayed,
        goals: hero.goals,
        assists: hero.assists,
        yellowCards: hero.yellowCards,
        redCards: hero.redCards,
      },
      obtainTime: hero.obtainTime,
      canTrain: hero.canTrain,
      canEvolve: hero.canEvolve,
    };
  }

  // ==================== 升星系统辅助方法 ====================

  /**
   * 计算升星金币消耗
   */
  private calculateEvolutionGoldCost(currentStar: number, useProtection: boolean): number {
    // 基础消耗随星级递增
    const baseCost = 1000 * Math.pow(2, currentStar);

    // 使用保护道具需要额外消耗
    const protectionMultiplier = useProtection ? 2 : 1;

    return baseCost * protectionMultiplier;
  }

  /**
   * 计算降星概率
   */
  private calculateDegradeRate(currentStar: number): number {
    // 星级越高，失败时降星概率越高
    if (currentStar <= 3) return 0; // 3星以下不降星
    if (currentStar <= 6) return 20; // 4-6星失败时20%概率降星
    return 30; // 7-9星失败时30%概率降星
  }

  /**
   * 计算升星属性加成
   */
  private calculateEvolutionAttributeBonus(hero: any, newStar: number): any {
    const oldStar = hero.evolution.star;
    const starDiff = newStar - oldStar;

    if (starDiff === 0) {
      // 星级没有变化，无属性加成
      return {
        speed: 0, jumping: 0, strength: 0, stamina: 0, explosiveForce: 0,
        finishing: 0, dribbling: 0, passing: 0, longPassing: 0, longShots: 0,
        heading: 0, volleys: 0, standingTackle: 0, slidingTackle: 0,
        penalties: 0, cornerKick: 0, freeKick: 0, attack: 0, save: 0, resistanceDamage: 0
      };
    }

    // 每升一星，所有属性增加基础值的4%
    const bonusRate = 0.04 * starDiff;

    return {
      speed: Math.round(hero.attributes.speed.cur * bonusRate),
      jumping: Math.round(hero.attributes.jumping.cur * bonusRate),
      strength: Math.round(hero.attributes.strength.cur * bonusRate),
      stamina: Math.round(hero.attributes.stamina.cur * bonusRate),
      explosiveForce: Math.round(hero.attributes.explosiveForce.cur * bonusRate),
      finishing: Math.round(hero.attributes.finishing.cur * bonusRate),
      dribbling: Math.round(hero.attributes.dribbling.cur * bonusRate),
      passing: Math.round(hero.attributes.passing.cur * bonusRate),
      longPassing: Math.round(hero.attributes.longPassing.cur * bonusRate),
      longShots: Math.round(hero.attributes.longShots.cur * bonusRate),
      heading: Math.round(hero.attributes.heading.cur * bonusRate),
      volleys: Math.round(hero.attributes.volleys.cur * bonusRate),
      standingTackle: Math.round(hero.attributes.standingTackle.cur * bonusRate),
      slidingTackle: Math.round(hero.attributes.slidingTackle.cur * bonusRate),
      penalties: Math.round(hero.attributes.penalties.cur * bonusRate),
      cornerKick: Math.round(hero.attributes.cornerKick.cur * bonusRate),
      freeKick: Math.round(hero.attributes.freeKick.cur * bonusRate),
      attack: Math.round(hero.attributes.attack.cur * bonusRate),
      save: Math.round(hero.attributes.save.cur * bonusRate),
      resistanceDamage: Math.round(hero.attributes.resistanceDamage.cur * bonusRate)
    };
  }

  /**
   * 获取升星所需材料信息
   * Controller层已验证参数，无需重复验证
   */
  async getEvolutionRequirements(heroId: string): Promise<XResult<any>> {
    try {
      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      const currentStar = hero.evolution.star;

      const requirements = {
        heroId,
        currentStar,
        maxStar: 9,
        canEvolve: hero.canEvolve,
        successRate: hero.evolutionSuccessRate,
        requiredMaterialCount: hero.evolutionMaterialCount,
        goldCost: this.calculateEvolutionGoldCost(currentStar, false),
        goldCostWithProtection: this.calculateEvolutionGoldCost(currentStar, true),
        degradeRate: this.calculateDegradeRate(currentStar),
        nextStarBonus: this.calculateEvolutionAttributeBonus(hero, currentStar + 1),
      };

      return XResultUtils.ok(requirements);
    } catch (error) {
      this.logger.error('获取升星需求失败', error);
      return XResultUtils.error('获取升星需求失败', 'GET_EVOLUTION_REQUIREMENTS_ERROR');
    }
  }

  // ==================== 训练系统辅助方法 ====================

  /**
   * 执行通用训练（初级、中级、高级）
   * 适配Result模式，提供完整的错误处理和业务逻辑验证
   */
  private async executeGeneralTraining(hero: any, trainDto: any, count: number): Promise<XResult<any>> {
    try {
      const attributeChanges = {};
      const updateData = {};
      let goldCost = 0;
      let itemCost = [];
      let currentStage = 1;
      let reachedStageLimit = false;

      // 根据训练类型确定影响的属性
      const affectedAttributes = this.getAffectedAttributesByTrainingType(trainDto.trainingType);

      // 验证球员训练数据完整性
      if (!hero.training) {
        return XResultUtils.error('球员训练数据不完整', 'HERO_TRAINING_DATA_INCOMPLETE');
      }

      for (const attribute of affectedAttributes) {
        const stageInfo = hero.training[`${attribute}Stage`];
        if (!stageInfo) {
          return XResultUtils.error(`球员${attribute}训练阶段数据缺失`, 'HERO_TRAINING_STAGE_DATA_MISSING');
        }

        const currentAttributeValue = hero.attributes[attribute];
        if (currentAttributeValue === undefined || currentAttributeValue === null) {
          return XResultUtils.error(`球员${attribute}属性数据缺失`, 'HERO_ATTRIBUTE_DATA_MISSING');
        }

        // 检查属性是否已达到上限
        if (currentAttributeValue >= 100) {
          this.logger.debug(`球员${attribute}属性已达上限，跳过训练`);
          continue;
        }

        // 计算训练效果
        const trainingEffect = this.calculateTrainingEffect(
          trainDto.trainingType,
          stageInfo.stage,
          hero.quality,
          count
        );

        // 计算消耗
        const cost = this.calculateTrainingCost(trainDto.trainingType, trainDto.trainingMethod, stageInfo.stage, count);
        goldCost += cost.gold;
        itemCost = itemCost.concat(cost.items);

        // 更新属性值
        const newAttributeValue = Math.min(currentAttributeValue + trainingEffect.attributeIncrease, 100);

        attributeChanges[attribute] = {
          oldValue: currentAttributeValue,
          newValue: newAttributeValue,
          increase: trainingEffect.attributeIncrease,
        };

        // 更新阶段进度
        const newStageProgress = stageInfo.stageProgress + trainingEffect.stageProgress;
        const newStage = this.calculateNewStage(stageInfo.stage, newStageProgress);

        updateData[`attributes.${attribute}.cur`] = newAttributeValue;
        updateData[`training.${attribute}Stage.stage`] = newStage.stage;
        updateData[`training.${attribute}Stage.stageProgress`] = newStage.progress;
        updateData[`training.${attribute}Stage.totalTrainingCount`] = stageInfo.totalTrainingCount + count;

        currentStage = Math.max(currentStage, newStage.stage);
        if (newStage.stage >= 9) {
          reachedStageLimit = true;
        }
      }

      const trainingResult = {
        attributeChanges,
        updateData,
        goldCost,
        itemCost,
        currentStage,
        reachedStageLimit,
      };

      return XResultUtils.ok(trainingResult);
    } catch (error) {
      this.logger.error('执行通用训练失败', error);
      return XResultUtils.error('执行通用训练失败', 'EXECUTE_GENERAL_TRAINING_ERROR');
    }
  }

  /**
   * 执行定向训练（针对特定属性）
   * 适配Result模式，提供完整的错误处理和业务逻辑验证
   */
  private async executeTargetedTraining(hero: any, trainDto: any, count: number): Promise<XResult<any>> {
    try {
      const attributeChanges = {};
      const updateData = {};
      let goldCost = 0;
      let itemCost = [];
      let currentStage = 1;
      let reachedStageLimit = false;

      // 定向训练只影响指定的属性
      const targetAttributes = trainDto.targetAttributes || ['speed']; // 默认训练速度

      // 验证目标属性有效性 - 适配新的hero.schema.ts的20个完整属性
      const validAttributes = [
        'speed', 'jumping', 'strength', 'stamina', 'explosiveForce',
        'finishing', 'dribbling', 'passing', 'longPassing', 'longShots',
        'heading', 'volleys', 'standingTackle', 'slidingTackle',
        'penalties', 'cornerKick', 'freeKick', 'attack', 'save', 'resistanceDamage'
      ];
      for (const attribute of targetAttributes) {
        if (!validAttributes.includes(attribute)) {
          return XResultUtils.error(`无效的训练属性: ${attribute}`, 'INVALID_TRAINING_ATTRIBUTE');
        }
      }

      // 验证球员训练数据完整性
      if (!hero.training) {
        return XResultUtils.error('球员训练数据不完整', 'HERO_TRAINING_DATA_INCOMPLETE');
      }

      for (const attribute of targetAttributes) {
        const stageInfo = hero.training[`${attribute}Stage`];
        if (!stageInfo) {
          return XResultUtils.error(`球员${attribute}训练阶段数据缺失`, 'HERO_TRAINING_STAGE_DATA_MISSING');
        }

        const currentAttributeValue = hero.attributes[attribute];
        if (currentAttributeValue === undefined || currentAttributeValue === null) {
          return XResultUtils.error(`球员${attribute}属性数据缺失`, 'HERO_ATTRIBUTE_DATA_MISSING');
        }

        // 检查属性是否已达到上限
        if (currentAttributeValue >= 100) {
          this.logger.debug(`球员${attribute}属性已达上限，跳过训练`);
          continue;
        }

        // 定向训练效果更强
        const trainingEffect = this.calculateTargetedTrainingEffect(
          stageInfo.stage,
          hero.quality,
          count
        );

        // 定向训练消耗更高
        const cost = this.calculateTargetedTrainingCost(trainDto.trainingMethod, stageInfo.stage, count);
        goldCost += cost.gold;
        itemCost = itemCost.concat(cost.items);

        // 更新属性值
        const newAttributeValue = Math.min(currentAttributeValue + trainingEffect.attributeIncrease, 100);

        attributeChanges[attribute] = {
          oldValue: currentAttributeValue,
          newValue: newAttributeValue,
          increase: trainingEffect.attributeIncrease,
        };

        // 更新阶段进度
        const newStageProgress = stageInfo.stageProgress + trainingEffect.stageProgress;
        const newStage = this.calculateNewStage(stageInfo.stage, newStageProgress);

        updateData[`attributes.${attribute}`] = newAttributeValue;
        updateData[`training.${attribute}Stage.stage`] = newStage.stage;
        updateData[`training.${attribute}Stage.stageProgress`] = newStage.progress;
        updateData[`training.${attribute}Stage.totalTrainingCount`] = stageInfo.totalTrainingCount + count;

        currentStage = Math.max(currentStage, newStage.stage);
        if (newStage.stage >= 9) {
          reachedStageLimit = true;
        }
      }

      const trainingResult = {
        attributeChanges,
        updateData,
        goldCost,
        itemCost,
        currentStage,
        reachedStageLimit,
      };

      return XResultUtils.ok(trainingResult);
    } catch (error) {
      this.logger.error('执行定向训练失败', error);
      return XResultUtils.error('执行定向训练失败', 'EXECUTE_TARGETED_TRAINING_ERROR');
    }
  }

  /**
   * 根据训练类型获取影响的属性
   */
  private getAffectedAttributesByTrainingType(trainingType: number): string[] {
    switch (trainingType) {
      case 1: // 初级训练 - 全属性小幅提升
        return ['speed', 'finishing', 'passing', 'standingTackle', 'dribbling', 'strength'];
      case 2: // 中级训练 - 主要属性中幅提升
        return ['speed', 'finishing', 'passing', 'standingTackle'];
      case 3: // 高级训练 - 核心属性大幅提升
        return ['finishing', 'passing'];
      default:
        return ['speed'];
    }
  }

  /**
   * 计算训练效果
   */
  private calculateTrainingEffect(trainingType: number, stage: number, quality: number, count: number): any {
    // 基础效果根据训练类型和阶段计算
    const baseEffect = this.getBaseTrainingEffect(trainingType, stage);

    // 品质加成
    const qualityMultiplier = 1 + (quality - 1) * 0.1;

    // 次数加成
    const countMultiplier = count;

    const attributeIncrease = Math.round(baseEffect.attribute * qualityMultiplier * countMultiplier);
    const stageProgress = Math.round(baseEffect.progress * countMultiplier);

    return {
      attributeIncrease: Math.min(attributeIncrease, 10), // 单次最多增加10点
      stageProgress: Math.min(stageProgress, 100), // 单次最多增加100进度
    };
  }

  /**
   * 获取基础训练效果
   */
  private getBaseTrainingEffect(trainingType: number, stage: number): any {
    const stageMultiplier = Math.max(1, 10 - stage); // 阶段越高，效果递减

    switch (trainingType) {
      case 1: // 初级训练
        return {
          attribute: 1 * stageMultiplier,
          progress: 10 * stageMultiplier,
        };
      case 2: // 中级训练
        return {
          attribute: 2 * stageMultiplier,
          progress: 15 * stageMultiplier,
        };
      case 3: // 高级训练
        return {
          attribute: 3 * stageMultiplier,
          progress: 20 * stageMultiplier,
        };
      default:
        return { attribute: 1, progress: 10 };
    }
  }

  /**
   * 计算定向训练效果
   */
  private calculateTargetedTrainingEffect(stage: number, quality: number, count: number): any {
    // 定向训练效果是高级训练的1.5倍
    const baseEffect = this.getBaseTrainingEffect(3, stage);
    const qualityMultiplier = 1 + (quality - 1) * 0.1;
    const countMultiplier = count;
    const targetedMultiplier = 1.5;

    const attributeIncrease = Math.round(baseEffect.attribute * qualityMultiplier * countMultiplier * targetedMultiplier);
    const stageProgress = Math.round(baseEffect.progress * countMultiplier * targetedMultiplier);

    return {
      attributeIncrease: Math.min(attributeIncrease, 15), // 定向训练单次最多增加15点
      stageProgress: Math.min(stageProgress, 150), // 定向训练单次最多增加150进度
    };
  }

  /**
   * 计算训练消耗
   */
  private calculateTrainingCost(trainingType: number, trainingMethod: number, stage: number, count: number): any {
    const baseCost = this.getBaseTrainingCost(trainingType, stage);
    const countMultiplier = count;
    const stageMultiplier = Math.pow(1.2, stage - 1); // 阶段越高，消耗越大

    if (trainingMethod === 1) { // 使用金币
      return {
        gold: Math.round(baseCost.gold * countMultiplier * stageMultiplier),
        items: [],
      };
    } else { // 使用道具
      return {
        gold: 0,
        items: baseCost.items.map(item => ({
          ...item,
          quantity: item.quantity * countMultiplier,
        })),
      };
    }
  }

  /**
   * 获取基础训练消耗
   */
  private getBaseTrainingCost(trainingType: number, stage: number): any {
    switch (trainingType) {
      case 1: // 初级训练
        return {
          gold: 1000,
          items: [{ itemId: 'training_item_1', quantity: 1 }],
        };
      case 2: // 中级训练
        return {
          gold: 2000,
          items: [{ itemId: 'training_item_2', quantity: 1 }],
        };
      case 3: // 高级训练
        return {
          gold: 5000,
          items: [{ itemId: 'training_item_3', quantity: 1 }],
        };
      default:
        return { gold: 1000, items: [] };
    }
  }

  /**
   * 计算定向训练消耗
   */
  private calculateTargetedTrainingCost(trainingMethod: number, stage: number, count: number): any {
    const baseCost = this.getBaseTrainingCost(3, stage); // 基于高级训练
    const countMultiplier = count;
    const stageMultiplier = Math.pow(1.3, stage - 1); // 定向训练消耗更高
    const targetedMultiplier = 2; // 定向训练消耗是高级训练的2倍

    if (trainingMethod === 1) { // 使用金币
      return {
        gold: Math.round(baseCost.gold * countMultiplier * stageMultiplier * targetedMultiplier),
        items: [],
      };
    } else { // 使用道具
      return {
        gold: 0,
        items: [{ itemId: 'targeted_training_item', quantity: count }],
      };
    }
  }

  /**
   * 计算新的阶段和进度
   */
  private calculateNewStage(currentStage: number, newProgress: number): any {
    let stage = currentStage;
    let progress = newProgress;

    // 每100进度升一阶段
    while (progress >= 100 && stage < 9) {
      progress -= 100;
      stage++;
    }

    // 达到最高阶段时，进度不再增加
    if (stage >= 9) {
      progress = 0;
    }

    return { stage, progress };
  }

  /**
   * 计算训练冷却时间
   */
  private calculateTrainingCooldown(trainingType: number): number {
    switch (trainingType) {
      case 1: // 初级训练 - 10分钟
        return 10 * 60 * 1000;
      case 2: // 中级训练 - 20分钟
        return 20 * 60 * 1000;
      case 3: // 高级训练 - 30分钟
        return 30 * 60 * 1000;
      case 4: // 定向训练 - 60分钟
        return 60 * 60 * 1000;
      default:
        return 30 * 60 * 1000;
    }
  }

  // ==================== 球员状态管理系统 ====================

  /**
   * 增加球员疲劳值
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async addHeroFatigue(heroId: string, fatigueValue: number): Promise<XResult<void>> {
    return this.executeBusinessOperation(async () => {
      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      const newFatigue = Math.min(hero.fatigue + fatigueValue, 100);
      const newFatigueRatio = this.calculateFatigueRatio(newFatigue);

      const updateResult = await this.heroRepository.update(heroId, {
        fatigue: newFatigue,
        fatigueRatio: newFatigueRatio,
      });

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员疲劳值失败: ${updateResult.message}`, updateResult.code);
      }

      this.logger.log(`球员疲劳增加: ${heroId}, 疲劳值: ${hero.fatigue} -> ${newFatigue}`);
      return XResultUtils.ok(undefined);
    }, { reason: 'add_hero_fatigue', metadata: { heroId, fatigueValue } });
  }

  /**
   * 恢复球员疲劳值
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async recoverHeroFatigue(heroId: string, recoveryValue?: number): Promise<XResult<void>> {
    return this.executeBusinessOperation(async () => {
      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      // 如果没有指定恢复值，则完全恢复
      const recovery = recoveryValue !== undefined ? recoveryValue : hero.fatigue;
      const newFatigue = Math.max(hero.fatigue - recovery, 0);
      const newFatigueRatio = this.calculateFatigueRatio(newFatigue);

      const updateResult = await this.heroRepository.update(heroId, {
        fatigue: newFatigue,
        fatigueRatio: newFatigueRatio,
        reTimeFatigue: newFatigue > 0 ? Date.now() + (newFatigue * 60 * 1000) : 0, // 每点疲劳需要1分钟恢复
      });

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员疲劳值失败: ${updateResult.message}`, updateResult.code);
      }

      this.logger.log(`球员疲劳恢复: ${heroId}, 疲劳值: ${hero.fatigue} -> ${newFatigue}`);
      return XResultUtils.ok(undefined);
    }, { reason: 'recover_hero_fatigue', metadata: { heroId, recoveryValue } });
  }

  /**
   * 治疗球员
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async treatHero(heroId: string, treatmentType: 'quick' | 'normal' | 'full' = 'normal'): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      if (hero.isTreat) {
        return XResultUtils.error('球员已在治疗中', 'HERO_IN_TREATMENT');
      }

      const treatmentInfo = this.calculateTreatmentInfo(treatmentType, hero.fatigue);

      const updateResult = await this.heroRepository.update(heroId, {
        isTreat: true,
        fatigue: 0,
        fatigueRatio: 1.0,
        reTimeFatigue: Date.now() + treatmentInfo.duration,
      });

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员治疗状态失败: ${updateResult.message}`, updateResult.code);
      }

      // 设置治疗完成的定时任务
      setTimeout(async () => {
        const completeResult = await this.heroRepository.update(heroId, {
          isTreat: false,
          reTimeFatigue: 0,
        });
        if (XResultUtils.isSuccess(completeResult)) {
          this.logger.log(`球员治疗完成: ${heroId}`);
        }
      }, treatmentInfo.duration);

      this.logger.log(`球员开始治疗: ${heroId}, 类型: ${treatmentType}, 持续时间: ${treatmentInfo.duration}ms`);

      return XResultUtils.ok({
        heroId,
        treatmentType,
        duration: treatmentInfo.duration,
        cost: treatmentInfo.cost,
        completionTime: Date.now() + treatmentInfo.duration,
      });
    }, { reason: 'treat_hero', metadata: { heroId, treatmentType } });
  }

  /**
   * 续约球员
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async renewHeroContract(heroId: string, contractDays: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      // 检查是否可以续约
      if (hero.contractDays > 30) {
        return XResultUtils.error('合约尚未到期，无法续约', 'CONTRACT_NOT_EXPIRED');
      }

      const renewalCost = this.calculateRenewalCost(hero.level, hero.quality, contractDays);
      const newContractDays = hero.contractDays + contractDays;
      const newLifeNum = hero.lifeNum + 1;

      const updateResult = await this.heroRepository.update(heroId, {
        contractDays: newContractDays,
        lifeNum: newLifeNum,
        treatyReTime: Date.now(),
      });

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员合约信息失败: ${updateResult.message}`, updateResult.code);
      }

      this.logger.log(`球员续约成功: ${heroId}, 新合约天数: ${newContractDays}, 生涯次数: ${newLifeNum}`);

      return XResultUtils.ok({
        heroId,
        oldContractDays: hero.contractDays,
        newContractDays,
        renewalDays: contractDays,
        cost: renewalCost,
        lifeNum: newLifeNum,
      });
    }, { reason: 'renew_hero_contract', metadata: { heroId, contractDays } });
  }

  /**
   * 球员退役检查
   * Controller层已验证参数，无需重复验证
   */
  async checkHeroRetirement(heroId: string): Promise<XResult<boolean>> {
    const heroResult = await this.heroRepository.findById(heroId);
    if (XResultUtils.isFailure(heroResult)) {
      this.logger.error(`获取球员信息失败: ${heroResult.message}`);
      return XResultUtils.ok(false);
    }

    const hero = heroResult.data;
    if (!hero) {
      return XResultUtils.ok(false);
    }

    // 退役条件：合约到期且生涯次数达到上限
    const maxLifeNum = this.calculateMaxLifeNum(hero.quality);
    const shouldRetire = hero.contractDays <= 0 && hero.lifeNum >= maxLifeNum;

    if (shouldRetire) {
      const updateResult = await this.heroRepository.update(heroId, {
        isRetired: true,
        retirementTime: Date.now(),
      });

      if (XResultUtils.isSuccess(updateResult)) {
        this.logger.log(`球员退役: ${heroId}, 生涯次数: ${hero.lifeNum}`);
      } else {
        this.logger.error(`更新球员退役状态失败: ${updateResult.message}`);
      }
    }

    return XResultUtils.ok(shouldRetire);
  }

  // ==================== 状态管理辅助方法 ====================

  /**
   * 计算疲劳衰减系数
   */
  private calculateFatigueRatio(fatigue: number): number {
    // 疲劳值越高，属性衰减越严重
    // 0疲劳 = 1.0倍属性，100疲劳 = 0.5倍属性
    return Math.max(0.5, 1.0 - (fatigue * 0.005));
  }

  /**
   * 计算治疗信息
   */
  private calculateTreatmentInfo(treatmentType: string, fatigue: number): any {
    switch (treatmentType) {
      case 'quick': // 快速治疗 - 5分钟，费用高
        return {
          duration: 5 * 60 * 1000,
          cost: { gold: 5000, items: [] },
        };
      case 'normal': // 普通治疗 - 根据疲劳值计算时间
        return {
          duration: fatigue * 60 * 1000, // 每点疲劳1分钟
          cost: { gold: fatigue * 100, items: [] },
        };
      case 'full': // 完全治疗 - 立即恢复，费用最高
        return {
          duration: 0,
          cost: { gold: 10000, items: [{ itemId: 'healing_potion', quantity: 1 }] },
        };
      default:
        return {
          duration: fatigue * 60 * 1000,
          cost: { gold: fatigue * 100, items: [] },
        };
    }
  }

  /**
   * 计算续约费用
   */
  private calculateRenewalCost(level: number, quality: number, contractDays: number): any {
    const baseCost = level * quality * 100;
    const daysCost = contractDays * 50;
    const totalGold = baseCost + daysCost;

    return {
      gold: totalGold,
      items: [],
    };
  }

  /**
   * 计算最大生涯次数
   */
  private calculateMaxLifeNum(quality: number): number {
    // 品质越高，可续约次数越多
    switch (quality) {
      case 1: return 3;  // 白色品质最多3次
      case 2: return 4;  // 绿色品质最多4次
      case 3: return 5;  // 蓝色品质最多5次
      case 4: return 6;  // 紫色品质最多6次
      case 5: return 8;  // 橙色品质最多8次
      case 6: return 10; // 红色品质最多10次
      default: return 3;
    }
  }

  /**
   * 获取球员状态信息
   * Controller层已验证参数，无需重复验证
   */
  async getHeroStatus(heroId: string): Promise<XResult<any>> {
    const heroResult = await this.heroRepository.findById(heroId);
    if (XResultUtils.isFailure(heroResult)) {
      return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
    }

    const hero = heroResult.data;
    if (!hero) {
      return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
    }

    const currentTime = Date.now();
    const maxLifeNum = this.calculateMaxLifeNum(hero.quality);
    const canRenew = hero.contractDays <= 30 && hero.lifeNum < maxLifeNum;
    const willRetire = hero.contractDays <= 0 && hero.lifeNum >= maxLifeNum;

    const statusData = {
      heroId,
      fatigue: hero.fatigue,
      fatigueRatio: hero.fatigueRatio,
      isTreat: hero.isTreat,
      isTraining: hero.training?.lastTrainingTime ? (Date.now() - hero.training.lastTrainingTime < (hero.training.trainingCooldown || 0)) : false,
      isLocked: hero.isLocked,
      isInLineup: hero.isInLineup,
      contractDays: hero.contractDays,
      lifeNum: hero.lifeNum,
      maxLifeNum,
      canRenew,
      willRetire,
      treatmentTimeRemaining: hero.isTreat ? Math.max(0, hero.reTimeFatigue - currentTime) : 0,
      fatigueRecoveryTime: hero.fatigue > 0 ? hero.reTimeFatigue : 0,
      energy: hero.energy,
      morale: hero.morale,
      battleNum: hero.battleNum,
      marketValue: hero.marketValue,
      isOnMarket: hero.isOnMarket,
    };

    return XResultUtils.ok(statusData);
  }

  // ==================== 突破系统辅助方法 ====================

  /**
   * 计算当前潜能值
   */
  private calculateCurrentPotential(breakthrough: number[]): number {
    return breakthrough.reduce((sum, value) => sum + value, 0);
  }

  /**
   * 计算突破费用
   */
  private calculateBreakthroughCost(currentCount: number): number {
    // 基础费用随突破次数递增
    return 10000 * Math.pow(1.5, currentCount);
  }

  /**
   * 计算撤销费用
   */
  private calculateRevertCost(): number {
    // 撤销费用固定
    return 50000;
  }

  /**
   * 计算突破值（基于品质和权重）
   */
  private calculateBreakthroughValue(quality: number, breakthroughLayer: number): number {
    // 根据品质确定权重ID
    const weightId = this.getBreakthroughWeightId(quality);

    // 根据权重和层数计算突破值
    return this.calculateWeightedBreakthroughValue(weightId, breakthroughLayer);
  }

  /**
   * 根据品质获取突破权重ID
   */
  private getBreakthroughWeightId(quality: number): number {
    // 品质越高，获得高突破值的概率越大
    switch (quality) {
      case 1: return 1; // 白色品质
      case 2: return 2; // 绿色品质
      case 3: return 3; // 蓝色品质
      case 4: return 4; // 紫色品质
      case 5: return 5; // 橙色品质
      case 6: return 6; // 红色品质
      default: return 1;
    }
  }

  /**
   * 根据权重计算突破值
   */
  private calculateWeightedBreakthroughValue(weightId: number, layer: number): number {
    // 模拟old项目中的权重配置
    const weightConfigs = {
      1: { // 白色品质权重
        1: [10, 15, 20, 25, 15, 10, 5], // 1-7的权重
        2: [15, 20, 25, 20, 10, 7, 3],
        3: [20, 25, 25, 15, 8, 5, 2],
      },
      2: { // 绿色品质权重
        1: [8, 12, 18, 25, 20, 12, 5],
        2: [10, 15, 20, 25, 18, 10, 2],
        3: [15, 20, 25, 20, 12, 6, 2],
      },
      3: { // 蓝色品质权重
        1: [5, 10, 15, 25, 25, 15, 5],
        2: [8, 12, 18, 25, 22, 12, 3],
        3: [10, 15, 20, 25, 20, 8, 2],
      },
      4: { // 紫色品质权重
        1: [3, 8, 12, 22, 28, 20, 7],
        2: [5, 10, 15, 25, 25, 15, 5],
        3: [8, 12, 18, 25, 22, 12, 3],
      },
      5: { // 橙色品质权重
        1: [2, 5, 10, 20, 30, 25, 8],
        2: [3, 8, 12, 22, 28, 20, 7],
        3: [5, 10, 15, 25, 25, 15, 5],
      },
      6: { // 红色品质权重
        1: [1, 3, 8, 18, 32, 28, 10],
        2: [2, 5, 10, 20, 30, 25, 8],
        3: [3, 8, 12, 22, 28, 20, 7],
      },
    };

    const layerWeights = weightConfigs[weightId]?.[Math.min(layer, 3)] || weightConfigs[1][1];

    // 根据权重随机选择突破值
    const totalWeight = layerWeights.reduce((sum, weight) => sum + weight, 0);
    let random = Math.random() * totalWeight;

    for (let i = 0; i < layerWeights.length; i++) {
      random -= layerWeights[i];
      if (random <= 0) {
        return i + 1; // 返回1-7的值
      }
    }

    return 1; // 默认返回1
  }

  /**
   * 计算属性上限
   */
  private calculateAttributeLimits(resId: number, potential: number): any {
    // 基于潜能值计算属性上限提升
    // 潜能值越高，属性上限越高
    const potentialMultiplier = potential / 10; // 每10点潜能提升1倍

    const baseLimit = Math.round(80 + potentialMultiplier * 20);
    return {
      speed: baseLimit, jumping: baseLimit, strength: baseLimit, stamina: baseLimit, explosiveForce: baseLimit,
      finishing: baseLimit, dribbling: baseLimit, passing: baseLimit, longPassing: baseLimit, longShots: baseLimit,
      heading: baseLimit, volleys: baseLimit, standingTackle: baseLimit, slidingTackle: baseLimit,
      penalties: baseLimit, cornerKick: baseLimit, freeKick: baseLimit, attack: baseLimit, save: baseLimit, resistanceDamage: baseLimit
    };
  }

  /**
   * 获取突破信息
   * Controller层已验证参数，无需重复验证
   */
  async getBreakthroughInfo(heroId: string): Promise<XResult<any>> {
    const heroResult = await this.heroRepository.findById(heroId);
    if (XResultUtils.isFailure(heroResult)) {
      return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
    }

    const hero = heroResult.data;
    if (!hero) {
      return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
    }

    const currentPotential = this.calculateCurrentPotential(hero.breakthrough || []);
    const canBreakthrough = (hero.breakthrough?.length || 0) < 10;
    const canRevert = (hero.breakthrough?.length || 0) > 0 && hero.breakthrough?.some(value => value !== 7);

    const breakthroughInfo = {
      heroId,
      breakthroughCount: hero.breakthrough?.length || 0,
      breakthroughHistory: hero.breakthrough || [],
      currentPotential,
      maxPotential: 70, // 10次 * 7点
      oldBreakOut: hero.oldBreakOut || 30,
      canBreakthrough,
      canRevert,
      breakthroughCost: canBreakthrough ? this.calculateBreakthroughCost(hero.breakthrough?.length || 0) : 0,
      revertCost: canRevert ? this.calculateRevertCost() : 0,
      attributeLimits: this.calculateAttributeLimits(hero.resId, currentPotential),
    };

    return XResultUtils.ok(breakthroughInfo);
  }

  // ==================== 属性重新计算系统 ====================

  /**
   * 重新计算球员所有属性（核心方法）
   * 对应old项目中的reCalcAttrRevision方法
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async reCalcHeroAttributes(heroId: string): Promise<XResult<void>> {
    return this.executeBusinessOperation(async () => {
      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      // 1. 计算图鉴加成（教练加成）
      const coachBonusResult = await this.calculateCoachBonus(hero);
      if (XResultUtils.isFailure(coachBonusResult)) {
        return XResultUtils.error(`计算教练加成失败: ${coachBonusResult.message}`, coachBonusResult.code);
      }
      const coachBonus = coachBonusResult.data;

      // 2. 获取球员所在阵容信息
      const formationInfoResult = await this.getHeroFormationInfo(heroId);
      if (XResultUtils.isFailure(formationInfoResult)) {
        return XResultUtils.error(`获取阵容信息失败: ${formationInfoResult.message}`, formationInfoResult.code);
      }
      const formationInfo = formationInfoResult.data;

      // 3. 计算阵容相关加成
      let formationBonus = { attributes: {}, tactics: {}, trainer: {} };
      if (formationInfo.isInMainFormation) {
        const formationBonusResult = await this.calculateFormationBonus(heroId, formationInfo.teamId);
        if (XResultUtils.isFailure(formationBonusResult)) {
          return XResultUtils.error(`计算阵容加成失败: ${formationBonusResult.message}`, formationBonusResult.code);
        }
        formationBonus = formationBonusResult.data;
      }

      // 4. 计算信仰技能加成
      const beliefSkillBonusResult = await this.calculateBeliefSkillBonus(hero);
      if (XResultUtils.isFailure(beliefSkillBonusResult)) {
        return XResultUtils.error(`计算信仰技能加成失败: ${beliefSkillBonusResult.message}`, beliefSkillBonusResult.code);
      }
      const beliefSkillBonus = beliefSkillBonusResult.data;

      // 5. 计算一级属性（基础属性）
      const primaryAttributesResult = await this.calculatePrimaryAttributes(hero, {
        coach: coachBonus,
        formation: formationBonus.attributes,
        beliefSkill: beliefSkillBonus,
      });
      if (XResultUtils.isFailure(primaryAttributesResult)) {
        return XResultUtils.error(`计算一级属性失败: ${primaryAttributesResult.message}`, primaryAttributesResult.code);
      }
      const primaryAttributes = primaryAttributesResult.data;

      // 6. 计算二级属性（综合属性）
      const secondaryAttributesResult = await this.calculateSecondaryAttributes(hero, primaryAttributes);
      if (XResultUtils.isFailure(secondaryAttributesResult)) {
        return XResultUtils.error(`计算二级属性失败: ${secondaryAttributesResult.message}`, secondaryAttributesResult.code);
      }
      const secondaryAttributes = secondaryAttributesResult.data;

      // 7. 更新球员属性 - 使用正确的schema字段名称
      const updateResult = await this.heroRepository.update(heroId, {
        'attributes.speed': primaryAttributes.speed,
        'attributes.finishing': primaryAttributes.shooting,
        'attributes.passing': primaryAttributes.passing,
        'attributes.standingTackle': primaryAttributes.defending,
        'attributes.dribbling': primaryAttributes.dribbling,
        'attributes.strength': primaryAttributes.physicality,
        'attributes.save': primaryAttributes.goalkeeping,
        'secondaryAttributes': secondaryAttributes,
        'lastAttributeCalculation': Date.now(),
      });

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员属性失败: ${updateResult.message}`, updateResult.code);
      }

      this.logger.log(`球员属性重新计算完成: ${heroId}`);
      return XResultUtils.ok(undefined);
    }, { reason: 'recalc_hero_attributes', metadata: { heroId } });
  }

  /**
   * 批量重新计算所有球员属性
   * Controller层已验证参数，无需重复验证
   */
  async reCalcAllHeroAttributes(characterId: string): Promise<XResult<void>> {
    const heroesResult = await this.heroRepository.findByCharacterId(characterId);
    if (XResultUtils.isFailure(heroesResult)) {
      return XResultUtils.error(`获取角色球员列表失败: ${heroesResult.message}`, heroesResult.code);
    }

    const heroes = heroesResult.data || [];

    for (const hero of heroes) {
      const recalcResult = await this.reCalcHeroAttributes(hero.heroId);
      if (XResultUtils.isFailure(recalcResult)) {
        this.logger.warn(`重新计算球员属性失败: ${hero.heroId}, ${recalcResult.message}`);
        // 继续处理其他球员，不中断整个流程
      }
    }

    this.logger.log(`角色所有球员属性重新计算完成: ${characterId}, 共${heroes.length}个球员`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 获取角色球员统计信息
   * 基于old项目: 统计角色的球员数据
   */
  /**
   * 获取角色球员列表
   * Controller层已验证参数，无需重复验证
   */
  async getHeroes(characterId: string): Promise<XResult<any[]>> {
    this.logger.log(`获取角色球员列表: ${characterId}`);

    // 获取角色所有球员
    const heroesResult = await this.heroRepository.findByCharacterId(characterId);
    if (XResultUtils.isFailure(heroesResult)) {
      return XResultUtils.error(`获取角色球员列表失败: ${heroesResult.message}`, heroesResult.code);
    }

    const heroes = heroesResult.data || [];
    return XResultUtils.ok(heroes);
  }

  /**
   * 装备道具
   * 供InventoryService调用，处理装备类道具的使用
   */
  async equipItem(characterId: string, equipmentId: number, quantity: number): Promise<XResult<void>> {
    this.logger.log(`装备道具: 角色${characterId}, 装备${equipmentId}, 数量${quantity}`);

    // TODO: 实现装备逻辑
    // 1. 验证装备配置
    // 2. 检查角色是否有对应球员
    // 3. 应用装备效果
    // 4. 更新球员属性

    // 暂时返回成功
    return XResultUtils.ok(undefined);
  }

  /**
   * 获取角色球员统计信息
   * Controller层已验证参数，无需重复验证
   */
  async getCharacterHeroStats(characterId: string): Promise<XResult<any>> {
    this.logger.log(`获取角色球员统计: ${characterId}`);

    // 获取角色所有球员
    const heroesResult = await this.heroRepository.findByCharacterId(characterId);
    if (XResultUtils.isFailure(heroesResult)) {
      return XResultUtils.error(`获取角色球员列表失败: ${heroesResult.message}`, heroesResult.code);
    }

    const heroes = heroesResult.data || [];

    if (heroes.length === 0) {
      const emptyStats = {
        totalHeroes: 0,
        averageLevel: 0,
        maxLevel: 0,
        totalMarketValue: 0,
        heroesInFormation: 0,
        heroesOnMarket: 0,
        averageRating: 0,
        totalPower: 0,
      };
      return XResultUtils.ok(emptyStats);
    }

    // 计算统计数据
    const totalHeroes = heroes.length;
    const totalLevel = heroes.reduce((sum, hero) => sum + (hero.level || 1), 0);
    const averageLevel = Math.round(totalLevel / totalHeroes);
    const maxLevel = Math.max(...heroes.map(hero => hero.level || 1));

    // 计算总市场价值
    const totalMarketValue = heroes.reduce((sum, hero) => sum + (hero.marketValue || 0), 0);

    // 计算平均评分
    const totalRating = heroes.reduce((sum, hero) => sum + (hero.averageRating || 0), 0);
    const averageRating = totalHeroes > 0 ? Math.round(totalRating / totalHeroes) : 0;

    // 计算总实力
    const totalPower = heroes.reduce((sum, hero) => sum + (hero.totalPower || 0), 0);

    // TODO: 统计阵容中的球员数量（需要调用Character服务）
    const heroesInFormation = 0;

    // TODO: 统计市场上的球员数量（需要调用相关服务）
    const heroesOnMarket = 0;

    const stats = {
      totalHeroes,
      averageLevel,
      maxLevel,
      totalMarketValue,
      heroesInFormation,
      heroesOnMarket,
      averageRating,
      totalPower,
    };

    this.logger.debug(`角色球员统计完成: ${characterId}`, stats);
    return XResultUtils.ok(stats);
  }

  /**
   * 计算教练图鉴加成
   * 基于old项目: calcCoachAttr + checkHeroHandbookAttr逻辑
   *
   * 实现逻辑：
   * 1. 获取角色的图鉴收集数据
   * 2. 计算单个球员图鉴加成
   * 3. 计算组合图鉴加成
   * 4. 累加所有图鉴加成
   */
  private async calculateCoachBonus(hero: any): Promise<XResult<any>> {
    try {
      // 1. 获取角色的图鉴收集数据
      const handbookDataResult = await this.getCharacterHandbookData(hero.characterId);
      if (XResultUtils.isFailure(handbookDataResult)) {
        this.logger.warn(`获取图鉴数据失败: ${handbookDataResult.message}`);
        return XResultUtils.ok(this.getEmptyAttributeBonus());
      }

      const handbookData = handbookDataResult.data;
      if (!handbookData) {
        return XResultUtils.ok(this.getEmptyAttributeBonus());
      }

      let totalBonus = this.getEmptyAttributeBonus();

      // 2. 计算单个球员图鉴加成（基于old项目PlayerHandbook配置）
      if (handbookData.playerHandbook && handbookData.playerHandbook.length > 0) {
        for (const resId of handbookData.playerHandbook) {
          const playerConfig = await this.gameConfig.playerHandbook?.get(resId);
          if (playerConfig) {
            // 基于old项目：直接累加各属性值
            totalBonus.speed += playerConfig.speed || 0;
            totalBonus.shooting += playerConfig.finishing || 0;
            totalBonus.passing += playerConfig.passing || 0;
            totalBonus.defending += playerConfig.standingTackle || 0;
            totalBonus.dribbling += playerConfig.dribbling || 0;
            totalBonus.physicality += playerConfig.strength || 0;
            totalBonus.goalkeeping += playerConfig.save || 0;
          }
        }
      }

      // 3. 计算组合图鉴加成（基于old项目CombinationHandbook配置）
      if (handbookData.combinationHandbook && handbookData.combinationHandbook.length > 0) {
        for (const comboId of handbookData.combinationHandbook) {
          const comboConfig = await this.gameConfig.combinationHandbook?.get(comboId);
          if (comboConfig) {
            // 基于old项目：组合图鉴提供额外加成
            totalBonus.speed += comboConfig.speed || 0;
            totalBonus.shooting += comboConfig.finishing || 0;
            totalBonus.passing += comboConfig.passing || 0;
            totalBonus.defending += comboConfig.standingTackle || 0;
            totalBonus.dribbling += comboConfig.dribbling || 0;
            totalBonus.physicality += comboConfig.strength || 0;
            totalBonus.goalkeeping += comboConfig.save || 0;
          }
        }
      }

      this.logger.debug(`教练图鉴加成计算完成: ${hero._id}`, totalBonus);
      return XResultUtils.ok(totalBonus);
    } catch (error) {
      this.logger.error('计算教练图鉴加成失败', error);
      return XResultUtils.ok(this.getEmptyAttributeBonus());
    }
  }

  /**
   * 获取球员阵容信息
   * 基于old项目: getHeroInMainTeamUid
   * 修复：使用内部FormationService调用替代微服务调用
   */
  private async getHeroFormationInfo(heroId: string): Promise<XResult<any>> {
    // 使用内部FormationService获取球员阵容信息（基于old项目getHeroInMainTeamUid）
    const result = await this.formationService.getHeroInActiveLineup(heroId);

    if (XResultUtils.isFailure(result)) {
      this.logger.debug(`获取球员阵容信息失败: ${heroId}, ${result.message}`);
      const defaultFormationInfo = {
        isInMainFormation: false,
        lineupId: null,
        position: null,
      };
      return XResultUtils.ok(defaultFormationInfo);
    }

    const formationData = result.data || {
      isInActiveLineup: false,
      lineupId: null,
      position: null,
      formationType: null
    };

    const formationInfo = {
      isInMainFormation: formationData.isInActiveLineup,
      lineupId: formationData.lineupId,
      position: formationData.position,
    };

    return XResultUtils.ok(formationInfo);
  }

  /**
   * 计算阵容相关加成
   * 基于old项目: calcHeroTrainerAttr + calcHeroTacticsAttr + calcTrainerSkillAttr逻辑
   *
   * 实现逻辑：
   * 1. 计算教练属性加成
   * 2. 计算战术加成
   * 3. 计算教练技能加成
   * 4. 返回综合加成结果
   */
  private async calculateFormationBonus(heroId: string, teamId: string): Promise<XResult<any>> {
    // 1. 计算教练属性加成（基于old项目calcHeroTrainerAttr）
    const trainerBonusResult = await this.calculateTrainerAttributeBonus(heroId, teamId);
    if (XResultUtils.isFailure(trainerBonusResult)) {
      this.logger.warn(`计算教练属性加成失败: ${trainerBonusResult.message}`);
    }
    const trainerBonus = XResultUtils.isSuccess(trainerBonusResult) ? trainerBonusResult.data : this.getEmptyAttributeBonus();

    // 2. 计算战术加成（基于old项目calcHeroTacticsAttr）
    const tacticsBonusResult = await this.calculateTacticsBonus(heroId, teamId);
    if (XResultUtils.isFailure(tacticsBonusResult)) {
      this.logger.warn(`计算战术加成失败: ${tacticsBonusResult.message}`);
    }
    const tacticsBonus = XResultUtils.isSuccess(tacticsBonusResult) ? tacticsBonusResult.data : { attack: 0, defend: 0 };

    // 3. 计算教练技能加成（基于old项目calcTrainerSkillAttr）
    const trainerSkillBonusResult = await this.calculateTrainerSkillBonus(heroId, teamId);
    if (XResultUtils.isFailure(trainerSkillBonusResult)) {
      this.logger.warn(`计算教练技能加成失败: ${trainerSkillBonusResult.message}`);
    }
    const trainerSkillBonus = XResultUtils.isSuccess(trainerSkillBonusResult) ? trainerSkillBonusResult.data : this.getEmptyAttributeBonus();

    const formationBonus = {
      attributes: {
        speed: trainerBonus.speed + trainerSkillBonus.speed,
        shooting: trainerBonus.shooting + trainerSkillBonus.shooting,
        passing: trainerBonus.passing + trainerSkillBonus.passing,
        defending: trainerBonus.defending + trainerSkillBonus.defending,
        dribbling: trainerBonus.dribbling + trainerSkillBonus.dribbling,
        physicality: trainerBonus.physicality + trainerSkillBonus.physicality,
        goalkeeping: trainerBonus.goalkeeping + trainerSkillBonus.goalkeeping,
      },
      tactics: {
        attack: tacticsBonus.attack,
        defend: tacticsBonus.defend,
      },
      trainer: trainerBonus,
    };

    return XResultUtils.ok(formationBonus);
  }

  /**
   * 计算信仰技能加成
   * 基于old项目: reCalcBeliefSkillAttr逻辑
   *
   * 实现逻辑：
   * 1. 获取角色的信仰技能列表
   * 2. 检查每个技能是否解锁和激活
   * 3. 检查球员是否享有该技能加成
   * 4. 累加所有有效的技能加成
   */
  private async calculateBeliefSkillBonus(hero: any): Promise<XResult<any>> {
    // 1. 获取角色的信仰技能数据
    const beliefSkillDataResult = await this.getCharacterBeliefSkillData(hero.characterId);
    if (XResultUtils.isFailure(beliefSkillDataResult)) {
      this.logger.warn(`获取信仰技能数据失败: ${beliefSkillDataResult.message}`);
      return XResultUtils.ok(this.getEmptyAttributeBonus());
    }

    const beliefSkillData = beliefSkillDataResult.data;
    if (!beliefSkillData || !beliefSkillData.skillList) {
      return XResultUtils.ok(this.getEmptyAttributeBonus());
    }

    let totalBonus = this.getEmptyAttributeBonus();

    // 2. 遍历所有信仰技能
    for (const skill of beliefSkillData.skillList) {
      // 检查技能是否激活（status === 1）
      if (skill.status !== 1) {
        continue;
      }

      // 3. 检查球员是否享有该技能加成
      const hasSkillBonusResult = await this.checkHeroHasBeliefSkillBonus(hero, skill.skillId);
      if (XResultUtils.isFailure(hasSkillBonusResult) || !hasSkillBonusResult.data) {
        continue;
      }

      // 4. 获取技能配置并计算加成
      const skillConfig = await this.gameConfig.beliefSkill?.get(skill.skillId);
      if (skillConfig) {
        // 基于old项目：技能等级影响加成值
        const skillLevel = skill.level || 1;
        const bonusMultiplier = skillLevel; // 简化处理，实际可能有更复杂的公式

        // 累加技能加成（基于old项目的属性映射）
        totalBonus.speed += (skillConfig.speed || 0) * bonusMultiplier;
        totalBonus.shooting += (skillConfig.finishing || 0) * bonusMultiplier;
        totalBonus.passing += (skillConfig.passing || 0) * bonusMultiplier;
        totalBonus.defending += (skillConfig.standingTackle || 0) * bonusMultiplier;
        totalBonus.dribbling += (skillConfig.dribbling || 0) * bonusMultiplier;
        totalBonus.physicality += (skillConfig.strength || 0) * bonusMultiplier;
        totalBonus.goalkeeping += (skillConfig.save || 0) * bonusMultiplier;
      }
    }

    this.logger.debug(`信仰技能加成计算完成: ${hero._id}`, totalBonus);
    return XResultUtils.ok(totalBonus);
  }

  /**
   * 获取角色图鉴收集数据
   * 基于old项目: player.footballGround.handbookData
   * 修复：使用内部CharacterService调用替代微服务调用
   */
  private async getCharacterHandbookData(characterId: string): Promise<XResult<any>> {
    // 使用内部CharacterService获取图鉴数据（基于old项目player.footballGround.getBallHandbook）
    const result = await this.characterService.getCharacterInfo(characterId);

    if (XResultUtils.isFailure(result)) {
      this.logger.warn(`获取角色信息失败: ${characterId}, ${result.message}`);
      const defaultHandbookData = {
        playerHandbook: [],
        combinationHandbook: [],
        ballActual: 0,
        ballComActual: 0,
      };
      return XResultUtils.ok(defaultHandbookData);
    }

    // 从角色数据中提取图鉴信息
    const footballGroundData = result.data?.footballGround || {};

    const handbookData = {
      playerHandbook: footballGroundData.ballHandbook || [],        // 已收集的球员图鉴
      combinationHandbook: footballGroundData.ballComHandbook || [], // 已激活的组合图鉴
      ballActual: footballGroundData.ballActual || 0,               // 球员实力加成
      ballComActual: footballGroundData.ballComActual || 0,         // 组合实力加成
    };

    return XResultUtils.ok(handbookData);
  }

  /**
   * 获取角色信仰技能数据
   * 基于old项目: player.beliefSkill.skillList
   */
  private async getCharacterBeliefSkillData(characterId: string): Promise<XResult<any>> {
    // TODO: 调用Character服务获取信仰技能数据
    // const result = await this.callMicroservice(
    //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
    //   'character.getBeliefSkillData',
    //   { characterId }
    // );
    //
    // if (XResultUtils.isFailure(result)) {
    //   this.logger.error(`获取角色信仰技能数据失败: ${result.message}`);
    //   return XResultUtils.ok({ skillList: [] });
    // }
    //
    // return XResultUtils.ok(result.data);

    // 暂时返回模拟数据
    const mockBeliefSkillData = {
      skillList: [
        { skillId: 1001, status: 1, level: 2 },
        { skillId: 1002, status: 1, level: 1 },
        { skillId: 1003, status: 0, level: 0 },
      ],
    };

    return XResultUtils.ok(mockBeliefSkillData);
  }

  /**
   * 检查球员是否享有信仰技能加成
   * 基于old项目: checkHeroIsHaveBeliefSkillAttr逻辑
   *
   * 实现逻辑：
   * 1. 获取技能配置中的位置要求
   * 2. 获取球员配置中的位置信息
   * 3. 检查位置是否匹配
   */
  private async checkHeroHasBeliefSkillBonus(hero: any, skillId: number): Promise<XResult<boolean>> {
    // 1. 获取信仰技能配置
    const skillConfig = await this.gameConfig.beliefSkill?.get(skillId);
    if (!skillConfig) {
      return XResultUtils.ok(false);
    }

    // 2. 获取球员配置
    const heroConfigResult = await this.getHeroDefinitionConfig(hero.resId);
    if (XResultUtils.isFailure(heroConfigResult)) {
      this.logger.warn(`获取球员配置失败: ${heroConfigResult.message}`);
      return XResultUtils.ok(false);
    }

    const heroConfig = heroConfigResult.data;
    if (!heroConfig) {
      return XResultUtils.ok(false);
    }

    // 3. 检查位置匹配（基于old项目逻辑）
    const heroPosition = heroConfig.position1; // 球员主位置

    // 检查技能的AddPosition1-AddPosition12字段
    for (let i = 1; i <= 12; i++) {
      const addPositionField = `addPosition${i}`;
      const skillPosition = skillConfig[addPositionField];

      if (heroPosition === skillPosition) {
        return XResultUtils.ok(true);
      }
    }

    return XResultUtils.ok(false);
  }

  /**
   * 获取球员配置
   * 基于old项目: Footballer或FootballerPve配置表
   */
  private async getHeroDefinitionConfig(resId: number): Promise<XResult<any>> {
    try {
      // 基于old项目逻辑：resId/10000 < 9 使用Hero，否则使用HeroPve
      const bead = Math.floor(resId / 10000);

      let heroConfig;
      if (bead < 9) {
        heroConfig = await this.gameConfig.hero?.get(resId);
      } else {
        heroConfig = await this.gameConfig.heroPve?.get(resId);
      }

      if (!heroConfig) {
        return XResultUtils.error('球员配置不存在', 'HERO_CONFIG_NOT_FOUND');
      }

      return XResultUtils.ok(heroConfig);
    } catch (error) {
      this.logger.error('获取球员配置失败', error);
      return XResultUtils.error('获取球员配置失败', 'GET_HERO_CONFIG_ERROR');
    }
  }

  /**
   * 计算教练属性加成
   * 基于old项目: calcHeroTrainerAttr逻辑
   * 修复：使用内部FormationService调用替代微服务调用
   */
  private async calculateTrainerAttributeBonus(heroId: string, teamId: string): Promise<XResult<any>> {
    // 使用内部FormationService获取阵容教练数据（基于old项目calcHeroTrainerAttr）
    const result = await this.formationService.calcHeroTrainerAttr(heroId, teamId);

    if (XResultUtils.isFailure(result)) {
      this.logger.debug(`获取教练属性加成失败: ${heroId}, ${teamId}, ${result.message}`);
      return XResultUtils.ok(this.getEmptyAttributeBonus());
    }

    // 转换old项目属性格式到新格式
    const convertedAttributes = this.convertOldAttributesToNew(result.data);
    return XResultUtils.ok(convertedAttributes);
  }

  /**
   * 计算战术加成
   * 基于old项目: calcHeroTacticsAttr逻辑
   * 修复：使用内部FormationService调用替代微服务调用
   */
  private async calculateTacticsBonus(heroId: string, teamId: string): Promise<XResult<any>> {
    // 使用内部FormationService获取战术加成（基于old项目calcHeroTacticsAttr）
    const result = await this.formationService.calcHeroTacticsAttr(heroId, teamId);

    if (XResultUtils.isFailure(result)) {
      this.logger.debug(`获取战术加成失败: ${heroId}, ${teamId}, ${result.message}`);
      const defaultTacticsBonus = { attack: 0, defend: 0 };
      return XResultUtils.ok(defaultTacticsBonus);
    }

    const tacticsBonus = {
      attack: result.data?.attack || 0,
      defend: result.data?.defend || 0,
    };

    return XResultUtils.ok(tacticsBonus);
  }

  /**
   * 计算教练技能加成
   * 基于old项目: calcTrainerSkillAttr逻辑
   * 修复：使用内部FormationService调用替代微服务调用
   */
  private async calculateTrainerSkillBonus(heroId: string, teamId: string): Promise<XResult<any>> {
    // 使用内部FormationService获取教练技能加成（基于old项目calcTrainerSkillAttr）
    const result = await this.formationService.calcTrainerSkillAttr(heroId, teamId);

    if (XResultUtils.isFailure(result)) {
      this.logger.debug(`获取教练技能加成失败: ${heroId}, ${teamId}, ${result.message}`);
      return XResultUtils.ok(this.getEmptyAttributeBonus());
    }

    // 转换old项目属性格式到新格式
    const convertedAttributes = this.convertOldAttributesToNew(result.data);
    return XResultUtils.ok(convertedAttributes);
  }

  /**
   * 获取空的属性加成对象
   */
  private getEmptyAttributeBonus(): any {
    return {
      speed: 0, jumping: 0, strength: 0, stamina: 0, explosiveForce: 0,
      finishing: 0, dribbling: 0, passing: 0, longPassing: 0, longShots: 0,
      heading: 0, volleys: 0, standingTackle: 0, slidingTackle: 0,
      penalties: 0, cornerKick: 0, freeKick: 0, attack: 0, save: 0, resistanceDamage: 0
    };
  }

  /**
   * 计算一级属性（基础属性）
   * 基于old项目: calcHeroAttr逻辑
   */
  private async calculatePrimaryAttributes(hero: any, bonuses: any): Promise<XResult<any>> {
    try {
      const baseAttributes = hero.attributes || {};
      const starBonus = this.calculateStarAttributeBonus(hero);
      const trainingBonus = this.calculateTrainingAttributeBonus(hero);
      const breakthroughBonus = this.calculateBreakthroughAttributeBonus(hero);

      const result = {};
      const attributeKeys = [
        'speed', 'jumping', 'strength', 'stamina', 'explosiveForce',
        'finishing', 'dribbling', 'passing', 'longPassing', 'longShots',
        'heading', 'volleys', 'standingTackle', 'slidingTackle',
        'penalties', 'cornerKick', 'freeKick', 'attack', 'save', 'resistanceDamage'
      ];

      for (const attr of attributeKeys) {
        result[attr] = Math.round(
          (baseAttributes[attr] || 0) +
          (starBonus[attr] || 0) +
          (trainingBonus[attr] || 0) +
          (breakthroughBonus[attr] || 0) +
          (bonuses.coach?.[attr] || 0) +
          (bonuses.formation?.[attr] || 0) +
          (bonuses.beliefSkill?.[attr] || 0)
        );
      }

      return XResultUtils.ok(result);
    } catch (error) {
      this.logger.error('计算一级属性失败', error);
      return XResultUtils.error('计算一级属性失败', 'CALCULATE_PRIMARY_ATTRIBUTES_ERROR');
    }
  }

  /**
   * 计算二级属性（综合属性）
   * 基于old项目: 综合评分计算逻辑
   */
  private async calculateSecondaryAttributes(hero: any, primaryAttributes: any): Promise<XResult<any>> {
    try {
      // 根据位置计算综合评分
      const position = hero.position;
      let overallRating = 0;

      switch (position) {
        case 1: // 守门员
          overallRating = Math.round(
            primaryAttributes.goalkeeping * 0.6 +
            primaryAttributes.defending * 0.2 +
            primaryAttributes.physicality * 0.2
          );
          break;
        case 2:
        case 3:
        case 4:
        case 5: // 后卫
          overallRating = Math.round(
            primaryAttributes.defending * 0.4 +
            primaryAttributes.physicality * 0.3 +
            primaryAttributes.passing * 0.2 +
            primaryAttributes.speed * 0.1
          );
          break;
        case 6:
        case 7:
        case 8:
        case 9: // 中场
          overallRating = Math.round(
            primaryAttributes.passing * 0.3 +
            primaryAttributes.dribbling * 0.25 +
            primaryAttributes.speed * 0.2 +
            primaryAttributes.shooting * 0.15 +
            primaryAttributes.defending * 0.1
          );
          break;
        case 10:
        case 11: // 前锋
          overallRating = Math.round(
            primaryAttributes.shooting * 0.4 +
            primaryAttributes.dribbling * 0.3 +
            primaryAttributes.speed * 0.2 +
            primaryAttributes.physicality * 0.1
          );
          break;
        default:
          overallRating = Math.round(
            (primaryAttributes.speed +
             primaryAttributes.shooting +
             primaryAttributes.passing +
             primaryAttributes.defending +
             primaryAttributes.dribbling +
             primaryAttributes.physicality) / 6
          );
      }

      const secondaryAttributes = {
        overallRating,
        attackRating: Math.round((primaryAttributes.shooting + primaryAttributes.dribbling + primaryAttributes.speed) / 3),
        defenseRating: Math.round((primaryAttributes.defending + primaryAttributes.physicality) / 2),
        midFieldRating: Math.round((primaryAttributes.passing + primaryAttributes.dribbling) / 2),
      };

      return XResultUtils.ok(secondaryAttributes);
    } catch (error) {
      this.logger.error('计算二级属性失败', error);
      return XResultUtils.error('计算二级属性失败', 'CALCULATE_SECONDARY_ATTRIBUTES_ERROR');
    }
  }

  /**
   * 计算升星属性加成
   */
  private calculateStarAttributeBonus(hero: any): any {
    const starLevel = hero.evolution?.star || 0;
    const baseAttributes = hero.attributes;
    const bonusRate = starLevel * 0.04; // 每星级4%加成

    return {
      speed: Math.round(baseAttributes.speed * bonusRate),
      jumping: Math.round(baseAttributes.jumping * bonusRate),
      strength: Math.round(baseAttributes.strength * bonusRate),
      stamina: Math.round(baseAttributes.stamina * bonusRate),
      explosiveForce: Math.round(baseAttributes.explosiveForce * bonusRate),
      finishing: Math.round(baseAttributes.finishing * bonusRate),
      dribbling: Math.round(baseAttributes.dribbling * bonusRate),
      passing: Math.round(baseAttributes.passing * bonusRate),
      longPassing: Math.round(baseAttributes.longPassing * bonusRate),
      longShots: Math.round(baseAttributes.longShots * bonusRate),
      heading: Math.round(baseAttributes.heading * bonusRate),
      volleys: Math.round(baseAttributes.volleys * bonusRate),
      standingTackle: Math.round(baseAttributes.standingTackle * bonusRate),
      slidingTackle: Math.round(baseAttributes.slidingTackle * bonusRate),
      penalties: Math.round(baseAttributes.penalties * bonusRate),
      cornerKick: Math.round(baseAttributes.cornerKick * bonusRate),
      freeKick: Math.round(baseAttributes.freeKick * bonusRate),
      attack: Math.round(baseAttributes.attack * bonusRate),
      save: Math.round(baseAttributes.save * bonusRate),
      resistanceDamage: Math.round(baseAttributes.resistanceDamage * bonusRate)
    };
  }

  /**
   * 计算训练属性加成
   */
  private calculateTrainingAttributeBonus(hero: any): any {
    // 基于训练阶段计算属性加成
    const training = hero.training;
    if (!training) {
      return this.getEmptyAttributeBonus();
    }

    return {
      speed: this.calculateAttributeTrainingBonus(training.speedStage),
      jumping: this.calculateAttributeTrainingBonus(training.jumpingStage),
      strength: this.calculateAttributeTrainingBonus(training.strengthStage),
      stamina: this.calculateAttributeTrainingBonus(training.staminaStage),
      explosiveForce: this.calculateAttributeTrainingBonus(training.explosiveForceStage),
      finishing: this.calculateAttributeTrainingBonus(training.finishingStage),
      dribbling: this.calculateAttributeTrainingBonus(training.dribblingStage),
      passing: this.calculateAttributeTrainingBonus(training.passingStage),
      longPassing: this.calculateAttributeTrainingBonus(training.longPassingStage),
      longShots: this.calculateAttributeTrainingBonus(training.longShotsStage),
      heading: this.calculateAttributeTrainingBonus(training.headingStage),
      volleys: this.calculateAttributeTrainingBonus(training.volleysStage),
      standingTackle: this.calculateAttributeTrainingBonus(training.standingTackleStage),
      slidingTackle: this.calculateAttributeTrainingBonus(training.slidingTackleStage),
      penalties: this.calculateAttributeTrainingBonus(training.penaltiesStage),
      cornerKick: this.calculateAttributeTrainingBonus(training.cornerKickStage),
      freeKick: this.calculateAttributeTrainingBonus(training.freeKickStage),
      attack: this.calculateAttributeTrainingBonus(training.attackStage),
      save: this.calculateAttributeTrainingBonus(training.saveStage),
      resistanceDamage: this.calculateAttributeTrainingBonus(training.resistanceDamageStage)
    };
  }

  /**
   * 计算单个属性的训练加成
   */
  private calculateAttributeTrainingBonus(stageInfo: any): number {
    if (!stageInfo) return 0;

    const stage = stageInfo.stage || 1;
    const progress = stageInfo.stageProgress || 0;

    // 每个阶段提供固定加成，进度提供额外加成
    const stageBonus = (stage - 1) * 5; // 每阶段5点加成
    const progressBonus = Math.round(progress * 0.05); // 进度转换为加成

    return stageBonus + progressBonus;
  }

  /**
   * 计算突破属性加成
   */
  private calculateBreakthroughAttributeBonus(hero: any): any {
    const breakthrough = hero.breakthrough || [];
    const totalPotential = breakthrough.reduce((sum, value) => sum + value, 0);

    // 每点潜能提供0.5点属性加成
    const bonusPerAttribute = Math.round(totalPotential * 0.5);

    return {
      speed: bonusPerAttribute, jumping: bonusPerAttribute, strength: bonusPerAttribute,
      stamina: bonusPerAttribute, explosiveForce: bonusPerAttribute,
      finishing: bonusPerAttribute, dribbling: bonusPerAttribute, passing: bonusPerAttribute,
      longPassing: bonusPerAttribute, longShots: bonusPerAttribute,
      heading: bonusPerAttribute, volleys: bonusPerAttribute, standingTackle: bonusPerAttribute,
      slidingTackle: bonusPerAttribute,
      penalties: bonusPerAttribute, cornerKick: bonusPerAttribute, freeKick: bonusPerAttribute,
      attack: bonusPerAttribute, save: bonusPerAttribute, resistanceDamage: bonusPerAttribute
    };
  }

  // ==================== 球员生涯管理系统 ====================

  /**
   * 添加球员生涯天数（续约）
   * 对应old项目中的addHeroLeftDay方法
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async addHeroCareerDays(heroId: string, days: number = 365): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const heroResult = await this.heroRepository.findById(heroId);
      if (XResultUtils.isFailure(heroResult)) {
        return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
      }

      const hero = heroResult.data;
      if (!hero) {
        return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
      }

      // 检查球员是否已退役
      if (hero.isRetired) {
        return XResultUtils.error('球员已退役，无法续约', 'HERO_ALREADY_RETIRED');
      }

      // 检查生涯次数限制（根据品质决定）
      const maxLifeNum = this.calculateMaxLifeNum(hero.quality);
      if (hero.lifeNum >= maxLifeNum) {
        return XResultUtils.error('球员生涯次数已达上限', 'HERO_MAX_CAREER');
      }

      // 计算续约费用
      const renewalCost = this.calculateCareerRenewalCost(hero.level, hero.quality, days);

      // TODO: 检查金币是否足够（需要与Character服务通信）

      // 更新球员数据
      const newContractDays = hero.contractDays + days;
      const newLifeNum = hero.lifeNum + 1;

      const updateResult = await this.heroRepository.update(heroId, {
        contractDays: newContractDays,
        lifeNum: newLifeNum,
        treatyReTime: Date.now(),
      });

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新球员生涯数据失败: ${updateResult.message}`, updateResult.code);
      }

      this.logger.log(`球员续约成功: ${heroId}, 新合约天数: ${newContractDays}, 生涯次数: ${newLifeNum}`);

      return XResultUtils.ok({
        heroId,
        oldContractDays: hero.contractDays,
        newContractDays,
        addedDays: days,
        lifeNum: newLifeNum,
        maxLifeNum,
        cost: renewalCost,
        renewalTime: Date.now(),
      });
    }, { reason: 'add_hero_career_days', metadata: { heroId, days } });
  }
  

  /**
   * 计算续约费用
   */
  private calculateCareerRenewalCost(level: number, quality: number, days: number): any {
    // 基础费用根据等级和品质计算
    const baseCost = level * quality * 100;

    // 天数费用
    const daysCost = Math.round(days * 2);

    // 总费用
    const totalGold = baseCost + daysCost;

    return {
      gold: totalGold,
      items: [],
    };
  }

  /**
   * 检查并处理球员退役
   * Controller层已验证参数，无需重复验证
   */
  async checkAndProcessRetirement(characterId: string): Promise<XResult<string[]>> {
    const heroesResult = await this.heroRepository.findByCharacterId(characterId);
    if (XResultUtils.isFailure(heroesResult)) {
      return XResultUtils.error(`获取角色球员列表失败: ${heroesResult.message}`, heroesResult.code);
    }

    const heroes = heroesResult.data || [];
    const retiredHeroes = [];

    for (const hero of heroes) {
      if (this.shouldRetire(hero)) {
        const retirementResult = await this.processHeroRetirement(hero.heroId);
        if (XResultUtils.isSuccess(retirementResult)) {
          retiredHeroes.push(hero.heroId);
        } else {
          this.logger.warn(`处理球员退役失败: ${hero.heroId}, ${retirementResult.message}`);
        }
      }
    }

    if (retiredHeroes.length > 0) {
      this.logger.log(`处理球员退役: ${characterId}, 退役球员: ${retiredHeroes.join(', ')}`);
    }

    return XResultUtils.ok(retiredHeroes);
  }

  /**
   * 判断球员是否应该退役
   */
  private shouldRetire(hero: any): boolean {
    // 退役条件：合约到期且生涯次数达到上限
    const maxLifeNum = this.calculateMaxLifeNum(hero.quality);
    return hero.contractDays <= 0 && hero.lifeNum >= maxLifeNum && !hero.isRetired;
  }

  /**
   * 处理球员退役
   */
  private async processHeroRetirement(heroId: string): Promise<XResult<void>> {
    const updateResult = await this.heroRepository.update(heroId, {
      isRetired: true,
      retirementTime: Date.now(),
      isInLineup: false, // 退役球员自动离开阵容
      isLocked: false, // 解除锁定
    });

    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(`更新球员退役状态失败: ${updateResult.message}`, updateResult.code);
    }

    // 通知Character服务更新阵容（基于old项目退役逻辑）
    const formationUpdateResult = await this.notifyFormationUpdate(heroId);
    if (XResultUtils.isFailure(formationUpdateResult)) {
      this.logger.warn(`通知阵容更新失败: ${formationUpdateResult.message}`);
    }

    // 触发退役相关任务和成就（基于old项目TARGET_TYPE）
    const tasksResult = await this.triggerRetirementTasks(heroId);
    if (XResultUtils.isFailure(tasksResult)) {
      this.logger.warn(`触发退役任务失败: ${tasksResult.message}`);
    }

    this.logger.log(`球员退役处理完成: ${heroId}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 获取球员生涯信息
   * Controller层已验证参数，无需重复验证
   */
  async getHeroCareerInfo(heroId: string): Promise<XResult<any>> {
    const heroResult = await this.heroRepository.findById(heroId);
    if (XResultUtils.isFailure(heroResult)) {
      return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
    }

    const hero = heroResult.data;
    if (!hero) {
      return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
    }

    const maxLifeNum = this.calculateMaxLifeNum(hero.quality);
    const canRenew = hero.contractDays <= 30 && hero.lifeNum < maxLifeNum && !hero.isRetired;
    const willRetire = hero.contractDays <= 0 && hero.lifeNum >= maxLifeNum;
    const daysUntilRetirement = hero.contractDays;

    const careerInfo = {
      heroId,
      contractDays: hero.contractDays,
      lifeNum: hero.lifeNum,
      maxLifeNum,
      canRenew,
      willRetire,
      isRetired: hero.isRetired,
      retirementTime: hero.retirementTime,
      daysUntilRetirement,
      renewalCost: canRenew ? this.calculateCareerRenewalCost(hero.level, hero.quality, 365) : null,
      careerStats: {
        totalMatches: hero.battleNum || 0,
        totalGoals: hero.goals || 0,
        totalAssists: hero.assists || 0,
        averageRating: hero.averageRating || 0,
      },
    };

    return XResultUtils.ok(careerInfo);
  }

  /**
   * 批量检查合约到期
   * Controller层已验证参数，无需重复验证
   */
  async checkContractExpiration(characterId: string): Promise<XResult<any>> {
    const heroesResult = await this.heroRepository.findByCharacterId(characterId);
    if (XResultUtils.isFailure(heroesResult)) {
      return XResultUtils.error(`获取角色球员列表失败: ${heroesResult.message}`, heroesResult.code);
    }

    const heroes = heroesResult.data || [];

    const expiringHeroes = heroes.filter(hero =>
      hero.contractDays <= 7 && hero.contractDays > 0 && !hero.isRetired
    );

    const expiredHeroes = heroes.filter(hero =>
      hero.contractDays <= 0 && !hero.isRetired
    );

    const retirementCandidates = expiredHeroes.filter(hero => {
      const maxLifeNum = this.calculateMaxLifeNum(hero.quality);
      return hero.lifeNum >= maxLifeNum;
    });

    const contractExpirationData = {
      expiringHeroes: expiringHeroes.map(hero => ({
        heroId: hero.heroId,
        name: hero.name,
        contractDays: hero.contractDays,
        lifeNum: hero.lifeNum,
        maxLifeNum: this.calculateMaxLifeNum(hero.quality),
      })),
      expiredHeroes: expiredHeroes.map(hero => ({
        heroId: hero.heroId,
        name: hero.name,
        lifeNum: hero.lifeNum,
        maxLifeNum: this.calculateMaxLifeNum(hero.quality),
        canRenew: hero.lifeNum < this.calculateMaxLifeNum(hero.quality),
      })),
      retirementCandidates: retirementCandidates.map(hero => ({
        heroId: hero.heroId,
        name: hero.name,
        lifeNum: hero.lifeNum,
      })),
      summary: {
        totalHeroes: heroes.length,
        expiringCount: expiringHeroes.length,
        expiredCount: expiredHeroes.length,
        retirementCount: retirementCandidates.length,
      },
    };

    return XResultUtils.ok(contractExpirationData);
  }

  // ==================== 疲劳恢复定时任务系统 ====================

  /**
   * 处理所有球员的疲劳恢复（定时任务）
   * 对应old项目中的calcHeroFatigueReTime方法
   * 适配Result模式，提供完整的错误处理和业务逻辑验证
   */
  async processFatigueRecovery(): Promise<XResult<void>> {
    // 获取所有有疲劳值的球员
    const fatigueHeroesResult = await this.heroRepository.findHeroesWithFatigue();
    if (XResultUtils.isFailure(fatigueHeroesResult)) {
      return XResultUtils.error(`获取疲劳球员列表失败: ${fatigueHeroesResult.message}`, fatigueHeroesResult.code);
    }

    const fatigueHeroes = fatigueHeroesResult.data || [];

    if (fatigueHeroes.length === 0) {
      this.logger.debug('没有需要恢复疲劳的球员');
      return XResultUtils.ok(undefined);
    }

    const currentTime = Date.now();
    const recoveryUpdates = [];

    for (const hero of fatigueHeroes) {
      // 验证球员数据完整性
      if (!hero.heroId || hero.fatigue === undefined || hero.fatigue === null) {
        this.logger.warn(`球员数据不完整，跳过疲劳恢复: ${hero.heroId || 'unknown'}`);
        continue;
      }

      if (hero.fatigue <= 0 || hero.reTimeFatigue <= 0) {
        continue;
      }

      // 计算时间差（小时）
      const hoursPassed = Math.floor((currentTime - hero.reTimeFatigue) / (60 * 60 * 1000));

      if (hoursPassed <= 0) {
        continue;
      }

      // 计算疲劳恢复
      const recoveryRate = this.calculateFatigueRecoveryRate(hero);
      const fatigueRecovery = Math.floor(hoursPassed * recoveryRate);

      if (fatigueRecovery > 0) {
        const newFatigue = Math.max(0, hero.fatigue - fatigueRecovery);
        const newFatigueRatio = this.calculateFatigueRatio(newFatigue);

        recoveryUpdates.push({
          heroId: hero.heroId,
          oldFatigue: hero.fatigue,
          newFatigue,
          newFatigueRatio,
          reTimeFatigue: newFatigue > 0 ? currentTime : 0,
        });
      }
    }

    // 批量更新疲劳值
    if (recoveryUpdates.length > 0) {
      const batchUpdateResult = await this.batchUpdateFatigueRecovery(recoveryUpdates);
      if (XResultUtils.isFailure(batchUpdateResult)) {
        this.logger.warn(`批量更新疲劳恢复部分失败: ${batchUpdateResult.message}`);
      }

      this.logger.log(`疲劳恢复处理完成: ${recoveryUpdates.length}个球员`);
    } else {
      this.logger.debug('没有球员需要进行疲劳恢复');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 计算疲劳恢复速率
   */
  private calculateFatigueRecoveryRate(hero: any): number {
    // 基础恢复速率：每小时恢复1点疲劳
    let baseRate = 1;

    // 品质加成：品质越高恢复越快
    const qualityBonus = hero.quality * 0.1;

    // 等级加成：等级越高恢复越快
    const levelBonus = hero.level * 0.01;

    // 治疗状态加成
    const treatmentBonus = hero.isTreat ? 2 : 0;

    return baseRate + qualityBonus + levelBonus + treatmentBonus;
  }

  /**
   * 批量更新疲劳恢复
   */
  private async batchUpdateFatigueRecovery(updates: any[]): Promise<XResult<void>> {
    const updatePromises = updates.map(async (update) => {
      const updateResult = await this.heroRepository.update(update.heroId, {
        fatigue: update.newFatigue,
        fatigueRatio: update.newFatigueRatio,
        reTimeFatigue: update.reTimeFatigue,
      });

      if (XResultUtils.isFailure(updateResult)) {
        this.logger.warn(`更新球员疲劳恢复失败: ${update.heroId}, ${updateResult.message}`);
      }

      return updateResult;
    });

    const results = await Promise.all(updatePromises);
    const failedCount = results.filter(result => XResultUtils.isFailure(result)).length;

    if (failedCount > 0) {
      this.logger.warn(`批量更新疲劳恢复部分失败: ${failedCount}/${updates.length}`);
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 处理合约天数递减（每日任务）
   * 批量处理所有球员的合约天数递减和退役检查
   */
  async processContractDayDecrement(): Promise<XResult<void>> {
    // 获取所有未退役的球员
    const activeHeroesResult = await this.heroRepository.findActiveHeroes();
    if (XResultUtils.isFailure(activeHeroesResult)) {
      return XResultUtils.error(`获取活跃球员列表失败: ${activeHeroesResult.message}`, activeHeroesResult.code);
    }

    const activeHeroes = activeHeroesResult.data || [];
    const contractUpdates = [];
    const retirementCandidates = [];

    for (const hero of activeHeroes) {
      if (hero.contractDays > 0) {
        const newContractDays = hero.contractDays - 1;
        contractUpdates.push({
          heroId: hero.heroId,
          contractDays: newContractDays,
        });

        // 检查是否需要退役
        if (newContractDays <= 0) {
          const maxLifeNum = this.calculateMaxLifeNum(hero.quality);
          if (hero.lifeNum >= maxLifeNum) {
            retirementCandidates.push(hero.heroId);
          }
        }
      }
    }

    // 批量更新合约天数
    if (contractUpdates.length > 0) {
      const batchUpdateResult = await this.batchUpdateContractDays(contractUpdates);
      if (XResultUtils.isFailure(batchUpdateResult)) {
        this.logger.warn(`批量更新合约天数部分失败: ${batchUpdateResult.message}`);
      }
    }

    // 处理退役
    if (retirementCandidates.length > 0) {
      for (const heroId of retirementCandidates) {
        const retirementResult = await this.processHeroRetirement(heroId);
        if (XResultUtils.isFailure(retirementResult)) {
          this.logger.warn(`处理球员退役失败: ${heroId}, ${retirementResult.message}`);
        }
      }
    }

    this.logger.log(`合约天数处理完成: ${contractUpdates.length}个球员, 退役: ${retirementCandidates.length}个球员`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 批量更新合约天数
   */
  private async batchUpdateContractDays(updates: any[]): Promise<XResult<void>> {
    const updatePromises = updates.map(async (update) => {
      const updateResult = await this.heroRepository.update(update.heroId, {
        contractDays: update.contractDays,
      });

      if (XResultUtils.isFailure(updateResult)) {
        this.logger.warn(`更新球员合约天数失败: ${update.heroId}, ${updateResult.message}`);
      }

      return updateResult;
    });

    const results = await Promise.all(updatePromises);
    const failedCount = results.filter(result => XResultUtils.isFailure(result)).length;

    if (failedCount > 0) {
      this.logger.warn(`批量更新合约天数部分失败: ${failedCount}/${updates.length}`);
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 获取疲劳恢复统计
   */
  async getFatigueRecoveryStats(): Promise<XResult<any>> {
    const fatigueHeroesResult = await this.heroRepository.findHeroesWithFatigue();
    if (XResultUtils.isFailure(fatigueHeroesResult)) {
      return XResultUtils.error(`获取疲劳球员列表失败: ${fatigueHeroesResult.message}`, fatigueHeroesResult.code);
    }

    const fatigueHeroes = fatigueHeroesResult.data || [];

    const stats = {
      totalFatigueHeroes: fatigueHeroes.length,
      highFatigueHeroes: fatigueHeroes.filter(h => h.fatigue >= 80).length,
      mediumFatigueHeroes: fatigueHeroes.filter(h => h.fatigue >= 40 && h.fatigue < 80).length,
      lowFatigueHeroes: fatigueHeroes.filter(h => h.fatigue > 0 && h.fatigue < 40).length,
      inTreatmentHeroes: fatigueHeroes.filter(h => h.isTreat).length,
      averageFatigue: fatigueHeroes.length > 0 ?
        Math.round(fatigueHeroes.reduce((sum, h) => sum + h.fatigue, 0) / fatigueHeroes.length) : 0,
    };

    return XResultUtils.ok(stats);
  }

  /**
   * 检查角色金币
   * 基于old项目: 金币检查逻辑
   */
  private async checkCharacterGold(characterId: string, requiredAmount: number): Promise<XResult<any>> {
    // 直接调用Character服务检查金币（基于old项目checkResourceIsEnough）
    const result = await this.characterService.getCharacterInfo(characterId);

    if (XResultUtils.isFailure(result)) {
      this.logger.error(`获取角色信息失败: ${characterId}, ${result.message}`);
      const defaultGoldCheck = {
        sufficient: false,
        current: 0,
      };
      return XResultUtils.ok(defaultGoldCheck);
    }

    const currentGold = result.data?.gold || 0;
    const goldCheckResult = {
      sufficient: currentGold >= requiredAmount,
      current: currentGold,
    };

    return XResultUtils.ok(goldCheckResult);
  }

  /**
   * 扣除角色金币
   * 基于old项目: 金币扣除逻辑
   */
  private async deductCharacterGold(characterId: string, amount: number): Promise<XResult<boolean>> {
    // 直接调用Character服务扣除金币（基于old项目deductMoney）
    const result = await this.characterService.deductCurrency({
      characterId,
      currencyType: 'gold',
      amount: amount,
      reason: 'hero_breakthrough'
    });

    if (XResultUtils.isFailure(result)) {
      this.logger.error(`扣除角色金币失败: ${characterId}, 数量: ${amount}, 错误: ${result.message}`);
      return XResultUtils.ok(false);
    }

    this.logger.debug(`扣除角色金币成功: ${characterId}, 数量: ${amount}`);
    return XResultUtils.ok(true);
  }

  /**
   * 通知阵容更新
   * 基于old项目: 退役球员需要从阵容中移除
   */
  private async notifyFormationUpdate(heroId: string): Promise<XResult<void>> {
    // 获取球员信息
    const heroResult = await this.heroRepository.findById(heroId);
    if (XResultUtils.isFailure(heroResult)) {
      this.logger.error(`获取球员信息失败: ${heroId}, ${heroResult.message}`);
      return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
    }

    const hero = heroResult.data;
    if (!hero) {
      this.logger.error(`球员不存在: ${heroId}`);
      return XResultUtils.error('球员不存在', 'HERO_NOT_FOUND');
    }

    // 使用内部FormationService更新阵容
    const result = await this.formationService.removeHeroFromAllLineups(hero.characterId, heroId);

    if (XResultUtils.isFailure(result)) {
      this.logger.warn(`移除球员失败: ${result.message}`);
      return XResultUtils.error(`移除球员失败: ${result.message}`, result.code);
    }

    this.logger.log(`球员已从阵容中移除: ${heroId}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 触发退役任务
   * 基于old项目: TARGET_TYPE退役任务触发
   */
  private async triggerRetirementTasks(heroId: string): Promise<XResult<void>> {
    // 获取退役球员信息
    const heroResult = await this.heroRepository.findById(heroId);
    if (XResultUtils.isFailure(heroResult)) {
      this.logger.error(`获取退役球员信息失败: ${heroId}, ${heroResult.message}`);
      return XResultUtils.error(`获取退役球员信息失败: ${heroResult.message}`, heroResult.code);
    }

    const hero = heroResult.data;
    if (!hero) {
      this.logger.error(`退役球员不存在: ${heroId}`);
      return XResultUtils.error('退役球员不存在', 'HERO_NOT_FOUND');
    }

    // 调用Activity服务触发退役任务
    const taskResult = await this.callMicroservice(
      MICROSERVICE_NAMES.ACTIVITY_SERVICE,
      'task.triggerTask',
      {
        characterId: hero.characterId,
        triggerType: 'HERO_RETIREMENT',
        arg1: hero.quality,
        arg2: hero.lifeNum,
        heroId
      }
    );

    if (XResultUtils.isSuccess(taskResult)) {
      this.logger.log(`退役任务触发成功: ${heroId}`);
    } else {
      this.logger.warn(`退役任务触发失败: ${heroId}, ${taskResult.message}`);
    }

    // 调用Activity服务触发退役成就
    const achievementResult = await this.callMicroservice(
      MICROSERVICE_NAMES.ACTIVITY_SERVICE,
      'achievement.triggerAchievement',
      {
        characterId: hero.characterId,
        achievementType: 'HERO_RETIREMENT',
        heroQuality: hero.quality,
        heroId
      }
    );

    if (XResultUtils.isSuccess(achievementResult)) {
      this.logger.log(`退役成就触发成功: ${heroId}`);
    } else {
      this.logger.warn(`退役成就触发失败: ${heroId}, ${achievementResult.message}`);
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 转换old项目属性格式到新格式
   * 基于old项目: 属性名称映射
   */
  private convertOldAttributesToNew(oldAttributes: any): any {
    return {
      speed: oldAttributes.speed || oldAttributes.Speed || 0,
      shooting: oldAttributes.shooting || oldAttributes.Shooting || 0,
      passing: oldAttributes.passing || oldAttributes.Passing || 0,
      defending: oldAttributes.defending || oldAttributes.Defending || 0,
      dribbling: oldAttributes.dribbling || oldAttributes.Dribbling || 0,
      physicality: oldAttributes.physicality || oldAttributes.Physicality || 0,
      goalkeeping: oldAttributes.goalkeeping || oldAttributes.Goalkeeping || 0,
    };
  }


}
