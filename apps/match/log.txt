[Nest] 7072  - 2025/09/28 15:30:20   DEBUG [BattleService] 战斗回放数据: {
  "battleRecord": {
    "battleRoundInfo": [
      {
        "eventTime": 2000,
        "moraleA": 300,
        "moraleB": 381,
        "attackerType": "teamA",
        "attackMode": 1,
        "scoreA": 0,
        "scoreB": 0,
        "periodInfo": [
          {
            "A1Info": {
              "heroId": "hero_1759044618633_u1tsqk5u9",
              "attrType1": 1,
              "attrValue1": 43,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 42,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 1000,
            "actionResult": 1,
            "moraleA": 300,
            "moraleB": 381,
            "skillEffectList": []
          },
          {
            "A1Info": {
              "heroId": "hero_1759044618633_u1tsqk5u9",
              "attrType1": 1,
              "attrValue1": 43,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 42,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 419,
            "actionResult": 1,
            "moraleA": 300,
            "moraleB": 381,
            "skillEffectList": []
          },
          {
            "A1Info": {
              "heroId": "hero_1759044618633_u1tsqk5u9",
              "attrType1": 1,
              "attrValue1": 43,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 42,
              "addValue2": 0
            },
            "A2Info": {
              "heroId": "hero_1759044618604_l0nfg6sk3",
              "attrType1": 3,
              "attrValue1": 36,
              "addValue1": 0,
              "attrType2": 5,
              "attrValue2": 41,
              "addValue2": 0
            },
            "BInfo": {
              "heroId": "enemy_90101_90001",
              "attrType1": 4,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 0,
              "addValue2": 0
            },
            "GKInfo": {
              "heroId": "enemy_90101_90003",
              "attrType1": 7,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 0,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 192,
            "actionResult": 0,
            "moraleA": 300,
            "moraleB": 381,
            "skillEffectList": [
              {
                "skillId": 11105,
                "teamType": "teamA",
                "addPer": 6,
                "heroId": "hero_1759044618604_l0nfg6sk3"
              }
            ]
          }
        ],
        "moraleSlotA": 1000,
        "moraleSlotB": 0,
        "comments": []
      },
      {
        "eventTime": 4000,
        "moraleA": 300,
        "moraleB": 381,
        "attackerType": "teamA",
        "attackMode": 1,
        "scoreA": 0,
        "scoreB": 0,
        "periodInfo": [
          {
            "A1Info": {
              "heroId": "hero_1759044618630_8phiu5vsc",
              "attrType1": 1,
              "attrValue1": 44,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 42,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 1000,
            "actionResult": 1,
            "moraleA": 300,
            "moraleB": 381,
            "skillEffectList": []
          },
          {
            "A1Info": {
              "heroId": "hero_1759044618630_8phiu5vsc",
              "attrType1": 1,
              "attrValue1": 44,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 42,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 419,
            "actionResult": 1,
            "moraleA": 300,
            "moraleB": 381,
            "skillEffectList": []
          },
          {
            "A1Info": {
              "heroId": "hero_1759044618630_8phiu5vsc",
              "attrType1": 1,
              "attrValue1": 44,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 42,
              "addValue2": 0
            },
            "A2Info": {
              "heroId": "hero_1759044618604_l0nfg6sk3",
              "attrType1": 3,
              "attrValue1": 36,
              "addValue1": 0,
              "attrType2": 5,
              "attrValue2": 41,
              "addValue2": 0
            },
            "BInfo": {
              "heroId": "enemy_90101_90002",
              "attrType1": 4,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 0,
              "addValue2": 0
            },
            "GKInfo": {
              "heroId": "enemy_90101_90003",
              "attrType1": 7,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 0,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 192,
            "actionResult": 0,
            "moraleA": 300,
            "moraleB": 381,
            "skillEffectList": [
              {
                "skillId": 11105,
                "teamType": "teamA",
                "addPer": 6,
                "heroId": "hero_1759044618604_l0nfg6sk3"
              }
            ]
          }
        ],
        "moraleSlotA": 1000,
        "moraleSlotB": 0,
        "comments": []
      }
    ],
    "totalTime": 6000,
    "totalRounds": 3
  },
  "teamAData": {
    "characterId": "char_server_001_e99a9ccc_itln92l5e",
    "teamType": "teamA",
    "teamName": "队伍char_server_001_e99a9ccc_itln92l5e",
    "rating": 0,
    "formationId": 442101,
    "attackTacticId": 101,
    "defendTacticId": 101,
    "heroes": [
      {
        "heroId": "hero_1759044618600_x1878qa27",
        "name": "巴塔查亚",
        "position": "GK",
        "rating": 1
      },
      {
        "heroId": "hero_1759044618604_l0nfg6sk3",
        "name": "弗赖马尼斯",
        "position": "DC",
        "rating": 1
      },
      {
        "heroId": "hero_1759044618609_9vyd3vioa",
        "name": "加文.霍伊特",
        "position": "DL",
        "rating": 1
      },
      {
        "heroId": "hero_1759044618614_tlr5fn8er",
        "name": "贾斯汀努森",
        "position": "DR",
        "rating": 1
      },
      {
        "heroId": "hero_1759044618617_yjl9dxd6m",
        "name": "哈布拉",
        "position": "MC",
        "rating": 1
      },
      {
        "heroId": "hero_1759044618621_fyd6o3mko",
        "name": "马塞兰",
        "position": "ML",
        "rating": 1
      },
      {
        "heroId": "hero_1759044618625_2hi0gvj5j",
        "name": "斯图波尔",
        "position": "MR",
        "rating": 1
      },
      {
        "heroId": "hero_1759044618627_qnbor41he",
        "name": "佩斯科",
        "position": "WL",
        "rating": 1
      },
      {
        "heroId": "hero_1759044618630_8phiu5vsc",
        "name": "拉兹丁什",
        "position": "WR",
        "rating": 1
      },
      {
        "heroId": "hero_1759044618633_u1tsqk5u9",
        "name": "雷加莱斯",
        "position": "ST",
        "rating": 1
      },
      {
        "heroId": "hero_1759044618636_3x5n53gm6",
        "name": "莫里斯",
        "position": "AM",
        "rating": 1
      }
    ],
    "attackTacticsState": 0,
    "defendTacticsState": 0,
    "baseAttackValue": 0,
    "baseDefendValue": 0,
    "attackValueFactor": 1,
    "defendValueFactor": 1
  },
  "teamBData": {
    "characterId": "AI",
    "teamType": "teamB",
    "teamName": "红星",
    "rating": 0,
    "formationId": 442201,
    "attackTacticId": 101,
    "defendTacticId": 101,
    "heroes": [
      {
        "heroId": "enemy_90101_90000",
        "name": "未知球员",
        "position": "GK",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90001",
        "name": "未知球员",
        "position": "DC",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90002",
        "name": "未知球员",
        "position": "DC",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90003",
        "name": "未知球员",
        "position": "DL",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90004",
        "name": "未知球员",
        "position": "DR",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90005",
        "name": "未知球员",
        "position": "DM",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90006",
        "name": "未知球员",
        "position": "MC",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90007",
        "name": "未知球员",
        "position": "MC",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90008",
        "name": "未知球员",
        "position": "AM",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90009",
        "name": "未知球员",
        "position": "ST",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90010",
        "name": "未知球员",
        "position": "ST",
        "rating": 1
      }
    ],
    "attackTacticsState": 0,
    "defendTacticsState": 0,
    "baseAttackValue": 0,
    "baseDefendValue": 0,
    "attackValueFactor": 1,
    "defendValueFactor": 1
  }
}