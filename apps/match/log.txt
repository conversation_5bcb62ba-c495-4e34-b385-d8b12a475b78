[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [BattleDataService] {
  "heroId": "hero_1759057854313_hy50ul2x5",
  "name": "巴塔查亚",
  "resId": 1183,
  "level": 1,
  "position": "GK",
  "mainPosition": "GK",
  "breakLevel": 0,
  "starLevel": 0,
  "evolutionStage": 0,
  "attributes": {
    "speed": {
      "base": 45,
      "cur": 45
    },
    "jumping": {
      "base": 35,
      "cur": 35
    },
    "strength": {
      "base": 38,
      "cur": 38
    },
    "stamina": {
      "base": 38,
      "cur": 38
    },
    "explosiveForce": {
      "base": 45,
      "cur": 45
    },
    "finishing": {
      "base": 45,
      "cur": 45
    },
    "dribbling": {
      "base": 41,
      "cur": 41
    },
    "passing": {
      "base": 35,
      "cur": 35
    },
    "longPassing": {
      "base": 36,
      "cur": 36
    },
    "longShots": {
      "base": 36,
      "cur": 36
    },
    "heading": {
      "base": 42,
      "cur": 42
    },
    "volleys": {
      "base": 44,
      "cur": 44
    },
    "standingTackle": {
      "base": 36,
      "cur": 36
    },
    "slidingTackle": {
      "base": 39,
      "cur": 39
    },
    "penalties": {
      "base": 35,
      "cur": 35
    },
    "cornerKick": {
      "base": 38,
      "cur": 38
    },
    "freeKick": {
      "base": 40,
      "cur": 40
    },
    "attack": {
      "base": 38,
      "cur": 38
    },
    "save": {
      "base": 45,
      "cur": 45
    },
    "resistanceDamage": {
      "base": 36,
      "cur": 36
    }
  },
  "attack": 42,
  "defend": 37,
  "speed": 45,
  "power": 38,
  "technique": 38,
  "skills": [
    {
      "skillId": 20023,
      "level": 1
    },
    {
      "skillId": 40013,
      "level": 1
    },
    {
      "skillId": 20015,
      "level": 1
    },
    {
      "skillId": 20005,
      "level": 1
    },
    {
      "skillId": 11163,
      "level": 1
    }
  ],
  "activeSkills": [
    20023,
    40013,
    20015,
    20005
  ],
  "currentStamina": 38,
  "morale": 500,
  "fatigue": 0,
  "isEnemy": false,
  "rating": 0
}
[Nest] 10620  - 2025/09/28 19:10:56     LOG [BattleDataService] 转换后的战斗数据: 球员数量=11, 阵型=442101
[Nest] 10620  - 2025/09/28 19:10:56     LOG [LeagueService] 玩家战斗数据转换完成: 球员数量=11
[Nest] 10620  - 2025/09/28 19:10:56     LOG [LeagueService] 开始生成敌方数据: {"defenseId":1101,"offensiveId":101,"teamId":90101,"wordId":0,"iconId":10020,"leagueId":90100,"consume":5,"exp1":388,"exp2":10,"exp3":10,"formation":442201,"lv":0,"position":1,"reward1":1,"reward2":5,"reward3":11006,"rewardNum1":100000,"rewardNum2":0,"rewardNum3":1,"rewardType1":0,"rewardType2":0,"rewardType3":0,"id":1,"name":"红星"}
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache miss: global:config:Team:all
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loading config: Team from Team.json
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loaded 5104 items for Team
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [RedisService] Set key: global:config:Team:all (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache set: global:config:Team:all, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:56     LOG [LeagueService] 敌方数据生成成功: 球员数量=11, 阵型=442201
[Nest] 10620  - 2025/09/28 19:10:56     LOG [LeagueService] 敌方战斗数据生成完成: 球员数量=11
[Nest] 10620  - 2025/09/28 19:10:56     LOG [BattleService] 业务操作开始: BattleService_1759057856484_tu9zx90cd
[Nest] 10620  - 2025/09/28 19:10:56     LOG [BattleService] Object:
{
  "reason": "pve_battle",
  "metadata": {
    "characterId": "char_server_001_d5fdcbbd_n69al3rof",
    "battleType": "PVE_LEAGUE"
  }
}

[Nest] 10620  - 2025/09/28 19:10:56     LOG [BattleService] PVE战斗开始: char_server_001_d5fdcbbd_n69al3rof, 类型: PVE_LEAGUE
[Nest] 10620  - 2025/09/28 19:10:56     LOG [BattleRepository] 文档创建成功: BattleRepository
[Nest] 10620  - 2025/09/28 19:10:56     LOG [BattleEngine] 🔧 开始战斗计算 - 类型: PVE_LEAGUE, 队伍数: 2
[Nest] 10620  - 2025/09/28 19:10:56     LOG [BattleInitializer] 初始化战斗数据 - 类型: PVE_LEAGUE, 队伍数: 2
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [BattleInitializer] 队伍A球员数量: 11, 队伍B球员数量: 11
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache redis hit: global:config:TeamFormation:442101
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position1
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position2
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position3
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position4
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position5
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position6
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position7
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position8
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position9
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position10
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 未知位置: AM
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [BattleAttributeCalculator] 阵型加成计算: 阵型=442101, 攻击加成=0, 防守加成=0
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache miss: global:config:Tactic:101
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loading config: Tactic from Tactic.json
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loaded 240 items for Tactic
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [RedisService] Set key: global:config:Tactic:101 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache set: global:config:Tactic:101, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [BattleAttributeCalculator] 战术加成计算: 战术=101, 攻击加成=0, 防守加成=0
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [BattleAttributeCalculator] 综合战力计算: 队伍char_server_001_d5fdcbbd_n69al3rof - 基础攻击=451, 基础防守=435, 最终攻击=451, 最终防守=435
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache miss: global:config:TeamFormation:442201
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loading config: TeamFormation from TeamFormation.json
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loaded 320 items for TeamFormation
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [RedisService] Set key: global:config:TeamFormation:442201 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache set: global:config:TeamFormation:442201, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position1
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position2
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position2
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position3
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position4
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 未知位置: DM
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position5
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position5
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 未知位置: AM
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position10
[Nest] 10620  - 2025/09/28 19:10:56    WARN [BattleAttributeCalculator] 阵型配置中缺少位置配置: Position10
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [BattleAttributeCalculator] 阵型加成计算: 阵型=442201, 攻击加成=0, 防守加成=0
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache memory hit: global:config:Tactic:101
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [BattleAttributeCalculator] 战术加成计算: 战术=101, 攻击加成=0, 防守加成=0
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [BattleAttributeCalculator] 综合战力计算: 红星 - 基础攻击=849, 基础防守=959, 最终攻击=849, 最终防守=959
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [BattleAttributeCalculator] 士气计算完成: teamA=300, teamB=381
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:20023
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [RedisService] Set key: global:config:HeroSkill:20023 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache set: global:config:HeroSkill:20023, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854313_hy50ul2x5, 技能ID: 20023
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:40013
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [RedisService] Set key: global:config:HeroSkill:40013 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache set: global:config:HeroSkill:40013, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854313_hy50ul2x5, 技能ID: 40013
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:20015
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [RedisService] Set key: global:config:HeroSkill:20015 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache set: global:config:HeroSkill:20015, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854313_hy50ul2x5, 技能ID: 20015
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:20005
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [RedisService] Set key: global:config:HeroSkill:20005 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache set: global:config:HeroSkill:20005, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854313_hy50ul2x5, 技能ID: 20005
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [BattleSkillSystem] 球员hero_1759057854313_hy50ul2x5技能初始化完成，激活技能数量: 4
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:10013
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [RedisService] Set key: global:config:HeroSkill:10013 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache set: global:config:HeroSkill:10013, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854314_wl60vnj69, 技能ID: 10013
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:21004
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [RedisService] Set key: global:config:HeroSkill:21004 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache set: global:config:HeroSkill:21004, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [BattleSkillSystem] 瞬时技能初始化: hero_1759057854314_wl60vnj69, 技能ID: 21004
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:11105
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [RedisService] Set key: global:config:HeroSkill:11105 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache set: global:config:HeroSkill:11105, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [BattleSkillSystem] 瞬时技能初始化: hero_1759057854314_wl60vnj69, 技能ID: 11105
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:10135
[Nest] 10620  - 2025/09/28 19:10:56   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:10135 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:10135, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854314_wl60vnj69, 技能ID: 10135
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 球员hero_1759057854314_wl60vnj69技能初始化完成，激活技能数量: 4
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:11003
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:11003 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:11003, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 瞬时技能初始化: hero_1759057854316_8xojh2usg, 技能ID: 11003
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:10124
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:10124 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:10124, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854316_8xojh2usg, 技能ID: 10124
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:10095
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:10095 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:10095, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854316_8xojh2usg, 技能ID: 10095
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:11095
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:11095 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:11095, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 瞬时技能初始化: hero_1759057854316_8xojh2usg, 技能ID: 11095
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 球员hero_1759057854316_8xojh2usg技能初始化完成，激活技能数量: 4
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:10063
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:10063 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:10063, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854318_hlzja3j4w, 技能ID: 10063
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache redis hit: global:config:HeroSkill:10004
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854318_hlzja3j4w, 技能ID: 10004
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:11065
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:11065 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:11065, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 瞬时技能初始化: hero_1759057854318_hlzja3j4w, 技能ID: 11065
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:10125
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:10125 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:10125, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854318_hlzja3j4w, 技能ID: 10125
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 球员hero_1759057854318_hlzja3j4w技能初始化完成，激活技能数量: 4
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:10043
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:10043 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:10043, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854321_29m90bspp, 技能ID: 10043
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:11174
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:11174 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:11174, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854321_29m90bspp, 技能ID: 11174
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:11075
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:11075 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:11075, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 瞬时技能初始化: hero_1759057854321_29m90bspp, 技能ID: 11075
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache memory hit: global:config:HeroSkill:11095
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 瞬时技能初始化: hero_1759057854321_29m90bspp, 技能ID: 11095
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 球员hero_1759057854321_29m90bspp技能初始化完成，激活技能数量: 4
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache redis hit: global:config:HeroSkill:10003
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854323_l7zl0e53e, 技能ID: 10003
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:11214
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:11214 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:11214, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854323_l7zl0e53e, 技能ID: 11214
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:11055
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:11055 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:11055, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 瞬时技能初始化: hero_1759057854323_l7zl0e53e, 技能ID: 11055
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:10065
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:10065 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:10065, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854323_l7zl0e53e, 技能ID: 10065
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 球员hero_1759057854323_l7zl0e53e技能初始化完成，激活技能数量: 4
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:10073
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:10073 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:10073, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854324_o8nx3elhi, 技能ID: 10073
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:10054
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:10054 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:10054, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854324_o8nx3elhi, 技能ID: 10054
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:10145
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:10145 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:10145, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854324_o8nx3elhi, 技能ID: 10145
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache memory hit: global:config:HeroSkill:10135
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854324_o8nx3elhi, 技能ID: 10135
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 球员hero_1759057854324_o8nx3elhi技能初始化完成，激活技能数量: 4
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:10083
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:10083 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:10083, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854326_sbxyulaqg, 技能ID: 10083
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache memory hit: global:config:HeroSkill:10124
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854326_sbxyulaqg, 技能ID: 10124
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:11035
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:11035 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:11035, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 瞬时技能初始化: hero_1759057854326_sbxyulaqg, 技能ID: 11035
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:10115
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:10115 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:10115, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854326_sbxyulaqg, 技能ID: 10115
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 球员hero_1759057854326_sbxyulaqg技能初始化完成，激活技能数量: 4
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:10093
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:10093 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:10093, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854328_erazst0yv, 技能ID: 10093
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:10094
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:10094 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:10094, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854328_erazst0yv, 技能ID: 10094
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache memory hit: global:config:HeroSkill:10145
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854328_erazst0yv, 技能ID: 10145
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache memory hit: global:config:HeroSkill:10145
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854328_erazst0yv, 技能ID: 10145
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 球员hero_1759057854328_erazst0yv技能初始化完成，激活技能数量: 4
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache memory hit: global:config:HeroSkill:10063
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854329_0f7532hkf, 技能ID: 10063
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:11024
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:11024 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:11024, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 瞬时技能初始化: hero_1759057854329_0f7532hkf, 技能ID: 11024
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:11045
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:11045 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:11045, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 瞬时技能初始化: hero_1759057854329_0f7532hkf, 技能ID: 11045
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:11015
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [RedisService] Set key: global:config:HeroSkill:11015 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache set: global:config:HeroSkill:11015, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 瞬时技能初始化: hero_1759057854329_0f7532hkf, 技能ID: 11015
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 球员hero_1759057854329_0f7532hkf技能初始化完成，激活技能数量: 4
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache memory hit: global:config:HeroSkill:10073
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [BattleSkillSystem] 持续技能初始化: hero_1759057854330_rjumcpeve, 技能ID: 10073
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:11034
[Nest] 10620  - 2025/09/28 19:10:57   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [RedisService] Set key: global:config:HeroSkill:11034 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheManager] Cache set: global:config:HeroSkill:11034, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 瞬时技能初始化: hero_1759057854330_rjumcpeve, 技能ID: 11034
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheManager] Cache memory hit: global:config:HeroSkill:11035
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 瞬时技能初始化: hero_1759057854330_rjumcpeve, 技能ID: 11035
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheManager] Cache miss: global:config:HeroSkill:11025
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [ConfigLoader] Loading config: HeroSkill from FootballerSkill.json
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [ConfigLoader] Loaded 902 items for HeroSkill
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [RedisService] Set key: global:config:HeroSkill:11025 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheManager] Cache set: global:config:HeroSkill:11025, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 瞬时技能初始化: hero_1759057854330_rjumcpeve, 技能ID: 11025
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 球员hero_1759057854330_rjumcpeve技能初始化完成，激活技能数量: 4
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 队伍0技能初始化完成，球员数量: 11
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 球员enemy_90101_90000没有激活技能
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 球员enemy_90101_90001没有激活技能
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 球员enemy_90101_90002没有激活技能
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 球员enemy_90101_90003没有激活技能
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 球员enemy_90101_90004没有激活技能
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 球员enemy_90101_90005没有激活技能
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 球员enemy_90101_90006没有激活技能
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 球员enemy_90101_90007没有激活技能
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 球员enemy_90101_90008没有激活技能
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 球员enemy_90101_90009没有激活技能
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 球员enemy_90101_90010没有激活技能
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 队伍1技能初始化完成，球员数量: 11
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能系统初始化完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleInitializer] 技能信息初始化完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleInitializer] 回合结果数据初始化完成
[Nest] 10620  - 2025/09/28 19:10:58     LOG [BattleInitializer] 战斗数据初始化完成 - 队伍A: 11人, 队伍B: 11人
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 🔧 应用特殊战斗初始化 - 类型: PVE_LEAGUE
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleInitializer] 队伍teamA球员评分初始化完成，共11名球员
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleInitializer] 角色队伍数据初始化完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleInitializer] 队伍teamB球员评分初始化完成，共11名球员
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleInitializer] 角色队伍数据初始化完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleInitializer] 敌人队伍数据初始化完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleInitializer] PVE战斗数据初始化完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleInitializer] 技能信息初始化完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleInitializer] 回合结果数据初始化完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleInitializer] PVE战斗初始化完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] ✅ PVE基础初始化完成 - 类型: PVE_LEAGUE
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 开始执行战斗回合循环
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleInitializer] 技能信息初始化完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleInitializer] 回合结果数据初始化完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 士气时间计算: A队=1333, B队=1050
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 下次事件: 时间=1050, 发起方=teamB, 总时间=1050
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合开始: 第1回合, 攻击方: teamB
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 选择进攻方式: 推射 (ID: 3)
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择攻击球员: A1, 进攻方式=3, 阶段=0, 球员=enemy_90101_90009
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 发起阶段: 攻击方式=3, 回合=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 发起阶段记录完成: 回合=0, 成功率=1000
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=A1, 阶段=1, 球员=hero_1759057854321_29m90bspp
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 触发技能: 阶段=1, 攻击模式=3, 回合=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能记录生成完成: 持续=2, 瞬时=2, 下次攻击=2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能效果列表填充完成: 阶段=1, 回合=0, 效果数量=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheManager] Cache miss: global:config:AttackMode:3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [ConfigLoader] Loading config: AttackMode from AttackMode.json
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [ConfigLoader] Loaded 9 items for AttackMode
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [RedisService] Set key: global:config:AttackMode:3 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheManager] Cache set: global:config:AttackMode:3, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 推进阶段: 攻击=100, 防守=100, 基础=0.500, 士气=0.081, 技能=0, 最终=0.581, 结果=false
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 突破统计更新: teamB 突破失败, 总突破1, 成功undefined
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 推进阶段记录完成: 回合=0, 成功率=581, 结果=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleCommentSystem] 批量生成评论完成: 0条
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamA回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamB回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合结束: 第1回合处理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 士气时间计算: A队=281, B队=1053
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 下次事件: 时间=281, 发起方=teamA, 总时间=1331
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合开始: 第2回合, 攻击方: teamA
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 选择进攻方式: 抢点 (ID: 4)
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择攻击球员: A1, 进攻方式=4, 阶段=0, 球员=hero_1759057854326_sbxyulaqg
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 发起阶段: 攻击方式=4, 回合=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 发起阶段记录完成: 回合=1, 成功率=1000
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=A1, 阶段=1, 球员=enemy_90101_90004
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 触发技能: 阶段=1, 攻击模式=4, 回合=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能触发: 技能ID=11035, 球员=hero_1759057854326_sbxyulaqg
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能记录添加: 技能ID=11035, 球员=hero_1759057854326_sbxyulaqg, 类型=instant, 回合=1, 阶段=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能记录生成完成: 持续=2, 瞬时=2, 下次攻击=2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 填充技能效果: 技能ID=11035, 效果值=3, 球员=hero_1759057854326_sbxyulaqg, 阶段=1, 回合=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能效果列表填充完成: 阶段=1, 回合=1, 效果数量=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheManager] Cache miss: global:config:AttackMode:4
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [ConfigLoader] Loading config: AttackMode from AttackMode.json
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [ConfigLoader] Loaded 9 items for AttackMode
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [RedisService] Set key: global:config:AttackMode:4 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheManager] Cache set: global:config:AttackMode:4, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 推进阶段: 攻击=100, 防守=100, 基础=0.500, 士气=-0.078, 技能=0, 最终=0.422, 结果=false
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 突破统计更新: teamA 突破失败, 总突破1, 成功undefined
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 推进阶段记录完成: 回合=1, 成功率=422, 结果=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleCommentSystem] 批量生成评论完成: 0条
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamA回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamB回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合结束: 第2回合处理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 士气时间计算: A队=1329, B队=768
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 下次事件: 时间=768, 发起方=teamB, 总时间=2099
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合开始: 第3回合, 攻击方: teamB
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 选择进攻方式: 抢点 (ID: 4)
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择攻击球员: A1, 进攻方式=4, 阶段=0, 球员=enemy_90101_90009
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 发起阶段: 攻击方式=4, 回合=2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 发起阶段记录完成: 回合=2, 成功率=1000
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=A1, 阶段=1, 球员=hero_1759057854321_29m90bspp
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 触发技能: 阶段=1, 攻击模式=4, 回合=2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能记录生成完成: 持续=2, 瞬时=2, 下次攻击=2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=1, round=2, 实际period=1, round=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能效果列表填充完成: 阶段=1, 回合=2, 效果数量=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheManager] Cache memory hit: global:config:AttackMode:4
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 推进阶段: 攻击=100, 防守=100, 基础=0.500, 士气=0.081, 技能=0, 最终=0.581, 结果=false
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 突破统计更新: teamB 突破失败, 总突破2, 成功undefined
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 推进阶段记录完成: 回合=2, 成功率=581, 结果=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleCommentSystem] 批量生成评论完成: 0条
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamA回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamB回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合结束: 第3回合处理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 士气时间计算: A队=557, B队=1050
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 下次事件: 时间=557, 发起方=teamA, 总时间=2656
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合开始: 第4回合, 攻击方: teamA
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 选择进攻方式: 推射 (ID: 3)
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择攻击球员: A1, 进攻方式=3, 阶段=0, 球员=hero_1759057854329_0f7532hkf
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 发起阶段: 攻击方式=3, 回合=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 发起阶段记录完成: 回合=3, 成功率=1000
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=A1, 阶段=1, 球员=enemy_90101_90001
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 触发技能: 阶段=1, 攻击模式=3, 回合=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能触发: 技能ID=11024, 球员=hero_1759057854329_0f7532hkf
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能记录添加: 技能ID=11024, 球员=hero_1759057854329_0f7532hkf, 类型=instant, 回合=3, 阶段=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能记录生成完成: 持续=2, 瞬时=2, 下次攻击=2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=1, round=3, 实际period=1, round=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 填充技能效果: 技能ID=11024, 效果值=5, 球员=hero_1759057854329_0f7532hkf, 阶段=1, 回合=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能效果列表填充完成: 阶段=1, 回合=3, 效果数量=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheManager] Cache memory hit: global:config:AttackMode:3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 推进阶段: 攻击=100, 防守=100, 基础=0.500, 士气=-0.078, 技能=0, 最终=0.422, 结果=true
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 突破统计更新: teamA 突破成功, 总突破2, 成功1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 推进阶段记录完成: 回合=3, 成功率=422, 结果=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择攻击球员: A2, 进攻方式=3, 阶段=2, 球员=hero_1759057854313_hy50ul2x5
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=A1, 阶段=2, 球员=enemy_90101_90001
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=GK, 阶段=2, 球员=enemy_90101_90004
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 触发技能: 阶段=2, 攻击模式=3, 回合=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能触发: 技能ID=11024, 球员=hero_1759057854329_0f7532hkf
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能记录添加: 技能ID=11024, 球员=hero_1759057854329_0f7532hkf, 类型=instant, 回合=3, 阶段=2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能记录生成完成: 持续=2, 瞬时=2, 下次攻击=2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=2, round=3, 实际period=1, round=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=2, round=3, 实际period=1, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 填充技能效果: 技能ID=11024, 效果值=5, 球员=hero_1759057854329_0f7532hkf, 阶段=2, 回合=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能效果列表填充完成: 阶段=2, 回合=3, 效果数量=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 射门阶段: 基础=0.239, 士气=-0.039, 技能=0, 最终=0.200, 结果=false
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 射门阶段记录完成: 回合=3, 成功率=200, 进球=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleCommentSystem] 批量生成评论完成: 0条
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamA回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamB回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合结束: 第4回合处理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 士气时间计算: A队=1342, B队=493
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 下次事件: 时间=493, 发起方=teamB, 总时间=3149
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合开始: 第5回合, 攻击方: teamB
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 选择进攻方式: 推射 (ID: 3)
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择攻击球员: A1, 进攻方式=3, 阶段=0, 球员=enemy_90101_90009
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 发起阶段: 攻击方式=3, 回合=4
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 发起阶段记录完成: 回合=4, 成功率=1000
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=A1, 阶段=1, 球员=hero_1759057854321_29m90bspp
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 触发技能: 阶段=1, 攻击模式=3, 回合=4
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能记录生成完成: 持续=2, 瞬时=2, 下次攻击=2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=1, round=4, 实际period=1, round=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=1, round=4, 实际period=1, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=1, round=4, 实际period=2, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能效果列表填充完成: 阶段=1, 回合=4, 效果数量=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheManager] Cache memory hit: global:config:AttackMode:3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 推进阶段: 攻击=100, 防守=100, 基础=0.500, 士气=0.083, 技能=0, 最终=0.583, 结果=true
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 突破统计更新: teamB 突破成功, 总突破3, 成功1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 推进阶段记录完成: 回合=4, 成功率=583, 结果=1
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: finishing
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'finishing')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: finishing
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'finishing')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: finishing
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'finishing')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: finishing
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'finishing')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: finishing
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'finishing')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: finishing
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'finishing')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: finishing
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'finishing')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: finishing
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'finishing')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: finishing
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'finishing')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: finishing
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'finishing')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: finishing
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'finishing')
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择攻击球员: A2, 进攻方式=3, 阶段=2, 球员=
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=A1, 阶段=2, 球员=hero_1759057854314_wl60vnj69
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=GK, 阶段=2, 球员=hero_1759057854316_8xojh2usg
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 触发技能: 阶段=2, 攻击模式=3, 回合=4
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能记录生成完成: 持续=2, 瞬时=2, 下次攻击=2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=2, round=4, 实际period=1, round=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=2, round=4, 实际period=1, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=2, round=4, 实际period=2, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能效果列表填充完成: 阶段=2, 回合=4, 效果数量=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 射门阶段: 基础=0.565, 士气=0.042, 技能=0, 最终=0.600, 结果=true
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 射门统计更新: teamB 射门次数+1, 当前1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 射门阶段记录完成: 回合=4, 成功率=600, 进球=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleCommentSystem] 批量生成评论完成: 0条
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 进球！teamB 得分，当前比分 0:1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamA回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamB回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合结束: 第5回合处理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 士气时间计算: A队=849, B队=1050
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 下次事件: 时间=849, 发起方=teamA, 总时间=3998
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合开始: 第6回合, 攻击方: teamA
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 选择进攻方式: 推射 (ID: 3)
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择攻击球员: A1, 进攻方式=3, 阶段=0, 球员=hero_1759057854328_erazst0yv
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 发起阶段: 攻击方式=3, 回合=5
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 发起阶段记录完成: 回合=5, 成功率=1000
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=A1, 阶段=1, 球员=enemy_90101_90002
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 触发技能: 阶段=1, 攻击模式=3, 回合=5
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能记录生成完成: 持续=2, 瞬时=2, 下次攻击=2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=1, round=5, 实际period=1, round=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=1, round=5, 实际period=1, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=1, round=5, 实际period=2, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能效果列表填充完成: 阶段=1, 回合=5, 效果数量=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheManager] Cache memory hit: global:config:AttackMode:3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 推进阶段: 攻击=100, 防守=100, 基础=0.500, 士气=-0.083, 技能=0, 最终=0.417, 结果=false
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 突破统计更新: teamA 突破失败, 总突破3, 成功1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 推进阶段记录完成: 回合=5, 成功率=417, 结果=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleCommentSystem] 批量生成评论完成: 0条
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamA回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamB回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合结束: 第6回合处理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 士气时间计算: A队=1347, B队=200
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 下次事件: 时间=200, 发起方=teamB, 总时间=4198
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合开始: 第7回合, 攻击方: teamB
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 选择进攻方式: 头球 (ID: 1)
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择攻击球员: A1, 进攻方式=1, 阶段=0, 球员=enemy_90101_90009
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 发起阶段: 攻击方式=1, 回合=6
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 发起阶段记录完成: 回合=6, 成功率=1000
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 触发技能: 阶段=1, 攻击模式=1, 回合=6
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能记录生成完成: 持续=2, 瞬时=2, 下次攻击=2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=1, round=6, 实际period=1, round=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=1, round=6, 实际period=1, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=1, round=6, 实际period=2, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能效果列表填充完成: 阶段=1, 回合=6, 效果数量=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheManager] Cache miss: global:config:AttackMode:1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [ConfigLoader] Loading config: AttackMode from AttackMode.json
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [ConfigLoader] Loaded 9 items for AttackMode
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [RedisService] Set key: global:config:AttackMode:1 (dataType: global)
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheManager] Cache set: global:config:AttackMode:1, TTL: 7200s
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 推进阶段: 攻击=100, 防守=100, 基础=0.500, 士气=0.086, 技能=0, 最终=0.586, 结果=true
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 突破统计更新: teamB 突破成功, 总突破4, 成功2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 推进阶段记录完成: 回合=6, 成功率=586, 结果=1
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: heading
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'heading')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: heading
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'heading')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: heading
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'heading')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: heading
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'heading')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: heading
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'heading')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: heading
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'heading')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: heading
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'heading')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: heading
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'heading')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: heading
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'heading')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: heading
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'heading')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: heading
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'heading')
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择攻击球员: A2, 进攻方式=1, 阶段=2, 球员=
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=A1, 阶段=2, 球员=hero_1759057854318_hlzja3j4w
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=GK, 阶段=2, 球员=hero_1759057854318_hlzja3j4w
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 触发技能: 阶段=2, 攻击模式=1, 回合=6
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能记录生成完成: 持续=2, 瞬时=2, 下次攻击=2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=2, round=6, 实际period=1, round=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=2, round=6, 实际period=1, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=2, round=6, 实际period=2, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能效果列表填充完成: 阶段=2, 回合=6, 效果数量=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 射门阶段: 基础=0.565, 士气=0.043, 技能=0, 最终=0.600, 结果=false
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 射门阶段记录完成: 回合=6, 成功率=600, 进球=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleCommentSystem] 批量生成评论完成: 0条
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamA回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamB回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合结束: 第7回合处理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 士气时间计算: A队=1147, B队=1058
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 下次事件: 时间=1058, 发起方=teamB, 总时间=5256
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合开始: 第8回合, 攻击方: teamB
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 选择进攻方式: 抢点 (ID: 4)
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择攻击球员: A1, 进攻方式=4, 阶段=0, 球员=enemy_90101_90009
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 发起阶段: 攻击方式=4, 回合=7
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 发起阶段记录完成: 回合=7, 成功率=1000
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=A1, 阶段=1, 球员=hero_1759057854314_wl60vnj69
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 触发技能: 阶段=1, 攻击模式=4, 回合=7
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能记录生成完成: 持续=2, 瞬时=2, 下次攻击=2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=1, round=7, 实际period=1, round=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=1, round=7, 实际period=1, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=1, round=7, 实际period=2, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能效果列表填充完成: 阶段=1, 回合=7, 效果数量=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheManager] Cache memory hit: global:config:AttackMode:4
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 推进阶段: 攻击=100, 防守=100, 基础=0.500, 士气=0.081, 技能=0, 最终=0.581, 结果=true
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 突破统计更新: teamB 突破成功, 总突破5, 成功3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 推进阶段记录完成: 回合=7, 成功率=581, 结果=1
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: speed
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'speed')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: speed
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'speed')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: speed
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'speed')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: speed
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'speed')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: speed
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'speed')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: speed
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'speed')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: speed
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'speed')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: speed
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'speed')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: speed
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'speed')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: speed
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'speed')
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] 获取球员属性失败: speed
[Nest] 10620  - 2025/09/28 19:10:58    WARN [BattlePlayerSelector] TypeError: Cannot read properties of undefined (reading 'speed')
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择攻击球员: A2, 进攻方式=4, 阶段=2, 球员=
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=A1, 阶段=2, 球员=hero_1759057854316_8xojh2usg
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=GK, 阶段=2, 球员=hero_1759057854318_hlzja3j4w
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 触发技能: 阶段=2, 攻击模式=4, 回合=7
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能记录生成完成: 持续=2, 瞬时=2, 下次攻击=2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=2, round=7, 实际period=1, round=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=2, round=7, 实际period=1, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=2, round=7, 实际period=2, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能效果列表填充完成: 阶段=2, 回合=7, 效果数量=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 射门阶段: 基础=0.565, 士气=0.041, 技能=0, 最终=0.600, 结果=false
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 射门阶段记录完成: 回合=7, 成功率=600, 进球=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleCommentSystem] 批量生成评论完成: 0条
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamA回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamB回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合结束: 第8回合处理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 士气时间计算: A队=89, B队=1072
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 下次事件: 时间=89, 发起方=teamA, 总时间=5345
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合开始: 第9回合, 攻击方: teamA
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 选择进攻方式: 抢点 (ID: 4)
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择攻击球员: A1, 进攻方式=4, 阶段=0, 球员=hero_1759057854328_erazst0yv
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 发起阶段: 攻击方式=4, 回合=8
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 发起阶段记录完成: 回合=8, 成功率=1000
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=A1, 阶段=1, 球员=enemy_90101_90007
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 触发技能: 阶段=1, 攻击模式=4, 回合=8
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能记录生成完成: 持续=2, 瞬时=2, 下次攻击=2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=1, round=8, 实际period=1, round=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=1, round=8, 实际period=1, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=1, round=8, 实际period=2, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能效果列表填充完成: 阶段=1, 回合=8, 效果数量=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheManager] Cache memory hit: global:config:AttackMode:4
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 推进阶段: 攻击=100, 防守=100, 基础=0.500, 士气=-0.076, 技能=0, 最终=0.424, 结果=true
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 突破统计更新: teamA 突破成功, 总突破4, 成功2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 推进阶段记录完成: 回合=8, 成功率=424, 结果=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择攻击球员: A2, 进攻方式=4, 阶段=2, 球员=hero_1759057854313_hy50ul2x5
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=A1, 阶段=2, 球员=enemy_90101_90001
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePlayerSelector] 选择防守球员: 攻击者=GK, 阶段=2, 球员=enemy_90101_90004
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 触发技能: 阶段=2, 攻击模式=4, 回合=8
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能记录生成完成: 持续=2, 瞬时=2, 下次攻击=2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=2, round=8, 实际period=1, round=1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=2, round=8, 实际period=1, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能不匹配当前阶段: 期望period=2, round=8, 实际period=2, round=3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleSkillSystem] 技能效果列表填充完成: 阶段=2, 回合=8, 效果数量=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattlePeriodCalculator] 射门阶段: 基础=0.239, 士气=-0.038, 技能=0, 最终=0.201, 结果=false
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleRecordGenerator] 射门阶段记录完成: 回合=8, 成功率=201, 进球=0
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleCommentSystem] 批量生成评论完成: 0条
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamA回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 队伍teamB回合数据清理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 回合结束: 第9回合处理完成
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 士气时间计算: A队=1370, B队=983
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 下次事件: 时间=983, 发起方=teamB, 总时间=6328
[Nest] 10620  - 2025/09/28 19:10:58     LOG [BattleEngine] 战斗时间超限，战斗结束
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 战斗时间到达上限，结束战斗
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 统计数据更新完成: A队控球率44%, B队控球率56%
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] A队统计: 射门0, 突破4/2
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] B队统计: 射门1, 突破5/3
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 战斗结束，最终比分 0:1，总回合数: 10
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleEngine] 战斗结果生成完成: teamB 获胜, 比分 0:1
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleService] 战斗结果数据结构检查:
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleService] Object:
{
  "hasBattleRecord": true,
  "hasStatistics": true,
  "battleRecordKeys": [
    "battleRoundInfo",
    "totalTime",
    "totalRounds"
  ],
  "statisticsKeys": [
    "totalTime",
    "totalRounds",
    "teamA",
    "teamB"
  ],
  "teamAStatsExists": true,
  "teamBStatsExists": true,
  "battleRoundInfoLength": 9,
  "statisticsDetail": "{\n  \"totalTime\": 6328,\n  \"totalRounds\": 10,\n  \"teamA\": {\n    \"shots\": 0,\n    \"shotsOnTarget\": 0,\n    \"possession\": 44,\n    \"passes\": 0,\n    \"fouls\": 0,\n    \"ballerScoreMap\": {},\n    \"breaks\": 4,\n    \"successfulBreaks\": 2\n  },\n  \"teamB\": {\n    \"shots\": 1,\n    \"shotsOnTarget\": 1,\n    \"possession\": 56,\n    \"passes\": 0,\n    \"fouls\": 0,\n    \"ballerScoreMap\": {},\n    \"breaks\": 5,\n    \"successfulBreaks\": 3,\n    \"goalEventMap\": {}\n  }\n}",
  "battleRecordDetail": {
    "battleRoundInfoLength": 9,
    "totalTime": 0,
    "totalRounds": 0
  }
}

[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleService] 生成进球记录: 1个进球
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleService] 准备更新数据: {
  "status": "finished",
  "finishedAt": true,
  "teamARating": 0,
  "teamBRating": 100,
  "resultExists": true,
  "roundsCount": 9
}
[Nest] 10620  - 2025/09/28 19:10:58     LOG [BattleRepository] 文档更新成功: BattleRepository
[Nest] 10620  - 2025/09/28 19:10:58     LOG [BattleService] 战斗记录更新成功: room_1759057856484_2527
[Nest] 10620  - 2025/09/28 19:10:58     LOG [BattleService] 业务操作成功: BattleService_1759057856484_tu9zx90cd - 耗时 1876ms
[Nest] 10620  - 2025/09/28 19:10:58     LOG [LeagueService] 业务操作成功: LeagueService_1759057856196_oxdn1kvio - 耗时 2181ms
[Nest] 10620  - 2025/09/28 19:10:58     LOG [LeagueController] PVE战斗成功: char_server_001_d5fdcbbd_n69al3rof, 获得奖励: 0个
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheInterceptor] 🔍 CacheEvict handleCacheEvict called for pveBattle, metadata: {"keys":["league:data:#{payload.characterId}:*"],"repository":"leaguecontroller","allEntries":false,"beforeInvocation":false,"paramNames":["payload"],"methodName":"pveBattle","dataType":"server","serverId":"#{payload.serverId}"}
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheInterceptor] 🔍 CacheEvict metadata.allEntries: false, metadata.keys: ["league:data:#{payload.characterId}:*"]
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheInterceptor] 🔍 CacheEvict resolving keys with args: [{"characterId":"char_server_001_d5fdcbbd_n69al3rof","serverId":"server_001","token":null,"leagueId":90100,"teamCopyId":90101,"trustedContext":{"userId":"68d917bd940ed8a774fc4315","wsContext":{"timestamp":*************,"routingStrategy":"normal"},"requestId":"req_*************_ehj9xm0w9","serverContext":{"serverId":"server_001","characterId":"char_server_001_d5fdcbbd_n69al3rof"}}}], paramNames: ["payload"]
[Nest] 10620  - 2025/09/28 19:10:58     LOG [CacheInterceptor] 🗑️ CacheEvict attempting to delete 1 keys for pveBattle: league:data:char_server_001_d5fdcbbd_n69al3rof:*
[Nest] 10620  - 2025/09/28 19:10:58     LOG [CacheInterceptor] 🗑️ CacheEvict ❌ failed to delete key: league:data:char_server_001_d5fdcbbd_n69al3rof:* (dataType: server)
[Nest] 10620  - 2025/09/28 19:10:58     LOG [CacheInterceptor] 🗑️ CacheEvict completed: deleted 0/1 keys for pveBattle
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [RedisPubSubService] Published message to channel server:server_001:match:pubsub:cache:delete (dataType: server), 0 subscribers
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheInterceptor] 🔍 CacheInterceptor called for BattleController.getBattleReplay (context: rpc)
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [CacheInterceptor] 🎯 RPC Context - Payload: {"characterId":"char_server_001_d5fdcbbd_n69al3rof","serverId":"server_001","token":null,"roomId":"room_1759057856484_2527","trustedContext":{"userId":"68d917bd940ed8a774fc4315","wsContext":{"timestamp":*************,"routingStrategy":"normal"},"requestId":"req_*************_u8h12zie5","serverContext":{"serverId":"server_001","characterId":"char_server_001_d5fdcbbd_n69al3rof"}}}
[Nest] 10620  - 2025/09/28 19:10:58     LOG [CacheInterceptor] 🔍 Cache interceptor triggered for BattleController.getBattleReplay
[Nest] 10620  - 2025/09/28 19:10:58     LOG [CacheInterceptor]   - @Cacheable: {"key":"battle:replay:#{payload.roomId}","repository":"battlecontroller","ttl":3600,"paramNames":["payload"],"methodName":"getBattleReplay","dataType":"global"}
[Nest] 10620  - 2025/09/28 19:10:58     LOG [CacheManagerService] Created cache repository: battlecontroller
[Nest] 10620  - 2025/09/28 19:10:58     LOG [CacheInterceptor] ❌ Cache miss for getBattleReplay
[Nest] 10620  - 2025/09/28 19:10:58     LOG [BattleController] 获取战斗回放: room_1759057856484_2527
[Nest] 10620  - 2025/09/28 19:10:58     LOG [BattleService] 业务操作开始: BattleService_1759057858440_8y0hq93mi
[Nest] 10620  - 2025/09/28 19:10:58     LOG [BattleService] Object:
{
  "reason": "get_battle_replay",
  "metadata": {
    "roomId": "room_1759057856484_2527"
  }
}

[Nest] 10620  - 2025/09/28 19:10:58     LOG [BattleService] 获取战斗回放: room_1759057856484_2527
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [RedisPubSubService] Published message to channel server:server_001:match:pubsub:cache:miss (dataType: server), 0 subscribers
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleService] 查询战斗房间结果: success=true, data=true
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleService] 战斗房间状态: finished, 是否有result: true
[Nest] 10620  - 2025/09/28 19:10:58   DEBUG [BattleService] 战斗回放数据: {
  "battleRecord": {
    "battleRoundInfo": [
      {
        "eventTime": 1050,
        "moraleA": 300,
        "moraleB": 381,
        "attackerType": "teamB",
        "attackMode": 3,
        "scoreA": 0,
        "scoreB": 0,
        "periodInfo": [
          {
            "A1Info": {
              "heroId": "enemy_90101_90009",
              "attrType1": 1,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 0,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 1000,
            "actionResult": 1,
            "moraleA": 381,
            "moraleB": 300,
            "skillEffectList": []
          },
          {
            "A1Info": {
              "heroId": "enemy_90101_90009",
              "attrType1": 1,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 0,
              "addValue2": 0
            },
            "BInfo": {
              "heroId": "hero_1759057854321_29m90bspp",
              "attrType1": 4,
              "attrValue1": 41,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 44,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 581,
            "actionResult": 0,
            "moraleA": 380,
            "moraleB": 302,
            "skillEffectList": []
          }
        ],
        "moraleSlotA": 200000,
        "moraleSlotB": 157500,
        "comments": []
      },
      {
        "eventTime": 1331,
        "moraleA": 302,
        "moraleB": 380,
        "attackerType": "teamA",
        "attackMode": 4,
        "scoreA": 0,
        "scoreB": 0,
        "periodInfo": [
          {
            "A1Info": {
              "heroId": "hero_1759057854326_sbxyulaqg",
              "attrType1": 1,
              "attrValue1": 36,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 35,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 1000,
            "actionResult": 1,
            "moraleA": 302,
            "moraleB": 380,
            "skillEffectList": []
          },
          {
            "A1Info": {
              "heroId": "hero_1759057854326_sbxyulaqg",
              "attrType1": 1,
              "attrValue1": 36,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 35,
              "addValue2": 0
            },
            "BInfo": {
              "heroId": "enemy_90101_90004",
              "attrType1": 4,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 0,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 422,
            "actionResult": 0,
            "moraleA": 301,
            "moraleB": 382,
            "skillEffectList": [
              {
                "skillId": 11035,
                "teamType": "teamA",
                "addPer": 3,
                "heroId": "hero_1759057854326_sbxyulaqg"
              }
            ]
          }
        ],
        "moraleSlotA": 200000,
        "moraleSlotB": 53390,
        "comments": []
      },
      {
        "eventTime": 2099,
        "moraleA": 301,
        "moraleB": 382,
        "attackerType": "teamB",
        "attackMode": 4,
        "scoreA": 0,
        "scoreB": 0,
        "periodInfo": [
          {
            "A1Info": {
              "heroId": "enemy_90101_90009",
              "attrType1": 1,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 0,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 1000,
            "actionResult": 1,
            "moraleA": 382,
            "moraleB": 301,
            "skillEffectList": []
          },
          {
            "A1Info": {
              "heroId": "enemy_90101_90009",
              "attrType1": 1,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 0,
              "addValue2": 0
            },
            "BInfo": {
              "heroId": "hero_1759057854321_29m90bspp",
              "attrType1": 4,
              "attrValue1": 41,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 44,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 581,
            "actionResult": 0,
            "moraleA": 381,
            "moraleB": 303,
            "skillEffectList": []
          }
        ],
        "moraleSlotA": 200000,
        "moraleSlotB": 115584,
        "comments": []
      },
      {
        "eventTime": 2656,
        "moraleA": 303,
        "moraleB": 381,
        "attackerType": "teamA",
        "attackMode": 3,
        "scoreA": 0,
        "scoreB": 0,
        "periodInfo": [
          {
            "A1Info": {
              "heroId": "hero_1759057854329_0f7532hkf",
              "attrType1": 1,
              "attrValue1": 37,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 40,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 1000,
            "actionResult": 1,
            "moraleA": 303,
            "moraleB": 381,
            "skillEffectList": []
          },
          {
            "A1Info": {
              "heroId": "hero_1759057854329_0f7532hkf",
              "attrType1": 1,
              "attrValue1": 37,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 40,
              "addValue2": 0
            },
            "BInfo": {
              "heroId": "enemy_90101_90001",
              "attrType1": 4,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 0,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 422,
            "actionResult": 1,
            "moraleA": 303,
            "moraleB": 381,
            "skillEffectList": [
              {
                "skillId": 11024,
                "teamType": "teamA",
                "addPer": 5,
                "heroId": "hero_1759057854329_0f7532hkf"
              }
            ]
          },
          {
            "A2Info": {
              "heroId": "hero_1759057854313_hy50ul2x5",
              "attrType1": 3,
              "attrValue1": 35,
              "addValue1": 0,
              "attrType2": 5,
              "attrValue2": 41,
              "addValue2": 0
            },
            "BInfo": {
              "heroId": "enemy_90101_90001",
              "attrType1": 4,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 0,
              "addValue2": 0
            },
            "GKInfo": {
              "heroId": "enemy_90101_90004",
              "attrType1": 7,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 0,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 200,
            "actionResult": 0,
            "moraleA": 298,
            "moraleB": 381,
            "skillEffectList": [
              {
                "skillId": 11024,
                "teamType": "teamA",
                "addPer": 5,
                "heroId": "hero_1759057854329_0f7532hkf"
              }
            ]
          }
        ],
        "moraleSlotA": 200000,
        "moraleSlotB": 106109,
        "comments": []
      },
      {
        "eventTime": 3149,
        "moraleA": 298,
        "moraleB": 381,
        "attackerType": "teamB",
        "attackMode": 3,
        "scoreA": 0,
        "scoreB": 0,
        "periodInfo": [
          {
            "A1Info": {
              "heroId": "enemy_90101_90009",
              "attrType1": 1,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 0,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 1000,
            "actionResult": 1,
            "moraleA": 381,
            "moraleB": 298,
            "skillEffectList": []
          },
          {
            "A1Info": {
              "heroId": "enemy_90101_90009",
              "attrType1": 1,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 0,
              "addValue2": 0
            },
            "BInfo": {
              "heroId": "hero_1759057854321_29m90bspp",
              "attrType1": 4,
              "attrValue1": 41,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 44,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 583,
            "actionResult": 1,
            "moraleA": 381,
            "moraleB": 298,
            "skillEffectList": []
          },
          {
            "BInfo": {
              "heroId": "hero_1759057854314_wl60vnj69",
              "attrType1": 4,
              "attrValue1": 36,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 40,
              "addValue2": 0
            },
            "GKInfo": {
              "heroId": "hero_1759057854316_8xojh2usg",
              "attrType1": 7,
              "attrValue1": 37,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 44,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 600,
            "actionResult": 1,
            "moraleA": 381,
            "moraleB": 298,
            "skillEffectList": []
          }
        ],
        "moraleSlotA": 200000,
        "moraleSlotB": 73457,
        "comments": []
      },
      {
        "eventTime": 3998,
        "moraleA": 298,
        "moraleB": 381,
        "attackerType": "teamA",
        "attackMode": 3,
        "scoreA": 0,
        "scoreB": 1,
        "periodInfo": [
          {
            "A1Info": {
              "heroId": "hero_1759057854328_erazst0yv",
              "attrType1": 1,
              "attrValue1": 40,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 41,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 1000,
            "actionResult": 1,
            "moraleA": 298,
            "moraleB": 381,
            "skillEffectList": []
          },
          {
            "A1Info": {
              "heroId": "hero_1759057854328_erazst0yv",
              "attrType1": 1,
              "attrValue1": 40,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 41,
              "addValue2": 0
            },
            "BInfo": {
              "heroId": "enemy_90101_90002",
              "attrType1": 4,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 0,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 417,
            "actionResult": 0,
            "moraleA": 297,
            "moraleB": 383,
            "skillEffectList": []
          }
        ],
        "moraleSlotA": 200000,
        "moraleSlotB": 161735,
        "comments": []
      },
      {
        "eventTime": 4198,
        "moraleA": 297,
        "moraleB": 383,
        "attackerType": "teamB",
        "attackMode": 1,
        "scoreA": 0,
        "scoreB": 1,
        "periodInfo": [
          {
            "A1Info": {
              "heroId": "enemy_90101_90009",
              "attrType1": 1,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 0,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 1000,
            "actionResult": 1,
            "moraleA": 383,
            "moraleB": 297,
            "skillEffectList": []
          },
          {
            "A1Info": {
              "heroId": "enemy_90101_90009",
              "attrType1": 1,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 0,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 586,
            "actionResult": 1,
            "moraleA": 383,
            "moraleB": 297,
            "skillEffectList": []
          },
          {
            "A1Info": {
              "heroId": "enemy_90101_90009",
              "attrType1": 1,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 0,
              "addValue2": 0
            },
            "BInfo": {
              "heroId": "hero_1759057854318_hlzja3j4w",
              "attrType1": 4,
              "attrValue1": 45,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 45,
              "addValue2": 0
            },
            "GKInfo": {
              "heroId": "hero_1759057854318_hlzja3j4w",
              "attrType1": 7,
              "attrValue1": 38,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 45,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 600,
            "actionResult": 0,
            "moraleA": 378,
            "moraleB": 297,
            "skillEffectList": []
          }
        ],
        "moraleSlotA": 200000,
        "moraleSlotB": 29700,
        "comments": []
      },
      {
        "eventTime": 5256,
        "moraleA": 297,
        "moraleB": 378,
        "attackerType": "teamB",
        "attackMode": 4,
        "scoreA": 0,
        "scoreB": 1,
        "periodInfo": [
          {
            "A1Info": {
              "heroId": "enemy_90101_90009",
              "attrType1": 1,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 0,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 1000,
            "actionResult": 1,
            "moraleA": 378,
            "moraleB": 297,
            "skillEffectList": []
          },
          {
            "A1Info": {
              "heroId": "enemy_90101_90009",
              "attrType1": 1,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 0,
              "addValue2": 0
            },
            "BInfo": {
              "heroId": "hero_1759057854314_wl60vnj69",
              "attrType1": 4,
              "attrValue1": 36,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 40,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 581,
            "actionResult": 1,
            "moraleA": 378,
            "moraleB": 297,
            "skillEffectList": []
          },
          {
            "BInfo": {
              "heroId": "hero_1759057854316_8xojh2usg",
              "attrType1": 4,
              "attrValue1": 41,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 44,
              "addValue2": 0
            },
            "GKInfo": {
              "heroId": "hero_1759057854318_hlzja3j4w",
              "attrType1": 7,
              "attrValue1": 38,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 45,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 600,
            "actionResult": 0,
            "moraleA": 373,
            "moraleB": 297,
            "skillEffectList": []
          }
        ],
        "moraleSlotA": 200000,
        "moraleSlotB": 186813,
        "comments": []
      },
      {
        "eventTime": 5345,
        "moraleA": 297,
        "moraleB": 373,
        "attackerType": "teamA",
        "attackMode": 4,
        "scoreA": 0,
        "scoreB": 1,
        "periodInfo": [
          {
            "A1Info": {
              "heroId": "hero_1759057854328_erazst0yv",
              "attrType1": 1,
              "attrValue1": 40,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 41,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 1000,
            "actionResult": 1,
            "moraleA": 297,
            "moraleB": 373,
            "skillEffectList": []
          },
          {
            "A1Info": {
              "heroId": "hero_1759057854328_erazst0yv",
              "attrType1": 1,
              "attrValue1": 40,
              "addValue1": 0,
              "attrType2": 2,
              "attrValue2": 41,
              "addValue2": 0
            },
            "BInfo": {
              "heroId": "enemy_90101_90007",
              "attrType1": 4,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 0,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 424,
            "actionResult": 1,
            "moraleA": 297,
            "moraleB": 373,
            "skillEffectList": []
          },
          {
            "A2Info": {
              "heroId": "hero_1759057854313_hy50ul2x5",
              "attrType1": 3,
              "attrValue1": 35,
              "addValue1": 0,
              "attrType2": 5,
              "attrValue2": 41,
              "addValue2": 0
            },
            "BInfo": {
              "heroId": "enemy_90101_90001",
              "attrType1": 4,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 0,
              "addValue2": 0
            },
            "GKInfo": {
              "heroId": "enemy_90101_90004",
              "attrType1": 7,
              "attrValue1": 0,
              "addValue1": 0,
              "attrType2": 6,
              "attrValue2": 0,
              "addValue2": 0
            },
            "actionId": 0,
            "startCommentId": 0,
            "resultCommentId": 0,
            "actionPer": 201,
            "actionResult": 0,
            "moraleA": 292,
            "moraleB": 373,
            "skillEffectList": []
          }
        ],
        "moraleSlotA": 200000,
        "moraleSlotB": 16599,
        "comments": []
      }
    ],
    "totalTime": 6328,
    "totalRounds": 10
  },
  "teamAData": {
    "characterId": "char_server_001_d5fdcbbd_n69al3rof",
    "teamType": "teamA",
    "teamName": "队伍char_server_001_d5fdcbbd_n69al3rof",
    "rating": 0,
    "formationId": 442101,
    "attackTacticId": 101,
    "defendTacticId": 101,
    "heroes": [
      {
        "heroId": "hero_1759057854313_hy50ul2x5",
        "name": "巴塔查亚",
        "position": "GK",
        "rating": 1
      },
      {
        "heroId": "hero_1759057854314_wl60vnj69",
        "name": "弗赖马尼斯",
        "position": "DC",
        "rating": 1
      },
      {
        "heroId": "hero_1759057854316_8xojh2usg",
        "name": "加文.霍伊特",
        "position": "DL",
        "rating": 1
      },
      {
        "heroId": "hero_1759057854318_hlzja3j4w",
        "name": "贾斯汀努森",
        "position": "DR",
        "rating": 1
      },
      {
        "heroId": "hero_1759057854321_29m90bspp",
        "name": "哈布拉",
        "position": "MC",
        "rating": 1
      },
      {
        "heroId": "hero_1759057854323_l7zl0e53e",
        "name": "马塞兰",
        "position": "ML",
        "rating": 1
      },
      {
        "heroId": "hero_1759057854324_o8nx3elhi",
        "name": "斯图波尔",
        "position": "MR",
        "rating": 1
      },
      {
        "heroId": "hero_1759057854326_sbxyulaqg",
        "name": "佩斯科",
        "position": "WL",
        "rating": 1
      },
      {
        "heroId": "hero_1759057854328_erazst0yv",
        "name": "拉兹丁什",
        "position": "WR",
        "rating": 1
      },
      {
        "heroId": "hero_1759057854329_0f7532hkf",
        "name": "雷加莱斯",
        "position": "ST",
        "rating": 1
      },
      {
        "heroId": "hero_1759057854330_rjumcpeve",
        "name": "莫里斯",
        "position": "AM",
        "rating": 1
      }
    ],
    "attackTacticsState": 0,
    "defendTacticsState": 0,
    "baseAttackValue": 0,
    "baseDefendValue": 0,
    "attackValueFactor": 1,
    "defendValueFactor": 1
  },
  "teamBData": {
    "characterId": "AI",
    "teamType": "teamB",
    "teamName": "红星",
    "rating": 100,
    "formationId": 442201,
    "attackTacticId": 101,
    "defendTacticId": 101,
    "heroes": [
      {
        "heroId": "enemy_90101_90000",
        "name": "未知球员",
        "position": "GK",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90001",
        "name": "未知球员",
        "position": "DC",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90002",
        "name": "未知球员",
        "position": "DC",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90003",
        "name": "未知球员",
        "position": "DL",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90004",
        "name": "未知球员",
        "position": "DR",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90005",
        "name": "未知球员",
        "position": "DM",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90006",
        "name": "未知球员",
        "position": "MC",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90007",
        "name": "未知球员",
        "position": "MC",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90008",
        "name": "未知球员",
        "position": "AM",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90009",
        "name": "未知球员",
        "position": "ST",
        "rating": 1
      },
      {
        "heroId": "enemy_90101_90010",
        "name": "未知球员",
        "position": "ST",
        "rating": 1
      }
    ],
    "attackTacticsState": 0,
    "defendTacticsState": 0,
    "baseAttackValue": 0,
    "baseDefendValue": 0,
    "attackValueFactor": 1,
    "defendValueFactor": 1
  },
  "battleResult": {
    "roomId": "room_1759057856484_2527",
    "battleType": "PVE_LEAGUE",
    "homeScore": 0,
    "awayScore": 1,
    "winner": 2,
    "totalTime": 6328,
    "totalRounds": 10,
    "teamAStats": {
      "shotNum": 0,
      "ctrlBallPer": 44,
      "breakPer": 50,
      "bestBaller": "hero_1759057854318_hlzja3j4w"
    },
    "teamBStats": {
      "shotNum": 1,
      "ctrlBallPer": 56,
      "breakPer": 60,
      "bestBaller": "enemy_90101_90004"
    },
    "goals": [
      {
        "time": 1759057858250,
        "teamType": "teamB",
        "ballerHeroId": "enemy_90101_90009",
        "assistHeroId": ""
      }
    ],
    "rewards": null,
    "lootItems": [],
    "skillRecords": [
      {
        "durRecord": [],
        "insRecord": [
          {
            "skillId": 11035,
            "round": 1,
            "heroId": "hero_1759057854326_sbxyulaqg",
            "period": 1
          },
          {
            "skillId": 11024,
            "round": 3,
            "heroId": "hero_1759057854329_0f7532hkf",
            "period": 1
          },
          {
            "skillId": 11024,
            "round": 3,
            "heroId": "hero_1759057854329_0f7532hkf",
            "period": 2
          }
        ],
        "nextAtkRecord": []
      },
      {
        "durRecord": [],
        "insRecord": [],
        "nextAtkRecord": []
      }
    ]
  }
}