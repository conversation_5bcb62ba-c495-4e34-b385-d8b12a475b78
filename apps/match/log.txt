  回放数据: {
  "id": "test_1759046585718_fjtpth3zq",
  "type": "response",
  "service": "gateway",
  "action": "response",
  "payload": {
    "success": true,
    "data": {
      "battleRecord": {
        "battleRoundInfo": [
          {
            "eventTime": 2000,
            "moraleA": 300,
            "moraleB": 381,
            "attackerType": "teamA",
            "attackMode": 5,
            "scoreA": 0,
            "scoreB": 0,
            "periodInfo": [
              {
                "A1Info": {
                  "heroId": "hero_1759046583977_wt9a72dkv",
                  "attrType1": 1,
                  "attrValue1": 40,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 43,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 0,
                "resultCommentId": 0,
                "actionPer": 1000,
                "actionResult": 1,
                "moraleA": 300,
                "moraleB": 381,
                "skillEffectList": []
              },
              {
                "A1Info": {
                  "heroId": "hero_1759046583977_wt9a72dkv",
                  "attrType1": 1,
                  "attrValue1": 40,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 43,
                  "addValue2": 0
                },
                "BInfo": {
                  "heroId": "enemy_90101_90007",
                  "attrType1": 4,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 6,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 0,
                "resultCommentId": 0,
                "actionPer": 419,
                "actionResult": 0,
                "moraleA": 300,
                "moraleB": 381,
                "skillEffectList": []
              }
            ],
            "moraleSlotA": 1000,
            "moraleSlotB": 0,
            "comments": []
          },
          {
            "eventTime": 4000,
            "moraleA": 300,
            "moraleB": 381,
            "attackerType": "teamA",
            "attackMode": 1,
            "scoreA": 0,
            "scoreB": 0,
            "periodInfo": [
              {
                "A1Info": {
                  "heroId": "hero_1759046583977_wt9a72dkv",
                  "attrType1": 1,
                  "attrValue1": 40,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 43,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 0,
                "resultCommentId": 0,
                "actionPer": 1000,
                "actionResult": 1,
                "moraleA": 300,
                "moraleB": 381,
                "skillEffectList": []
              },
              {
                "A1Info": {
                  "heroId": "hero_1759046583977_wt9a72dkv",
                  "attrType1": 1,
                  "attrValue1": 40,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 43,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 0,
                "resultCommentId": 0,
                "actionPer": 419,
                "actionResult": 0,
                "moraleA": 300,
                "moraleB": 381,
                "skillEffectList": []
              }
            ],
            "moraleSlotA": 1000,
            "moraleSlotB": 0,
            "comments": []
          }
        ],
        "totalTime": 6000,
        "totalRounds": 3
      },
      "teamAData": {
        "characterId": "char_server_001_b0880871_6blenna7o",
        "teamType": "teamA",
        "teamName": "队伍char_server_001_b0880871_6blenna7o",
        "rating": 0,
        "formationId": 442101,
        "attackTacticId": 101,
        "defendTacticId": 101,
        "heroes": [
          {
            "heroId": "hero_1759046583961_qdatlhd3t",
            "name": "巴塔查亚",
            "position": "GK",
            "rating": 1
          },
          {
            "heroId": "hero_1759046583963_dtzw3926x",
            "name": "弗赖马尼斯",
            "position": "DC",
            "rating": 1
          },
          {
            "heroId": "hero_1759046583964_m14nlwqah",
            "name": "加文.霍伊特",
            "position": "DL",
            "rating": 1
          },
          {
            "heroId": "hero_1759046583971_9f8gwtusm",
            "name": "贾斯汀努森",
            "position": "DR",
            "rating": 1
          },
          {
            "heroId": "hero_1759046583973_tuoyo9vb4",
            "name": "哈布拉",
            "position": "MC",
            "rating": 1
          },
          {
            "heroId": "hero_1759046583974_i6i3xrbvi",
            "name": "马塞兰",
            "position": "ML",
            "rating": 1
          },
          {
            "heroId": "hero_1759046583976_dopvkqo4i",
            "name": "斯图波尔",
            "position": "MR",
            "rating": 1
          },
          {
            "heroId": "hero_1759046583977_wt9a72dkv",
            "name": "佩斯科",
            "position": "WL",
            "rating": 1
          },
          {
            "heroId": "hero_1759046583978_ktu98leo0",
            "name": "拉兹丁什",
            "position": "WR",
            "rating": 1
          },
          {
            "heroId": "hero_1759046583980_6ts21lfl8",
            "name": "雷加莱斯",
            "position": "ST",
            "rating": 1
          },
          {
            "heroId": "hero_1759046583982_c5l0k1mde",
            "name": "莫里斯",
            "position": "AM",
            "rating": 1
          }
        ],
        "attackTacticsState": 0,
        "defendTacticsState": 0,
        "baseAttackValue": 0,
        "baseDefendValue": 0,
        "attackValueFactor": 1,
        "defendValueFactor": 1
      },
      "teamBData": {
        "characterId": "AI",
        "teamType": "teamB",
        "teamName": "红星",
        "rating": 0,
        "formationId": 442201,
        "attackTacticId": 101,
        "defendTacticId": 101,
        "heroes": [
          {
            "heroId": "enemy_90101_90000",
            "name": "未知球员",
            "position": "GK",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90001",
            "name": "未知球员",
            "position": "DC",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90002",
            "name": "未知球员",
            "position": "DC",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90003",
            "name": "未知球员",
            "position": "DL",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90004",
            "name": "未知球员",
            "position": "DR",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90005",
            "name": "未知球员",
            "position": "DM",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90006",
            "name": "未知球员",
            "position": "MC",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90007",
            "name": "未知球员",
            "position": "MC",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90008",
            "name": "未知球员",
            "position": "AM",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90009",
            "name": "未知球员",
            "position": "ST",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90010",
            "name": "未知球员",
            "position": "ST",
            "rating": 1
          }
        ],
        "attackTacticsState": 0,
        "defendTacticsState": 0,
        "baseAttackValue": 0,
        "baseDefendValue": 0,
        "attackValueFactor": 1,
        "defendValueFactor": 1
      }
    },
    "code": "SUCCESS",
    "timestamp": 1759046585763,
    "requestId": "test_1759046585718_fjtpth3zq",
    "duration": 65,
    "service": "match",
    "action": "battle.getBattleReplay"
  },
  "timestamp": 1759046585791
}