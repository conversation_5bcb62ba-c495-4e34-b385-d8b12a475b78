/**
 * 联赛系统测试模块
 * 
 * 测试内容：
 * 1. 获取联赛副本数据
 * 2. PVE联赛战斗
 * 3. 购买联赛次数
 * 4. 联赛奖励领取
 */

const chalk = require('chalk');

class LeagueSystemTester {
  constructor(socket, testData) {
    this.socket = socket;
    this.testData = testData;
    this.testResults = [];
  }

  /**
   * WebSocket调用封装 - 使用新的消息格式
   */
  async callWebSocket(command, data = {}) {
    return new Promise((resolve, reject) => {
      const messageId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      const message = {
        id: messageId,
        command: command,
        payload: {
          characterId: this.testData.characterId,
          serverId: this.testData.serverId || 'server_001',
          token: this.testData.token,
          ...data
        }
      };

      console.log(chalk.gray(`📤 发送消息: ${command}`));

      // 设置响应监听器
      const responseHandler = (response) => {
        if (response.id === messageId) {
          console.log(chalk.cyan(`📨 收到响应: ${command}`));
          this.socket.off('message', responseHandler);
          resolve(response);
        }
      };

      this.socket.on('message', responseHandler);

      // 发送消息
      this.socket.emit('message', message);

      // 设置超时
      setTimeout(() => {
        this.socket.off('message', responseHandler);
        reject(new Error(`消息超时: ${command}`));
      }, 10000);
    });
  }

  /**
   * 测试获取联赛副本数据
   */
  async testGetLeagueCopyData() {
    console.log(chalk.yellow('📋 测试获取联赛副本数据...'));

    try {
      const response = await this.callWebSocket('match.league.getLeagueCopyData');

      if (response?.payload?.success) {
        const data = response?.payload?.data;
        console.log(chalk.green('✅ 获取联赛副本数据成功'));
        console.log(chalk.gray(`   联赛数量: ${data.leagueCopyData?.length || 0}`));

        // 保存联赛数据供后续测试使用
        this.leagueData = data.leagueCopyData;

        if (data.leagueCopyData && data.leagueCopyData.length > 0) {
          const firstLeague = data.leagueCopyData[0];
          console.log(chalk.gray(`   第一个联赛ID: ${firstLeague.uid}`)); // 修复：使用uid而不是id
          console.log(chalk.gray(`   副本数量: ${firstLeague.copyData?.length || 0}`));
        }

        this.testResults.push({ test: 'getLeagueCopyData', success: true });
        return true;
      } else {
        console.log(chalk.red('❌ 获取联赛副本数据失败'));
        const errorMsg = response?.payload?.message || '未知错误';
        console.log(chalk.red(`   错误信息: ${errorMsg}`));
        this.testResults.push({ test: 'getLeagueCopyData', success: false, error: errorMsg });
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 获取联赛副本数据异常: ${error.message}`));
      this.testResults.push({ test: 'getLeagueCopyData', success: false, error: error.message });
      return false;
    }
  }

  /**
   * 测试PVE联赛战斗
   */
  async testPveLeagueBattle() {
    console.log(chalk.yellow('⚔️ 测试PVE联赛战斗...'));
    
    try {
      // 检查是否有联赛数据
      if (!this.leagueData || this.leagueData.length === 0) {
        console.log(chalk.yellow('⚠️ 没有联赛数据，跳过PVE战斗测试'));
        return true;
      }

      const firstLeague = this.leagueData[0];
      if (!firstLeague.copyData || firstLeague.copyData.length === 0) {
        console.log(chalk.yellow('⚠️ 没有副本数据，跳过PVE战斗测试'));
        return true;
      }

      const firstCopy = firstLeague.copyData[0];

      const response = await this.callWebSocket('match.league.pveBattle', {
        leagueId: firstLeague.uid,  // 修复：使用uid而不是id
        teamCopyId: firstCopy.teamCopyId  // 修复：使用teamCopyId而不是teamId
      });

      if (response?.payload?.success) {
        const data = response?.payload?.data;
        console.log(chalk.green('✅ PVE联赛战斗成功'));

        // 修复：正确解析战斗结果数据结构
        const battleResult = data.battleResult || data;
        const winner = battleResult.winner;
        const homeScore = battleResult.homeScore || 0;
        const awayScore = battleResult.awayScore || 0;
        const roomId = battleResult.roomId;

        console.log(chalk.gray(`   战斗结果: ${winner === 1 ? '胜利' : winner === 0 ? '平局' : '失败'}`));
        console.log(chalk.gray(`   比分: ${homeScore}:${awayScore}`));

        // 获取并显示战斗回放 - 支持新的old项目格式
        if (roomId) {
          console.log(chalk.gray(`   房间ID: ${roomId}`));
          const replaySuccess = await this.getBattleReplayAndDisplay(roomId);

          // 如果战斗回放获取失败，但有战斗数据，则显示战斗记录
          if (!replaySuccess && battleResult) {
            console.log(chalk.yellow('📹 使用已有的战斗数据...'));
            this.displayEnhancedBattleRecord(battleResult);
          }
        } else if (battleResult) {
          // 直接显示战斗记录 - 使用新的增强格式
          console.log(chalk.yellow('📹 显示战斗回放数据...'));
          this.displayEnhancedBattleRecord(battleResult);
        } else {
          console.log(chalk.yellow('⚠️ 未找到战斗数据，无法显示战斗回放'));
        }

        this.testResults.push({ test: 'pveBattle', success: true });
        return true;
      } else {
        console.log(chalk.red('❌ PVE联赛战斗失败'));
        const errorMsg = response?.payload?.message || '未知错误';
        console.log(chalk.red(`   错误信息: ${errorMsg}`));
        this.testResults.push({ test: 'pveBattle', success: false, error: errorMsg });
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`❌ PVE联赛战斗异常: ${error.message}`));
      this.testResults.push({ test: 'pveBattle', success: false, error: error.message });
      return false;
    }
  }

  /**
   * 测试购买联赛次数
   */
  async testBuyLeagueTimes() {
    console.log(chalk.yellow('💰 测试购买联赛次数...'));
    
    try {
      // 检查是否有联赛数据
      if (!this.leagueData || this.leagueData.length === 0) {
        console.log(chalk.yellow('⚠️ 没有联赛数据，跳过购买次数测试'));
        return true;
      }

      const firstLeague = this.leagueData[0];
      
      const response = await this.callWebSocket('match.league.buyLeagueTimes', {
        times: 1
      });

      const data = response?.payload?.data;

      if (response?.payload?.success) {
        console.log(chalk.green('✅ 购买联赛次数成功'));
        console.log(chalk.gray(`   购买数量: ${data.addedTimes || 1}`));
        console.log(chalk.gray(`   花费: ${data.totalCost || 0} 球币`));

        this.testResults.push({ test: 'buyLeagueTimes', success: true });
        return true;
      } else {
        console.log(chalk.red('❌ 购买联赛次数失败'));
        const errorMsg = response?.payload?.message || '未知错误';
        console.log(chalk.red(`   错误信息: ${errorMsg}`));
        this.testResults.push({ test: 'buyLeagueTimes', success: false, error: errorMsg });
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 购买联赛次数异常: ${error.message}`));
      this.testResults.push({ test: 'buyLeagueTimes', success: false, error: error.message });
      return false;
    }
  }

  /**
   * 测试获取联赛统计信息
   */
  async testGetLeagueStatistics() {
    console.log(chalk.yellow('📊 测试获取联赛统计信息...'));
    
    try {
      const response = await this.callWebSocket('match.league.getStatistics');

      if (response?.payload?.success) {
        const data = response?.payload?.data;
        console.log(chalk.green('✅ 获取联赛统计信息成功'));
        console.log(chalk.gray(`   统计数据: ${JSON.stringify(data.data || {})}`));

        this.testResults.push({ test: 'getLeagueStatistics', success: true });
        return true;
      } else {
        console.log(chalk.red('❌ 获取联赛统计信息失败'));
        const errorMsg = response?.payload?.message || '未知错误';
        console.log(chalk.red(`   错误信息: ${errorMsg}`));
        this.testResults.push({ test: 'getLeagueStatistics', success: false, error: errorMsg });
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 获取联赛统计信息异常: ${error.message}`));
      this.testResults.push({ test: 'getLeagueStatistics', success: false, error: error.message });
      return false;
    }
  }

  /**
   * 运行所有联赛系统测试
   */
  async runTests() {
    console.log(chalk.blue('\n=== 联赛系统测试 ==='));
    
    const tests = [
      () => this.testGetLeagueCopyData(),
      () => this.testPveLeagueBattle(),
      () => this.testBuyLeagueTimes(),
      () => this.testGetLeagueStatistics()
    ];

    let successCount = 0;
    
    for (const test of tests) {
      try {
        const result = await test();
        if (result) successCount++;
      } catch (error) {
        console.log(chalk.red(`测试执行异常: ${error.message}`));
      }
      
      // 测试间隔
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log(chalk.blue(`\n联赛系统测试完成: ${successCount}/${tests.length} 通过`));
    
    // 显示详细结果
    this.testResults.forEach(result => {
      const status = result.success ? chalk.green('✅') : chalk.red('❌');
      const error = result.error ? chalk.gray(` (${result.error})`) : '';
      console.log(`${status} ${result.test}${error}`);
    });

    return successCount === tests.length;
  }

  /**
   * 获取战斗回放并可视化显示
   */
  async getBattleReplayAndDisplay(roomId) {
    try {
      console.log(chalk.yellow('📹 获取战斗回放数据...'));

      const response = await this.callWebSocket('match.battle.getBattleReplay', {
        roomId: roomId
      });

      if (response?.payload?.success) {
        const data = response?.payload?.data;
        console.log(chalk.green('✅ 战斗回放获取成功'));
        console.log(chalk.gray(`   回放数据: ${JSON.stringify(response, null, 2)}`));
        this.displayBattleReplay(data, roomId);
      } else {
        console.log(chalk.yellow(`⚠️ 战斗回放获取失败或无数据: ${response?.payload?.message || '未知错误'}`));
        console.log(chalk.gray(`   错误信息: ${'未知错误'}`));

        // 如果回放获取失败，但有战斗记录数据，则显示战斗记录
        console.log(chalk.yellow('📹 使用已有的战斗记录数据...'));
        return false; // 返回false，让调用方知道需要使用备用方案
      }
    } catch (error) {
      console.log(chalk.red(`❌ 获取战斗回放异常: ${error.message}`));
    }
  }

  /**
   * 可视化显示战斗回放
   */
  displayBattleReplay(replayData, roomId) {
    console.log(chalk.blue('\n' + '='.repeat(80)));
    console.log(chalk.blue(`🎬 战斗回放可视化显示 - 房间ID: ${roomId}`));
    console.log(chalk.blue('='.repeat(80)));

    if (!replayData || !Array.isArray(replayData)) {
      console.log(chalk.yellow('⚠️ 回放数据格式异常'));
      return;
    }

    // 显示回放统计信息
    console.log(chalk.cyan(`📊 回放统计:`));
    console.log(chalk.gray(`   总回合数: ${replayData.length}`));
    console.log(chalk.gray(`   数据大小: ${JSON.stringify(replayData).length} 字符`));

    // 逐回合显示战斗过程
    replayData.forEach((round, index) => {
      console.log(chalk.yellow(`\n⚽ 第${index + 1}回合:`));

      if (round.events && Array.isArray(round.events)) {
        round.events.forEach((event, eventIndex) => {
          this.displayBattleEvent(event, eventIndex + 1);
        });
      } else if (round.action) {
        // 单个事件格式
        this.displayBattleEvent(round, index + 1);
      } else {
        console.log(chalk.gray(`   📝 原始数据: ${JSON.stringify(round)}`));
      }
    });

    console.log(chalk.blue('\n' + '='.repeat(80)));
    console.log(chalk.blue('🏁 战斗回放显示完成'));
    console.log(chalk.blue('='.repeat(80) + '\n'));
  }

  /**
   * 显示单个战斗事件
   */
  displayBattleEvent(event, eventNumber) {
    if (!event) return;

    const eventType = event.type || event.action || '未知事件';
    const description = event.description || event.message || '';

    // 根据事件类型使用不同颜色
    let eventColor = chalk.white;
    let eventIcon = '📝';

    switch (eventType.toLowerCase()) {
      case 'goal':
      case 'score':
      case '进球':
        eventColor = chalk.green;
        eventIcon = '⚽';
        break;
      case 'attack':
      case '攻击':
      case '进攻':
        eventColor = chalk.yellow;
        eventIcon = '🏃';
        break;
      case 'defend':
      case 'defense':
      case '防守':
        eventColor = chalk.blue;
        eventIcon = '🛡️';
        break;
      case 'foul':
      case '犯规':
        eventColor = chalk.red;
        eventIcon = '🟨';
        break;
      case 'substitution':
      case '换人':
        eventColor = chalk.magenta;
        eventIcon = '🔄';
        break;
      default:
        eventColor = chalk.gray;
        eventIcon = '📋';
    }

    console.log(eventColor(`   ${eventIcon} 事件${eventNumber}: ${eventType}`));

    if (description) {
      console.log(chalk.gray(`      描述: ${description}`));
    }

    // 显示相关球员信息
    if (event.player || event.heroId) {
      const playerInfo = event.player || event.heroId;
      console.log(chalk.gray(`      球员: ${playerInfo}`));
    }

    // 显示时间信息
    if (event.time || event.minute) {
      const timeInfo = event.time || `${event.minute}'`;
      console.log(chalk.gray(`      时间: ${timeInfo}`));
    }

    // 显示比分变化
    if (event.score || (event.homeScore !== undefined && event.awayScore !== undefined)) {
      const score = event.score || `${event.homeScore}:${event.awayScore}`;
      console.log(chalk.cyan(`      比分: ${score}`));
    }

    // 显示其他详细信息
    const otherFields = Object.keys(event).filter(key =>
      !['type', 'action', 'description', 'message', 'player', 'heroId', 'time', 'minute', 'score', 'homeScore', 'awayScore'].includes(key)
    );

    if (otherFields.length > 0) {
      otherFields.forEach(field => {
        if (event[field] !== null && event[field] !== undefined) {
          console.log(chalk.gray(`      ${field}: ${JSON.stringify(event[field])}`));
        }
      });
    }
  }

  /**
   * 显示充满游戏性的战斗回放过程（适配新的战斗系统数据结构）
   */
  displayEnhancedBattleRecord(battleResult) {
    console.log(chalk.blue('\n' + '='.repeat(80)));
    console.log(chalk.blue(`足球经理战斗回放 - 房间ID: ${battleResult.roomId}`));
    console.log(chalk.blue('='.repeat(80)));

    // 显示比赛开场信息
    this.displayMatchIntro(battleResult);

    // 显示实时战斗过程
    this.displayLiveBattleProcess(battleResult);

    // 显示比赛结束总结
    this.displayMatchSummary(battleResult);

    console.log(chalk.blue('\n' + '='.repeat(80)));
    console.log(chalk.blue('🏁 比赛结束！感谢观看本场精彩对决！'));
    console.log(chalk.blue('='.repeat(80) + '\n'));
  }

  /**
   * 显示比赛开场信息
   */
  displayMatchIntro(battleResult) {
    console.log(chalk.yellow('\n🎺 比赛即将开始！'));

    // 获取队伍信息
    const teamAName = battleResult.teamA?.teamName || battleResult.preBattleInfo?.[0]?.teamName || '主队';
    const teamBName = battleResult.teamB?.teamName || battleResult.preBattleInfo?.[1]?.teamName || '客队';
    const teamARating = battleResult.teamA?.rating || battleResult.preBattleInfo?.[0]?.rating || 0;
    const teamBRating = battleResult.teamB?.rating || battleResult.preBattleInfo?.[1]?.rating || 0;

    console.log(chalk.green(`🏠 主队: ${teamAName} (评分: ${teamARating})`));
    console.log(chalk.red(`✈️  客队: ${teamBName} (评分: ${teamBRating})`));

    // 显示阵型信息
    if (battleResult.preBattleInfo && battleResult.preBattleInfo.length >= 2) {
      const teamAFormation = battleResult.preBattleInfo[0].formationID || 0;
      const teamBFormation = battleResult.preBattleInfo[1].formationID || 0;
      console.log(chalk.cyan(`📐 阵型对比: ${teamAName} (${teamAFormation}) vs ${teamBName} (${teamBFormation})`));
    }

    // 显示实力对比
    if (battleResult.teamA && battleResult.teamB) {
      const teamATotal = (battleResult.teamA.totalAttack || 0) + (battleResult.teamA.totalDefend || 0);
      const teamBTotal = (battleResult.teamB.totalAttack || 0) + (battleResult.teamB.totalDefend || 0);
      console.log(chalk.magenta(`⚡ 实力对比: ${teamAName} (${teamATotal}) vs ${teamBName} (${teamBTotal})`));
    }

    console.log(chalk.yellow('🔥 裁判吹响开场哨！比赛正式开始！'));
    console.log(chalk.gray('─'.repeat(60)));
  }

  /**
   * 显示实时战斗过程
   */
  displayLiveBattleProcess(battleResult) {
    console.log(chalk.yellow('\n⚽ 比赛进行中...'));

    // 获取战斗回合数据
    let roundRecords = [];
    if (battleResult.battleRecord && battleResult.battleRecord.roundRecords) {
      roundRecords = battleResult.battleRecord.roundRecords;
    } else if (battleResult.battleRoundInfo) {
      roundRecords = this.convertOldFormatToRounds(battleResult.battleRoundInfo);
    }

    if (roundRecords.length === 0) {
      console.log(chalk.yellow('⚠️ 没有找到战斗回合数据'));
      return;
    }

    // 实时播放战斗过程
    let currentScore = { teamA: 0, teamB: 0 };
    let currentTime = 0;

    roundRecords.forEach((round, index) => {
      currentTime = round.battleTime || round.time || (index * 2 + 1);

      // 显示攻击回合
      this.displayAttackRound(round, currentTime, currentScore, index + 1);

      // 更新比分
      if (round.isGoal || round.goal) {
        if (round.attackerTeam === 'teamA' || round.attacker === 'teamA') {
          currentScore.teamA++;
        } else {
          currentScore.teamB++;
        }
      }
    });
  }

  /**
   * 显示单个攻击回合
   */
  displayAttackRound(round, time, currentScore, roundNumber) {
    const isTeamA = round.attackerTeam === 'teamA' || round.attacker === 'teamA';
    const teamName = isTeamA ? '主队' : '客队';
    const teamColor = isTeamA ? chalk.green : chalk.red;
    const isGoal = round.isGoal || round.goal;

    // 显示时间和回合
    console.log(chalk.cyan(`\n⏱️  第${time}'分钟 - 第${roundNumber}回合`));

    // 显示攻击过程
    if (round.periodResults && Array.isArray(round.periodResults)) {
      // 新战斗系统：三阶段攻击
      console.log(teamColor(`� ${teamName}发起进攻！`));

      const phases = ['发起', '突破', '射门'];
      round.periodResults.forEach((period, index) => {
        const phaseIcon = ['🚀', '⚡', '🎯'][index];
        const success = period.success ? '成功' : '失败';
        const successColor = period.success ? chalk.green : chalk.red;

        console.log(chalk.gray(`   ${phaseIcon} ${phases[index]}阶段: ${successColor(success)} (成功率: ${(period.successRate * 100).toFixed(1)}%)`));

        if (period.playerName) {
          console.log(chalk.gray(`      参与球员: ${period.playerName}`));
        }
      });
    } else {
      // 简化格式：单次攻击
      console.log(teamColor(`🏃 ${teamName}发起进攻！`));
      console.log(chalk.gray(`   攻击力: ${round.attackPower || 0} vs 防守力: ${round.defendPower || 0}`));
      console.log(chalk.gray(`   成功率: ${((round.successRate || 0) * 100).toFixed(1)}%`));
    }

    // 显示结果
    if (isGoal) {
      console.log(chalk.yellow(`🎉 ⚽ 进球！！！ ${teamName}破门得分！`));
      console.log(chalk.yellow(`🏆 比分更新: ${currentScore.teamA + (isTeamA ? 1 : 0)} - ${currentScore.teamB + (!isTeamA ? 1 : 0)}`));
    } else {
      const defenderTeam = isTeamA ? '客队' : '主队';
      console.log(chalk.blue(`🛡️  ${defenderTeam}成功防守！`));
    }

    // 显示技能使用
    if (round.skillUsed && round.skillUsed.length > 0) {
      console.log(chalk.magenta(`✨ 技能发动: ${round.skillUsed.map(s => s.skillName || s.skillId).join(', ')}`));
    }

    console.log(chalk.gray('─'.repeat(50)));
  }

  /**
   * 显示比赛结束总结
   */
  displayMatchSummary(battleResult) {
    console.log(chalk.yellow('\n🏁 全场比赛结束！'));

    // 显示最终比分
    const finalScoreA = battleResult.homeScore || battleResult.teamAScore || 0;
    const finalScoreB = battleResult.awayScore || battleResult.teamBScore || 0;
    const teamAName = battleResult.teamA?.teamName || battleResult.preBattleInfo?.[0]?.teamName || '主队';
    const teamBName = battleResult.teamB?.teamName || battleResult.preBattleInfo?.[1]?.teamName || '客队';

    console.log(chalk.cyan(`📊 最终比分: ${teamAName} ${finalScoreA} - ${finalScoreB} ${teamBName}`));

    // 显示比赛结果
    let resultText = '';
    let resultColor = chalk.yellow;
    if (battleResult.winner === 1) {
      resultText = `🏆 ${teamAName} 获得胜利！`;
      resultColor = chalk.green;
    } else if (battleResult.winner === -1) {
      resultText = `🏆 ${teamBName} 获得胜利！`;
      resultColor = chalk.red;
    } else {
      resultText = '🤝 双方战成平局！';
      resultColor = chalk.yellow;
    }
    console.log(resultColor(resultText));

    // 显示比赛统计
    if (battleResult.battleEndInfo && battleResult.battleEndInfo.stInfos) {
      const stInfos = battleResult.battleEndInfo.stInfos;
      if (stInfos.length >= 2) {
        console.log(chalk.cyan('\n📈 比赛统计:'));
        console.log(chalk.gray(`   ${teamAName}: 射门 ${stInfos[0].shotNum || 0} 次`));
        console.log(chalk.gray(`   ${teamBName}: 射门 ${stInfos[1].shotNum || 0} 次`));
      }
    }

    // 显示奖励信息
    if (battleResult.battleEndInfo) {
      const endInfo = battleResult.battleEndInfo;

      if (endInfo.lootItemList && endInfo.lootItemList.length > 0) {
        console.log(chalk.yellow('\n💰 获得奖励:'));
        endInfo.lootItemList.forEach((item, index) => {
          console.log(chalk.gray(`   ${index + 1}. 物品ID: ${item.resId}, 数量: ${item.num}`));
        });
      }

      if (endInfo.rewards) {
        console.log(chalk.yellow('\n💰 额外奖励:'));
        if (endInfo.rewards.experience) console.log(chalk.gray(`   经验: +${endInfo.rewards.experience}`));
        if (endInfo.rewards.coins) console.log(chalk.gray(`   金币: +${endInfo.rewards.coins}`));
        if (endInfo.rewards.reputation) console.log(chalk.gray(`   声望: +${endInfo.rewards.reputation}`));
      }
    }
  }

  /**
   * 转换old项目格式到标准回合格式
   */
  convertOldFormatToRounds(battleRoundInfo) {
    if (!Array.isArray(battleRoundInfo)) return [];

    return battleRoundInfo.map((round, index) => ({
      battleTime: round.eventTime || (index * 2 + 1),
      attackerTeam: round.attackerType,
      attacker: round.attackerType,
      attackPower: round.attackPower || 0,
      defendPower: round.defendPower || 0,
      successRate: round.successRate || 0,
      isGoal: round.periodInfo && round.periodInfo.some(p => p.actionResult === 1),
      goal: round.periodInfo && round.periodInfo.some(p => p.actionResult === 1),
      periodResults: round.periodInfo || []
    }));
  }
}

module.exports = LeagueSystemTester;
