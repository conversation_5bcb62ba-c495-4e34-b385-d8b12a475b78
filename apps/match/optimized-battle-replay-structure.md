# 优化的战斗回放数据结构设计

## 设计原则

基于当前项目的BattleRoom和BattleResult层级结构，对战斗回放数据结构进行优化：

1. **层级清晰**: 遵循BattleRoom -> BattleResult的现有架构
2. **类型安全**: 提供完整的TypeScript类型定义
3. **向后兼容**: 保持与old项目数据格式的兼容性
4. **实体复用**: 使用现有的TeamSnapshoot和HeroSnapshoot定义

## 核心数据结构层级

### 1. BattleRoom 层级 (顶层容器)

```typescript
/**
 * 战斗房间 - 顶层数据容器
 * 包含双方队伍信息和战斗结果
 */
export interface BattleRoom {
  roomId: string;                        // 房间ID
  status: RoomStatus;              // 房间状态
  battleType: BattleType;                // 战斗类型

  // 双方队伍信息 (对应原preBattleInfo数组)
  teamA: TeamSnapshoot;               // A队信息 (preBattleInfo[0])
  teamB: TeamSnapshoot;               // B队信息 (preBattleInfo[1])

  // 战斗结果 (包含所有回放数据)
  result?: BattleResult;           // 战斗结果和回放数据

  // 房间元数据
  createdAt: Date;                       // 创建时间
  updatedAt: Date;                       // 更新时间
}
```

### 2. TeamSnapshoot 层级 (队伍信息)

```typescript
/**
 * 战斗队伍实体 - 对应原preBattleInfo中的单个队伍
 * 包含队伍基本信息、战术配置和球员阵容
 */
export interface TeamSnapshoot {
  // 基本信息
  teamType: TeamType;                    // 队伍类型 (teamA/teamB)
  teamName: string;                      // 队伍名称
  rating: number;                        // 队伍评分

  // 战术配置
  formationId: number;                   // 阵型ID
  attackTacticId: number;                // 攻击战术ID
  defendTacticId: number;                // 防守战术ID

  // 球员阵容 (11名球员)
  heroes: HeroSnapshoot[];                  // 球员列表

  // 战术状态 (可选字段)
  attackTacticsState?: number;           // 攻击战术状态
  defendTacticsState?: number;           // 防守战术状态
  baseAttackValue?: number;              // 基础攻击值
  baseDefendValue?: number;              // 基础防守值
  attackValueFactor?: number;            // 攻击加成因子
  defendValueFactor?: number;            // 防守加成因子

  // 显示信息 (可选字段)
  teamAvatar?: string;                   // 队伍头像
  battleName?: string;                   // 战斗名称
  battleType?: number;                   // 战斗类型标识
}
```

### 3. HeroSnapshoot 层级 (球员定义)

```typescript
/**
 * 球员快照实体 - 战斗前的核心信息
 * 用于TeamSnapshoot中的heroes数组
 */
export interface HeroSnapshoot {
  heroId: string;                        // 球员ID (原heroUid)
  name: string;                          // 球员姓名
  position: HeroPosition;                // 球员位置
  rating: number;                        // 球员评分
}

```

### 4. BattleResult 层级 (战斗结果和回放数据)

```typescript
/**
 * 战斗结果实体 - 包含所有回放数据和结果信息
 * 对应原BattleReplayData层级，整合了battleEndInfo、battleRoundInfo、skillRecord
 */
export interface BattleResult {
  // 基本结果信息
  roomId: string;                        // 房间ID
  battleType: BattleType;                // 战斗类型
  homeScore: number;                     // 主队比分
  awayScore: number;                     // 客队比分
  winner: number;                        // 胜利方 (0平局 1主队 2客队)
  battleTime: Date;                      // 战斗时间

  // 战斗统计 (原battleEndInfo中的字段直接在此层级)
  totalTime: number;                     // 总时长
  totalRounds: number;                   // 总回合数

  // 队伍统计信息 (原battleEndInfo.stInfos)
  teamAStats: TeamStatistics;            // A队统计
  teamBStats: TeamStatistics;            // B队统计

  // 进球记录 (原battleEndInfo.goalRecord)
  goals: GoalRecord[];                   // 进球记录列表

  // 奖励信息 (原battleEndInfo.businessReward)
  rewards?: BattleRewards;               // 奖励信息

  // 掉落物品 (原battleEndInfo.lootItemList)
  lootItems?: LootItem[];                // 掉落物品列表

  // 回合详情 (原battleRoundInfo)
  rounds: BattleRound[];                 // 战斗回合列表

  // 技能记录 (原skillRecord)
  skillRecords: SkillRecord[];           // 技能记录列表
}
```

### 5. 支撑数据结构

```typescript
/**
 * 队伍统计信息
 * 对应原battleEndInfo.stInfos中的单个队伍统计
 */
export interface TeamStatistics {
  teamType: TeamType;                    // 队伍类型
  shotNum: number;                       // 射门次数
  ctrlBallPer: number;                   // 控球率 (百分比)
  breakPer: number;                      // 突破成功率 (百分比)
  placeKickNum: number;                  // 定位球次数
  bestBaller: string;                    // 最佳球员姓名
}

/**
 * 进球记录
 * 对应原battleEndInfo.goalRecord中的单个进球
 */
export interface GoalRecord {
  time: number;                          // 进球时间
  ballerName: string;                    // 进球球员姓名
  teamType: TeamType;                    // 进球队伍
  heroId?: string;                       // 进球球员ID
  assistHeroName?: string;               // 助攻球员姓名
  assistHeroId?: string;                 // 助攻球员ID
}

/**
 * 战斗奖励
 * 对应原battleEndInfo.businessReward
 */
export interface BattleRewards {
  homeReward: TeamReward;                // 主队奖励
  awayReward: TeamReward;                // 客队奖励
}

/**
 * 队伍奖励
 */
export interface TeamReward {
  exp: number;                           // 经验值
  coins: number;                         // 金币
  fansChangeNum: number;                 // 球迷变化数量
}

/**
 * 掉落物品
 * 对应原battleEndInfo.lootItemList中的单个物品
 */
export interface LootItem {
  itemId: number;                        // 物品ID
  quantity: number;                      // 数量
}
```

### 6. BattleRound 层级 (战斗回合)

```typescript
/**
 * 战斗回合实体
 * 对应原battleRoundInfo中的单个回合
 */
export interface BattleRound {
  eventTime: number;                     // 事件时间
  moraleA: number;                       // A队士气
  moraleB: number;                       // B队士气
  attackerType: TeamType;                // 攻击方
  attackMode: number;                    // 攻击模式
  scoreA: number;                        // A队比分
  scoreB: number;                        // B队比分

  // 阶段信息 (原periodInfo)
  periodInfo: PeriodInfo[];              // 阶段信息列表

  // 可选字段
  moraleSlotA?: number;                  // A队士气槽
  moraleSlotB?: number;                  // B队士气槽
  comments?: any[];                      // 评论列表
}

/**
 * 阶段信息
 * 对应原periodInfo中的单个阶段
 */
export interface PeriodInfo {
  // 球员信息
  A1Info?: HeroActionInfo;             // 主要攻击球员
  A2Info?: HeroActionInfo;             // 协助攻击球员
  BInfo?: HeroActionInfo;              // 防守球员
  GKInfo?: HeroActionInfo;             // 门将

  // 动作信息
  actionId: number;                      // 动作ID
  startCommentId?: number;               // 开始评论ID
  resultCommentId?: number;              // 结果评论ID
  actionPer?: number;                    // 成功率 (千分制)
  actionResult?: number;                 // 动作结果

  // 士气信息
  moraleA?: number;                      // A队士气
  moraleB?: number;                      // B队士气

  // 技能效果
  skillEffectList?: any[];               // 技能效果列表
}

/**
 * 球员动作信息
 * 对应原A1Info、A2Info、BInfo、GKInfo的结构
 */
export interface HeroActionInfo {
  heroId: string;                        // 球员ID

  // 第一属性
  attrType1?: number;                    // 第一属性类型 (1-20)
  attrValue1?: number;                   // 第一属性值
  addValue1?: number;                    // 第一属性加成值

  // 第二属性
  attrType2?: number;                    // 第二属性类型 (1-20)
  attrValue2?: number;                   // 第二属性值
  addValue2?: number;                    // 第二属性加成值

  // 资源信息
  resId?: number;                        // 球员资源ID
}
```

### 7. SkillRecord 层级 (技能记录)

```typescript
/**
 * 技能记录实体
 * 对应原skillRecord的结构
 */
export interface SkillRecord {
  // 持续性技能记录
  durRecord: DurativeSkillRecord[];      // 持续性技能列表

  // 瞬时性技能记录
  insRecord: InstantSkillRecord[];       // 瞬时性技能列表

  // 下次攻击技能记录
  nextAtkRecord: NextAttackSkillRecord[]; // 下次攻击技能列表
}

/**
 * 持续性技能记录
 * 对应原skillRecord.durRecord中的单个技能
 */
export interface DurativeSkillRecord {
  skillId: number;                       // 技能ID
  startTime: number;                     // 开始时间
  endTime: number;                       // 结束时间
  heroId: string;                        // 施放球员ID
  round?: number;                        // 触发回合
  period?: number;                       // 触发阶段
}

/**
 * 瞬时性技能记录
 * 对应原skillRecord.insRecord中的单个技能
 */
export interface InstantSkillRecord {
  skillId: number;                       // 技能ID
  round: number;                         // 触发回合
  heroId: string;                        // 施放球员ID
  period: number;                        // 触发阶段
}

/**
 * 下次攻击技能记录
 * 对应原skillRecord.nextAtkRecord中的单个技能
 */
export interface NextAttackSkillRecord {
  skillId: number;                       // 技能ID
  heroId: string;                        // 施放球员ID
  targetRound: number;                   // 目标回合
}
```

## 数据结构层级关系

```
BattleRoom (顶层容器)
├── teamA: TeamSnapshoot
│   └── heroes: HeroSnapshoot[]
├── teamB: TeamSnapshoot
│   └── heroes: HeroSnapshoot[]
└── result: BattleResult
    ├── teamAStats: TeamStatistics
    ├── teamBStats: TeamStatistics
    ├── goals: GoalRecord[]
    ├── rewards: BattleRewards
    ├── lootItems: LootItem[]
    ├── rounds: BattleRound[]
    │   └── periodInfo: PeriodInfo[]
    │       ├── A1Info: HeroActionInfo
    │       ├── A2Info: HeroActionInfo
    │       ├── BInfo: HeroActionInfo
    │       └── GKInfo: HeroActionInfo
    └── skillRecords: SkillRecord[]
        ├── durRecord: DurativeSkillRecord[]
        ├── insRecord: InstantSkillRecord[]
        └── nextAtkRecord: NextAttackSkillRecord[]
```

## 设计优势

### 1. 层级清晰
- **BattleRoom**: 顶层容器，包含房间基本信息和双方队伍
- **TeamSnapshoot**: 队伍层级，包含战术配置和球员阵容
- **BattleResult**: 结果层级，整合所有回放数据和统计信息
- **支撑结构**: 各种详细的数据结构支撑主要层级

### 2. 复用现有定义
- 使用现有的**TeamSnapshoot**和**HeroSnapshoot**定义
- 保持与当前项目架构的一致性
- 减少重复定义，提高维护性

### 3. 向后兼容
- 保持与old项目数据格式的完全兼容
- 字段名称和结构与参照文档一致
- 支持现有的数据迁移和转换

### 4. 类型安全
- 完整的TypeScript接口定义
- 明确的字段类型和可选性
- 便于IDE智能提示和类型检查
```
