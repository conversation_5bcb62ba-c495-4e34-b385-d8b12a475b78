import { BattleSkillSystem } from './../systems/battle-skill-system';
import { Logger } from '@nestjs/common';
import { GameConfigFacade } from '@libs/game-config';
import { BattleTeam, AttackMode, PeriodResult, BattleHero, AttackModeConfig } from '../types/battle-data.types';
import { BattleType } from '@libs/game-constants';



/**
 * 三阶段战斗计算器 - 性能优化版本
 * 基于old项目room.js的三阶段战斗逻辑
 *
 * 职责：
 * - 发起阶段计算 (calcStartPeriod)
 * - 推进阶段计算 (calcMiddlePeriod)
 * - 射门阶段计算 (calcEndPeriod)
 * - 进攻方式计算 (calcAttackMode)
 *
 * 🚀 性能优化：
 * - 预计算攻击模式权重
 * - 缓存重复计算结果
 * - 优化随机数生成
 * - 减少对象创建
 */
export class BattlePeriodCalculator {
  private readonly logger = new Logger(BattlePeriodCalculator.name);

  // 🚀 性能优化：预计算攻击模式权重
  private readonly attackModes: AttackModeConfig[] = [
    { id: AttackMode.Header, weight: 30, name: '头球' },      // 头球进攻
    { id: AttackMode.LongShot, weight: 15, name: '远射' },   // 远射
    { id: AttackMode.Push, weight: 25, name: '推射' },       // 推射
    { id: AttackMode.Scramble, weight: 20, name: '抢点' },   // 抢点
    { id: AttackMode.Lob, weight: 8, name: '吊射' },         // 吊射
    { id: AttackMode.OneOnOne, weight: 2, name: '单刀' }     // 单刀（稀有）
  ];

  // 🚀 性能优化：预计算总权重
  private readonly totalBaseWeight = this.attackModes.reduce((sum, mode) => sum + mode.weight, 0);

  // 🚀 性能优化：缓存计算结果
  private readonly calculationCache = new Map<string, any>();
  private readonly maxCacheSize = 1000; // 最大缓存条目数
  private cacheAccessCount = 0; // 缓存访问计数器

  // 🚀 性能优化：随机数生成器优化
  private randomSeed = Date.now();

  constructor(
    private readonly gameConfig: GameConfigFacade,
    private readonly battleSkillSystem?: BattleSkillSystem,
    private readonly battleType?: BattleType,
  ) {}

  /**
   * 🚀 缓存管理 - 防止内存泄漏
   */
  private manageCache(): void {
    this.cacheAccessCount++;

    // 每1000次访问清理一次缓存
    if (this.cacheAccessCount % 1000 === 0) {
      if (this.calculationCache.size > this.maxCacheSize) {
        // 清理最旧的50%缓存条目
        const entries = Array.from(this.calculationCache.entries());
        const keepCount = Math.floor(this.maxCacheSize * 0.5);

        this.calculationCache.clear();

        // 保留最近的条目
        for (let i = entries.length - keepCount; i < entries.length; i++) {
          this.calculationCache.set(entries[i][0], entries[i][1]);
        }

        this.logger.debug(`缓存清理完成，保留 ${keepCount} 个条目`);
      }
    }
  }

  /**
   * 🚀 优化的随机数生成器 - 使用线性同余生成器提升性能
   */
  private fastRandom(): number {
    this.randomSeed = (this.randomSeed * 1664525 + 1013904223) % 4294967296;
    return this.randomSeed / 4294967296;
  }

  /**
   * 🚀 计算进攻方式 - 性能优化版本
   * 基于old项目: Room.prototype.calcAttackModeByTactic
   *
   * 优化点：
   * - 使用预计算的攻击模式数组
   * - 缓存权重调整结果
   * - 优化随机数生成
   * - 减少对象创建和数组操作
   */
  calcAttackMode(attackerTeam: BattleTeam): AttackMode {
    try {
      // 🚀 优化：特殊情况检查（快速路径）
      const specialMode = this.checkSpecialAttackMode(attackerTeam);
      if (specialMode !== null) {
        return specialMode;
      }

      // 🚀 优化：生成缓存键
      const cacheKey = `attackMode_${attackerTeam.totalAttack}_${attackerTeam.totalDefend}_${attackerTeam.attr.morale}`;

      // 🚀 优化：检查缓存
      if (this.calculationCache.has(cacheKey)) {
        this.manageCache(); // 缓存管理
        const cachedModes = this.calculationCache.get(cacheKey);
        return this.selectWeightedRandom(cachedModes);
      }

      // 🚀 优化：根据队伍属性调整权重（只计算一次）
      const adjustedModes = this.adjustAttackModeWeights(attackerTeam, this.attackModes);

      // 🚀 优化：缓存调整后的权重
      this.calculationCache.set(cacheKey, adjustedModes);

      return this.selectWeightedRandom(adjustedModes);
    } catch (error) {
      this.logger.error('计算进攻方式失败', error);
      return AttackMode.Header; // 默认返回头球攻击
    }
  }

  /**
   * 🚀 优化的加权随机选择
   * 使用快速随机数生成器和预计算总权重
   *
   * @param modes 攻击模式配置数组 - 包含id、weight、name的配置对象
   * @returns AttackMode 选中的攻击模式枚举值
   */
  private selectWeightedRandom(modes: AttackModeConfig[]): AttackMode {
    // 🚀 优化：预计算总权重
    const totalWeight = modes.reduce((sum, mode) => sum + mode.weight, 0);
    let randomValue = this.fastRandom() * totalWeight;

    // 🚀 优化：使用for循环而不是forEach，性能更好
    for (let i = 0; i < modes.length; i++) {
      randomValue -= modes[i].weight;
      if (randomValue <= 0) {
        this.logger.debug(`选择进攻方式: ${modes[i].name} (ID: ${modes[i].id})`);
        return modes[i].id;
      }
    }

    // 默认返回头球攻击
    return AttackMode.Header;
  }

  /**
   * 🚀 预计算队伍对比数据 - 避免重复计算
   *
   * @param attackerTeam 攻击方队伍数据 - 包含总攻击力等属性
   * @param defenderTeam 防守方队伍数据 - 包含总防守力等属性
   * @returns 对比数据对象 - 包含攻击值和防守值
   */
  private calculateTeamComparison(attackerTeam: BattleTeam, defenderTeam: BattleTeam): {
    attackValue: number;
    defendValue: number;
    baseRate: number;
    moraleBonus: number;
  } {
    const cacheKey = `teamComp_${attackerTeam.totalAttack}_${attackerTeam.totalDefend}_${attackerTeam.attr.morale}_${defenderTeam.totalAttack}_${defenderTeam.totalDefend}_${defenderTeam.attr.morale}`;

    if (this.calculationCache.has(cacheKey)) {
      this.manageCache(); // 缓存管理
      return this.calculationCache.get(cacheKey);
    }

    const attackValue = attackerTeam.totalAttack || 0;
    const defendValue = defenderTeam.totalDefend || 0;

    // 防止除零错误
    const totalValue = attackValue + defendValue;
    let baseRate = 0.5; // 默认50%
    if (totalValue > 0) {
      baseRate = attackValue / totalValue;
    } else {
      this.logger.warn(`队伍属性值为0: 攻击=${attackValue}, 防守=${defendValue}`);
    }

    const attackerMorale = attackerTeam.attr?.morale || 500;
    const defenderMorale = defenderTeam.attr?.morale || 500;
    let moraleBonus = (attackerMorale - defenderMorale) / 1000;

    // 检查计算结果
    if (isNaN(baseRate) || isNaN(moraleBonus)) {
      this.logger.error(`队伍对比计算产生NaN: attackValue=${attackValue}, defendValue=${defendValue}, totalValue=${totalValue}, baseRate=${baseRate}, moraleBonus=${moraleBonus}`);
      baseRate = isNaN(baseRate) ? 0.5 : baseRate;
      moraleBonus = isNaN(moraleBonus) ? 0 : moraleBonus;
    }

    const result = { attackValue, defendValue, baseRate, moraleBonus };
    this.calculationCache.set(cacheKey, result);

    return result;
  }

  /**
   * 🚀 发起阶段计算 - 性能优化版本
   * 基于old项目room.js的calcStartPeriod逻辑 - 发起阶段100%成功
   *
   * @param attackerTeam 攻击方队伍数据 - 当前实现中未使用，保留接口兼容性
   * @param defenderTeam 防守方队伍数据 - 当前实现中未使用，保留接口兼容性
   * @param attackMode 攻击模式 - 用于日志记录和调试
   * @param roundIndex 回合索引 - 用于日志记录和调试
   * @param skillAddPer 技能加成百分比 - 当前实现中未使用
   * @returns PeriodResult 发起结果 - 总是返回成功(100%成功率)
   */
  calcStartPeriod(
    _attackerTeam: BattleTeam,
    _defenderTeam: BattleTeam,
    attackMode: AttackMode,
    roundIndex: number,
    _skillAddPer: number = 0
  ): PeriodResult {
    // 🚀 优化：发起阶段总是成功，无需复杂计算
    this.logger.debug(`发起阶段: 攻击方式=${attackMode}, 回合=${roundIndex}`);

    // 🚀 优化：返回预定义对象，避免重复创建
    const result = { success: true, rate: 1.0 };
    if (isNaN(result.rate)) {
      this.logger.error(`发起阶段计算产生NaN: attackMode=${attackMode}, roundIndex=${roundIndex}`);
    }
    return result;
  }

  /**
   * 🚀 推进阶段计算 - 基于room.js配置表的完整实现
   * 基于old项目room.js的calcMiddlePeriod逻辑（突破计算）
   *
   * 📋 完整计算流程：
   *
   * 【第一阶段：配置表数据获取】
   * 1. 根据attackMode获取AttackMode配置表数据
   * 2. 提取突破阶段的属性类型和系数(A1BrType1/A1BrType2, BBrType1/BBrType2)
   * 3. 将属性索引转换为属性名称(speed/shooting/passing等)
   *
   * 【第二阶段：球员属性计算】
   * 4. 获取攻击方A1球员和防守方B球员
   * 5. 调用getHeroBattleAttr获取球员实时属性(包含技能加成)
   * 6. 计算攻击数值: (attrObj.cur + attrObj.add) * factor
   * 7. 计算防守数值: 同样的逻辑应用于防守球员
   *
   * 【第三阶段：成功率计算】
   * 8. 基础成功率 = attackValue / (attackValue + defendValue)
   * 9. 士气影响 = (攻击方士气 - 防守方士气) / 1000
   * 10. 技能加成 = skillAddPer / 100
   * 11. 最终成功率 = 基础成功率 + 士气影响 + 技能加成 (限制在0.1-0.9)
   *
   * 【第四阶段：结果判定】
   * 12. 使用快速随机数生成器判定成功/失败
   * 13. 记录详细的计算日志
   * 14. 返回标准化结果格式
   *
   * @param attackerTeam 攻击方队伍数据 - 包含球员、士气、技能状态等
   * @param defenderTeam 防守方队伍数据 - 包含球员、士气、技能状态等
   * @param attackMode 攻击模式 - 1-9对应不同的攻击类型(头球/远射/推射等)
   * @param roundIndex 回合索引 - 用于技能时间计算和状态管理
   * @param skillAddPer 技能成功率加成 - 百分比形式的额外成功率
   * @returns Promise<PeriodResult> 阶段结果 - 包含成功状态和成功率
   */
  async calcMiddlePeriod(
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    attackMode: AttackMode,
    roundIndex: number,
    skillAddPer: number = 0
  ): Promise<PeriodResult> {
    try {
      // 🔧 技能系统集成 - 基于old项目的调用顺序
      let skillBonus = 0;
      if (this.battleSkillSystem) {
        // 1. 先触发技能
        skillBonus = this.battleSkillSystem.triggerSkillsInPeriod(
          1, // 推进阶段
          attackMode,
          roundIndex,
          attackerTeam,
          defenderTeam,
          [attackerTeam.roundInfo.A1Info?.heroId, defenderTeam.roundInfo.BInfo?.heroId].filter(Boolean)
        );

        // 2. 再填充skillEffectList
        this.battleSkillSystem.fillSkillEffectList(1, attackMode, roundIndex, attackerTeam);
      }

      // 基于配置表的属性计算 - 与room.js保持一致
      const { attackValue, defendValue } = await this.calculateConfigBasedAttributes(
        attackerTeam, defenderTeam, attackMode as number, roundIndex, 'breakthrough'
      );

      // 基础成功率计算
      const baseRate = attackValue / (attackValue + defendValue);

      // 士气影响
      const moraleBonus = (attackerTeam.attr.morale - defenderTeam.attr.morale) / 1000;
      let adjustedRate = Math.max(0.1, Math.min(0.9, baseRate + moraleBonus));

      // 技能成功率加成 - 使用实际触发的技能加成
      const totalSkillBonus = skillAddPer + skillBonus;
      adjustedRate += totalSkillBonus / 100;
      adjustedRate = Math.max(0.1, Math.min(0.9, adjustedRate));

      const success = this.fastRandom() < adjustedRate;

      this.logger.debug(`推进阶段: 攻击=${attackValue}, 防守=${defendValue}, 基础=${baseRate.toFixed(3)}, 士气=${moraleBonus.toFixed(3)}, 技能=${totalSkillBonus}, 最终=${adjustedRate.toFixed(3)}, 结果=${success}`);

      // 🔧 基于old项目：更新突破统计数据
      this.updateBreakStatistics(attackerTeam, defenderTeam, success);

      if (isNaN(adjustedRate)) {
        this.logger.error(`推进阶段计算产生NaN: attackValue=${attackValue}, defendValue=${defendValue}, baseRate=${baseRate}, moraleBonus=${moraleBonus}, skillAddPer=${skillAddPer}`);
        return { success: false, rate: 0.5 };
      }
      return { success, rate: adjustedRate };
    } catch (error) {
      this.logger.error('推进阶段计算失败', error);
      // 降级到简化计算
      return this.calcMiddlePeriodFallback(attackerTeam, defenderTeam, skillAddPer);
    }
  }

  /**
   * 🚀 射门阶段计算 - 性能优化版本
   * 基于old项目room.js的calcEndPeriod逻辑（射门vs门将）
   *
   * 优化点：
   * - 预计算射门对比数据
   * - 使用快速随机数生成器
   * - 合并数学运算
   * - 减少对象创建
   */
  async calcEndPeriod(
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    _attackMode: AttackMode,
    _roundIndex: number,
    skillAddPer: number = 0
  ): Promise<PeriodResult> {
    // 🔧 技能系统集成 - 基于old项目的调用顺序
    let skillBonus = 0;
    if (this.battleSkillSystem) {
      // 1. 先触发技能
      skillBonus = this.battleSkillSystem.triggerSkillsInPeriod(
        2, // 射门阶段
        _attackMode,
        _roundIndex,
        attackerTeam,
        defenderTeam,
        [attackerTeam.roundInfo.A1Info?.heroId, defenderTeam.roundInfo.GKInfo?.heroId].filter(Boolean)
      );

      // 2. 再填充skillEffectList
      this.battleSkillSystem.fillSkillEffectList(2, _attackMode, _roundIndex, attackerTeam);
    }

    // 🚀 优化：预计算射门对比数据
    const { baseRate, moraleBonus } = this.calculateShotComparison(attackerTeam, defenderTeam);

    // 🚀 优化：合并计算步骤，减少中间变量和数学运算
    const totalSkillBonus = skillAddPer + skillBonus;
    const adjustedRate = Math.max(0.05, Math.min(0.6, baseRate + moraleBonus + totalSkillBonus / 100));

    // 🚀 优化：使用快速随机数生成器
    const success = this.fastRandom() < adjustedRate;

    this.logger.debug(`射门阶段: 基础=${baseRate.toFixed(3)}, 士气=${moraleBonus.toFixed(3)}, 技能=${totalSkillBonus}, 最终=${adjustedRate.toFixed(3)}, 结果=${success}`);

    // 🔧 基于old项目：更新射门统计数据（只有成功时才更新）
    if (success) {
      this.updateShotStatistics(attackerTeam, defenderTeam, _attackMode);
    }

    if (isNaN(adjustedRate)) {
      this.logger.error(`射门阶段计算产生NaN: baseRate=${baseRate}, moraleBonus=${moraleBonus}, totalSkillBonus=${totalSkillBonus}`);
      return { success: false, rate: 0.5 };
    }
    return { success, rate: adjustedRate };
  }

  /**
   * 🚀 预计算射门对比数据 - 专门针对射门阶段的优化
   *
   * @param attackerTeam 攻击方队伍数据 - 包含射门相关属性
   * @param defenderTeam 防守方队伍数据 - 包含门将和防守属性
   * @returns 射门对比数据 - 包含基础成功率和士气加成
   */
  private calculateShotComparison(attackerTeam: BattleTeam, defenderTeam: BattleTeam): {
    baseRate: number;
    moraleBonus: number;
  } {
    const cacheKey = `shotComp_${attackerTeam.totalAttack}_${defenderTeam.totalDefend}_${attackerTeam.attr.morale}_${defenderTeam.attr.morale}`;

    if (this.calculationCache.has(cacheKey)) {
      this.manageCache(); // 缓存管理
      return this.calculationCache.get(cacheKey);
    }

    // 🚀 优化：射门阶段的特殊计算，合并到一次计算中
    const attackValue = attackerTeam.totalAttack * 0.8; // 射门能力
    const defendValue = defenderTeam.totalDefend * 1.2; // 门将防守加成
    const baseRate = attackValue / (attackValue + defendValue);
    const moraleBonus = (attackerTeam.attr.morale - defenderTeam.attr.morale) / 2000; // 士气影响减半

    const result = { baseRate, moraleBonus };
    this.calculationCache.set(cacheKey, result);

    return result;
  }

  /**
   * 🔧 更新射门统计数据
   * 基于old项目room.js的calcShotResult逻辑
   */
  private updateShotStatistics(attackerTeam: BattleTeam, defenderTeam: BattleTeam, attackMode: AttackMode): void {
    try {
      // 基于old项目：有效射门次数统计
      attackerTeam.statistic.shots = (attackerTeam.statistic.shots || 0) + 1;

      // 基于old项目：定位球统计（攻击模式>=7）
      if (attackMode >= 7) {
        attackerTeam.statistic.setPlays = (attackerTeam.statistic.setPlays || 0) + 1;
      }

      // 基于old项目：进球事件记录
      const battleTime = Date.now().toString(); // 转换为string类型
      const shotHeroId = attackMode === 8 ?
        attackerTeam.roundInfo.A2Info?.heroId :
        attackerTeam.roundInfo.A1Info?.heroId;

      // 获取助攻球员ID
      const assistHeroId = attackMode === 8 ?
        attackerTeam.roundInfo.A1Info?.heroId : // 角球时A1是助攻
        attackerTeam.roundInfo.A2Info?.heroId;   // 其他情况A2是助攻

      if (shotHeroId) {
        if (!attackerTeam.statistic.goalEventMap) {
          attackerTeam.statistic.goalEventMap = new Map();
        }
        attackerTeam.statistic.goalEventMap.set(battleTime, {
          ballerName: shotHeroId, // 射门球员
          assistName: assistHeroId || '', // 助攻球员
          teamType: attackerTeam.teamSide
        });
      }

      // 基于old项目：射门成功时的士气变化
      if (attackerTeam.attr.morale < 80) {
        attackerTeam.attr.morale += 10;
        attackerTeam.attr.moraleAcceleration = attackerTeam.attr.morale;
      }

      // 基于old项目：球员评分更新
      this.updatePlayerScores(attackerTeam, defenderTeam, attackMode, true);

      this.logger.debug(`射门统计更新: ${attackerTeam.teamSide} 射门次数+1, 当前${attackerTeam.statistic.shots}`);
    } catch (error) {
      this.logger.error('更新射门统计失败', error);
    }
  }

  /**
   * 🔧 更新突破统计数据
   * 基于old项目room.js的calcBreakResult逻辑
   */
  private updateBreakStatistics(attackerTeam: BattleTeam, defenderTeam: BattleTeam, success: boolean): void {
    try {
      // 基于old项目：突破次数统计（无论成功失败都增加）
      attackerTeam.statistic.breaks = (attackerTeam.statistic.breaks || 0) + 1;

      // 基于old项目：突破成功次数统计（只有成功时才增加）
      if (success) {
        attackerTeam.statistic.successfulBreaks = (attackerTeam.statistic.successfulBreaks || 0) + 1;
      }

      // 基于old项目：士气变化
      if (!success) { // result === 2 表示失败
        attackerTeam.attr.morale = Math.max(0, (attackerTeam.attr.morale || 500) - 1);
        attackerTeam.attr.moraleAcceleration = attackerTeam.attr.morale;
        defenderTeam.attr.morale = Math.min(1000, (defenderTeam.attr.morale || 500) + 2);
        defenderTeam.attr.moraleAcceleration = defenderTeam.attr.morale;
      }

      // 基于old项目：球员评分更新
      this.updateBreakPlayerScores(attackerTeam, success);

      this.logger.debug(`突破统计更新: ${attackerTeam.teamSide} 突破${success ? '成功' : '失败'}, 总突破${attackerTeam.statistic.breaks}, 成功${attackerTeam.statistic.successfulBreaks}`);
    } catch (error) {
      this.logger.error('更新突破统计失败', error);
    }
  }

  /**
   * 🔧 更新突破球员评分
   * 基于old项目的突破评分逻辑
   */
  private updateBreakPlayerScores(attackerTeam: BattleTeam, success: boolean): void {
    try {
      const BattleBaseScore = {
        breakSuc: 0.3,    // 突破成功加分
        breakFail: -0.1   // 突破失败扣分
      };

      // 确保评分映射存在
      if (!attackerTeam.statistic.ballerScoreMap) {
        attackerTeam.statistic.ballerScoreMap = new Map();
      }

      // 基于old项目：突破评分更新A1球员
      const breakHeroId = attackerTeam.roundInfo.A1Info?.heroId;
      if (breakHeroId) {
        const currentScore = attackerTeam.statistic.ballerScoreMap.get(breakHeroId) || 6.0;
        const newScore = currentScore + (success ? BattleBaseScore.breakSuc : BattleBaseScore.breakFail);
        attackerTeam.statistic.ballerScoreMap.set(breakHeroId, newScore);
      }
    } catch (error) {
      this.logger.error('更新突破球员评分失败', error);
    }
  }

  /**
   * 🔧 更新球员评分
   * 基于old项目的BattleBaseScore系统
   */
  private updatePlayerScores(attackerTeam: BattleTeam, defenderTeam: BattleTeam, attackMode: AttackMode, isSuccess: boolean): void {
    try {
      const BattleBaseScore = {
        shotSuc: 0.5,     // 射门成功加分
        shotFail: -0.2,   // 射门失败扣分
        GKSuc: 0.4,       // 守门成功加分
        GKFail: -0.3      // 守门失败扣分
      };

      // 确保评分映射存在
      if (!attackerTeam.statistic.ballerScoreMap) {
        attackerTeam.statistic.ballerScoreMap = new Map();
      }
      if (!defenderTeam.statistic.ballerScoreMap) {
        defenderTeam.statistic.ballerScoreMap = new Map();
      }

      // 基于old项目：角球由A2去射门，其他由A1射门
      const shotHeroId = attackMode === 8 ?
        attackerTeam.roundInfo.A2Info?.heroId :
        attackerTeam.roundInfo.A1Info?.heroId;

      const gkHeroId = defenderTeam.roundInfo.GKInfo?.heroId;

      // 更新射门球员评分
      if (shotHeroId) {
        const currentScore = attackerTeam.statistic.ballerScoreMap.get(shotHeroId) || 6.0;
        const newScore = currentScore + (isSuccess ? BattleBaseScore.shotSuc : BattleBaseScore.shotFail);
        attackerTeam.statistic.ballerScoreMap.set(shotHeroId, newScore);
      }

      // 更新门将评分
      if (gkHeroId) {
        const currentScore = defenderTeam.statistic.ballerScoreMap.get(gkHeroId) || 6.0;
        const newScore = currentScore + (isSuccess ? BattleBaseScore.GKFail : BattleBaseScore.GKSuc);
        defenderTeam.statistic.ballerScoreMap.set(gkHeroId, newScore);
      }
    } catch (error) {
      this.logger.error('更新球员评分失败', error);
    }
  }

  /**
   * 根据进攻方式判断是否需要突破阶段
   * 基于old项目逻辑：头球、任意球、点球没有突破阶段
   */
  needBreakThroughPeriod(attackMode: number): boolean {
    // 1=头球, 7=任意球, 9=点球 不需要突破阶段
    return ![1, 7, 9].includes(attackMode);
  }

  /**
   * 根据进攻方式和阶段确定参与的球员类型
   * 基于old项目的球员选择逻辑
   */
  getParticipatingPlayerTypes(attackMode: number, period: number): string[] {
    const playerTypes: string[] = ['A1']; // A1总是参与
    
    switch (period) {
      case 0: // 发起阶段
        // 角球需要A2参与
        if (attackMode === 8) {
          playerTypes.push('A2');
        }
        break;
        
      case 1: // 推进阶段
        // 部分进攻方式需要B参与防守
        if ([2, 3, 4, 5, 6, 8].includes(attackMode)) {
          playerTypes.push('B');
        }
        break;
        
      case 2: // 射门阶段
        // 部分进攻方式需要A2参与协助进攻
        if ([1, 3, 4, 8].includes(attackMode)) {
          playerTypes.push('A2');
        }
        // 门将总是参与射门防守
        playerTypes.push('GK');
        // 部分进攻方式需要B参与射门防守
        if ([1, 3, 4].includes(attackMode)) {
          playerTypes.push('B');
        }
        break;
    }
    
    return playerTypes;
  }

  /**
   * 🔧 检查特殊进攻方式
   * 基于old项目的特殊情况判断
   *
   * @param attackerTeam 攻击方队伍数据 - 用于判断特殊攻击条件
   * @returns AttackMode | null 特殊攻击模式，null表示无特殊情况
   */
  private checkSpecialAttackMode(attackerTeam: BattleTeam): AttackMode | null {
    try {
      // 当前实现：BattleTeam接口中没有specialSituation属性
      // 在完整实现中，这里应该检查特殊情况（任意球、角球、点球等）
      // 暂时返回null，表示无特殊情况

      // 检查是否满足单刀条件（基于old项目逻辑）
      if (this.checkOneOnOneCondition(attackerTeam)) {
        return AttackMode.OneOnOne; // 单刀
      }

      return null; // 无特殊情况
    } catch (error) {
      this.logger.warn('检查特殊进攻方式失败', error);
      return null;
    }
  }

  /**
   * 🔧 调整进攻方式权重
   * 基于队伍属性和战术调整权重
   *
   * @param attackerTeam 攻击方队伍数据 - 包含攻击力、防守力、士气等属性
   * @param attackModes 攻击模式配置数组 - 基础权重配置
   * @returns AttackModeConfig[] 调整后的攻击模式配置数组
   */
  private adjustAttackModeWeights(
    attackerTeam: BattleTeam,
    attackModes: AttackModeConfig[]
  ): AttackModeConfig[] {
    try {
      const adjustedModes = attackModes.map(mode => ({ ...mode }));

      // 根据队伍属性调整权重 - 基于现有的totalAttack和totalDefend
      const teamAttack = attackerTeam.totalAttack || 0;
      const teamDefend = attackerTeam.totalDefend || 0;
      const teamMorale = attackerTeam.attr.morale || 0;

      for (const mode of adjustedModes) {
        switch (mode.id) {
          case AttackMode.Header: // 头球 - 基于攻击力
            mode.weight += Math.floor(teamAttack / 2000);
            break;
          case AttackMode.LongShot: // 远射 - 基于攻击力
            mode.weight += Math.floor(teamAttack / 1500);
            break;
          case AttackMode.Scramble: // 抢点 - 基于士气
            mode.weight += Math.floor(teamMorale / 100);
            break;
          case AttackMode.OneOnOne: // 单刀 - 综合属性影响
            mode.weight += Math.floor((teamAttack + teamMorale) / 3000);
            break;
        }

        // 确保权重不为负数
        mode.weight = Math.max(1, mode.weight);
      }

      return adjustedModes;
    } catch (error) {
      this.logger.warn('调整进攻方式权重失败', error);
      return attackModes;
    }
  }

  /**
   * 🔧 检查单刀条件
   * 基于old项目的单刀判断逻辑
   *
   * @param attackerTeam 攻击方队伍数据 - 用于判断单刀条件
   * @returns boolean 是否满足单刀条件
   */
  private checkOneOnOneCondition(attackerTeam: BattleTeam): boolean {
    try {
      // 基于old项目的单刀条件判断逻辑
      // 单刀是一种稀有的攻击方式，需要满足多个条件

      const attackPower = attackerTeam.totalAttack || 0;
      // 注意：BattleTeam接口中没有opponentDefend属性，使用totalDefend作为参考
      const defendPower = attackerTeam.totalDefend || 0;
      const morale = attackerTeam.attr.morale || 500;

      // 1. 攻击力必须明显高于防守力
      const attackAdvantage = attackPower - defendPower;
      if (attackAdvantage < 1000) {
        return false; // 攻击优势不足
      }

      // 2. 士气影响单刀概率
      const moraleBonus = Math.max(0, (morale - 400) / 400); // 士气400以上才有加成

      // 3. 基础单刀概率（非常低）
      let oneOnOneProbability = 0.02; // 基础2%概率

      // 4. 攻击优势加成
      oneOnOneProbability += Math.min(0.03, attackAdvantage / 50000); // 最多3%加成

      // 5. 士气加成
      oneOnOneProbability += moraleBonus * 0.02; // 最多2%加成

      // 6. 随机因子
      const randomFactor = Math.random();

      // 7. 最终概率限制在7%以内（单刀是稀有事件）
      const finalProbability = Math.min(0.07, oneOnOneProbability);

      const isOneOnOne = randomFactor < finalProbability;

      if (isOneOnOne) {
        this.logger.debug(`单刀触发: 攻击=${attackPower}, 防守=${defendPower}, 优势=${attackAdvantage}, 士气=${morale}, 概率=${finalProbability.toFixed(4)}`);
      }

      return isOneOnOne;
    } catch (error) {
      this.logger.warn('检查单刀条件失败', error);
      return false;
    }
  }

  /**
   * 🔧 计算每回合动作ID
   * 基于old项目: Room.prototype.calcRoundActionID
   * 核心功能：根据攻击模式和阶段从Action配置表中随机选择动作ID
   */
  async calcRoundActionID(attackMode: number, period: number): Promise<number> {
    try {
      // 从配置表获取动作配置
      const actionConfigs = await this.gameConfig.action.getAll();

      if (!actionConfigs || actionConfigs.length === 0) {
        this.logger.warn('动作配置表为空');
        return 0;
      }

      // 筛选符合条件的动作ID
      // period+1 是因为配置表中period从1开始，而代码中period从0开始
      const validActions = actionConfigs.filter(config =>
        config.attackMode === attackMode && config.period === (period + 1)
      );

      this.logger.debug(`动作筛选: 攻击模式=${attackMode}, 阶段=${period}, 找到${validActions.length}个动作`);

      if (validActions.length === 0) {
        this.logger.warn(`未找到匹配的动作: 攻击模式=${attackMode}, 阶段=${period}`);
        return 0;
      }

      // 随机选择一个动作ID
      const randomIndex = Math.floor(Math.random() * validActions.length);
      const selectedAction = validActions[randomIndex];
      const actionID = selectedAction.id;

      this.logger.debug(`选择动作ID: ${actionID}, 动作名称: ${selectedAction.name || '未知'}`);

      return actionID;

    } catch (error) {
      this.logger.error('计算回合动作ID失败', error);
      return 0;
    }
  }

  /**
   * 🔧 获取动作配置信息
   * 辅助方法：根据动作ID获取详细配置
   */
  async getActionConfig(actionID: number): Promise<any> {
    try {
      const actionConfig = await this.gameConfig.action.get(actionID);
      return actionConfig || null;
    } catch (error) {
      this.logger.error(`获取动作配置失败: ${actionID}`, error);
      return null;
    }
  }

  /**
   * 🔧 验证动作有效性
   * 辅助方法：检查动作ID是否有效
   */
  async validateActionID(actionID: number, attackMode: number, period: number): Promise<boolean> {
    try {
      const actionConfig = await this.getActionConfig(actionID);

      if (!actionConfig) {
        return false;
      }

      // 验证动作是否匹配当前攻击模式和阶段
      return actionConfig.attackMode === attackMode && actionConfig.period === (period + 1);

    } catch (error) {
      this.logger.error('验证动作ID失败', error);
      return false;
    }
  }

  /**
   * 🔧 基于配置表的属性计算 - 与room.js完全兼容
   * 根据AttackMode配置表计算攻击和防守数值
   */
  private async calculateConfigBasedAttributes(
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    attackMode: AttackMode,
    roundIndex: number,
    phase: 'breakthrough' | 'shot'
  ): Promise<{ attackValue: number; defendValue: number }> {
    try {
      // 获取AttackMode配置
      const attackModeConfig = await this.gameConfig.attackMode.get(attackMode);
      if (!attackModeConfig) {
        throw new Error(`AttackMode配置不存在: ${attackMode}`);
      }

      let attackValue = 0;
      let defendValue = 0;

      // 根据阶段选择配置字段
      const configPrefix = phase === 'breakthrough' ? 'Br' : 'Sh';

      // 计算攻击方数值
      const A1Type1 = attackModeConfig[`A1${configPrefix}Type1`];
      const A1Factor1 = attackModeConfig[`A1${configPrefix}Factor1`];
      const A1Type2 = attackModeConfig[`A1${configPrefix}Type2`];
      const A1Factor2 = attackModeConfig[`A1${configPrefix}Factor2`];

      if (A1Type1 && A1Factor1) {
        const attrName = this.getAttributeNameByIndex(A1Type1);
        if (attrName && this.battleSkillSystem) {
          // 🔧 需要获取具体的攻击球员，而不是整个队伍
          const attackerHero = this.getAttackerHero(attackerTeam, attackMode, roundIndex);
          if (attackerHero) {
            const attrObj = this.battleSkillSystem.getHeroBattleAttr(
              attackerTeam.teamSide || 'teamA',
              attackerHero,
              attrName,
              this.battleType, // 🔧 使用传递的battleType
              attackerTeam,
              roundIndex
            );
            attackValue += (attrObj.cur + attrObj.add) * A1Factor1;
          }
        }
      }

      if (A1Type2 && A1Factor2) {
        const attrName = this.getAttributeNameByIndex(A1Type2);
        if (attrName && this.battleSkillSystem) {
          const attackerHero = this.getAttackerHero(attackerTeam, attackMode, roundIndex);
          if (attackerHero) {
            const attrObj = this.battleSkillSystem.getHeroBattleAttr(
              attackerTeam.teamSide || 'teamA',
              attackerHero,
              attrName,
              this.battleType, // 🔧 使用传递的battleType
              attackerTeam,
              roundIndex
            );
            attackValue += (attrObj.cur + attrObj.add) * A1Factor2;
          }
        }
      }

      // 计算防守方数值
      const BType1 = attackModeConfig[`B${configPrefix}Type1`];
      const BFactor1 = attackModeConfig[`B${configPrefix}Factor1`];
      const BType2 = attackModeConfig[`B${configPrefix}Type2`];
      const BFactor2 = attackModeConfig[`B${configPrefix}Factor2`];

      if (BType1 && BFactor1) {
        const attrName = this.getAttributeNameByIndex(BType1);
        if (attrName && this.battleSkillSystem) {
          const defenderHero = this.getDefenderHero(defenderTeam, attackMode, roundIndex);
          if (defenderHero) {
            const attrObj = this.battleSkillSystem.getHeroBattleAttr(
              defenderTeam.teamSide || 'teamB',
              defenderHero,
              attrName,
              this.battleType, // 🔧 使用传递的battleType
              defenderTeam,
              roundIndex
            );
            defendValue += (attrObj.cur + attrObj.add) * BFactor1;
          }
        }
      }

      if (BType2 && BFactor2) {
        const attrName = this.getAttributeNameByIndex(BType2);
        if (attrName && this.battleSkillSystem) {
          const defenderHero = this.getDefenderHero(defenderTeam, attackMode, roundIndex);
          if (defenderHero) {
            const attrObj = this.battleSkillSystem.getHeroBattleAttr(
              defenderTeam.teamSide || 'teamB',
              defenderHero,
              attrName,
              this.battleType, // 🔧 使用传递的battleType
              defenderTeam,
              roundIndex
            );
            defendValue += (attrObj.cur + attrObj.add) * BFactor2;
          }
        }
      }

      // 确保最小值
      attackValue = Math.max(attackValue, 100);
      defendValue = Math.max(defendValue, 100);

      return { attackValue, defendValue };
    } catch (error) {
      this.logger.error('配置表属性计算失败', error);
      // 降级到简化计算
      return {
        attackValue: attackerTeam.totalAttack || 1000,
        defendValue: defenderTeam.totalDefend || 1000
      };
    }
  }

  /**
   * 🔧 属性索引转换为属性名称
   * 与room.js的commonEnum.ONE_LEVEL_ATTR_INDEX_TO_NAMES保持一致
   */
  private getAttributeNameByIndex(index: number): string | null {
    // 与old项目enum.js的ONE_LEVEL_ATTR_INDEX_TO_NAMES完全一致，转换为小写真实字段名
    const attrMap: { [key: number]: string } = {
      0: 'default',
      1: 'speed',
      2: 'jumping',
      3: 'strength',
      4: 'stamina',
      5: 'finishing',
      6: 'dribbling',
      7: 'passing',
      8: 'heading',
      9: 'standingTackle',
      10: 'slidingTackle',
      11: 'longPassing',
      12: 'longShots',
      13: 'penalties',
      14: 'cornerKick',
      15: 'freeKick',
      16: 'explosiveForce',
      17: 'attack',
      18: 'volleys',
      19: 'save',
      20: 'resistanceDamage'
    };
    return attrMap[index] || null;
  }

  /**
   * 🔧 降级计算方法 - 当配置表计算失败时使用
   */
  private calcMiddlePeriodFallback(
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    skillAddPer: number
  ): PeriodResult {
    const { baseRate, moraleBonus } = this.calculateTeamComparison(attackerTeam, defenderTeam);
    let adjustedRate = Math.max(0.1, Math.min(0.9, baseRate + moraleBonus + skillAddPer / 100));
    const success = this.fastRandom() < adjustedRate;

    this.logger.debug(`推进阶段(降级): 基础=${baseRate.toFixed(3)}, 最终=${adjustedRate.toFixed(3)}, 结果=${success}`);
    if (isNaN(adjustedRate)) {
      this.logger.error(`推进阶段(降级)计算产生NaN: baseRate=${baseRate}, moraleBonus=${moraleBonus}, skillAddPer=${skillAddPer}`);
      return { success: false, rate: 0.5 };
    }
    return { success, rate: adjustedRate };
  }

  /**
   * 🔧 获取攻击球员 - 基于room.js逻辑
   * 根据攻击模式和回合选择合适的攻击球员
   *
   * @param attackerTeam 攻击方队伍数据 - 包含球员列表
   * @param attackMode 攻击模式 - 当前实现中未使用，保留接口兼容性
   * @param roundIndex 回合索引 - 当前实现中未使用，保留接口兼容性
   * @returns BattleHero | null 选中的攻击球员，null表示未找到合适球员
   */
  private getAttackerHero(attackerTeam: BattleTeam, _attackMode: AttackMode, _roundIndex: number): BattleHero | null {
    try {
      // 优先使用roundInfo中存储的球员信息
      if (attackerTeam.roundInfo?.A1Info?.heroId) {
        const selectedHeroUid = attackerTeam.roundInfo.A1Info.heroId;
        const selectedHero = attackerTeam.heroes?.find(hero => hero.heroId === selectedHeroUid);
        if (selectedHero) {
          return selectedHero;
        }
      }

      // 降级：如果roundInfo中没有球员信息，使用原有逻辑
      if (attackerTeam.heroes && attackerTeam.heroes.length > 0) {
        const availableHeroes = attackerTeam.heroes.filter((hero: BattleHero) => hero && hero.heroId);
        if (availableHeroes.length > 0) {
          const randomIndex = Math.floor(Math.random() * availableHeroes.length);
          return availableHeroes[randomIndex];
        }
      }
      return null;
    } catch (error) {
      this.logger.error('获取攻击球员失败', error);
      return null;
    }
  }

  /**
   * 🔧 获取防守球员 - 基于room.js逻辑
   * 根据攻击模式和回合选择合适的防守球员
   *
   * @param defenderTeam 防守方队伍数据 - 包含球员列表
   * @param attackMode 攻击模式 - 当前实现中未使用，保留接口兼容性
   * @param roundIndex 回合索引 - 当前实现中未使用，保留接口兼容性
   * @returns BattleHero | null 选中的防守球员，null表示未找到合适球员
   */
  private getDefenderHero(defenderTeam: BattleTeam, _attackMode: AttackMode, _roundIndex: number): BattleHero | null {
    try {
      // 优先使用roundInfo中存储的球员信息
      // 检查防守球员B
      if (defenderTeam.roundInfo?.BInfo?.heroId) {
        const selectedHeroId = defenderTeam.roundInfo.BInfo.heroId;
        const selectedHero = defenderTeam.heroes?.find(hero => hero.heroId === selectedHeroId);
        if (selectedHero) {
          return selectedHero;
        }
      }

      // 检查门将（射门阶段）
      if (defenderTeam.roundInfo?.GKInfo?.heroId) {
        const selectedHeroId = defenderTeam.roundInfo.GKInfo.heroId;
        const selectedHero = defenderTeam.heroes?.find(hero => hero.heroId === selectedHeroId);
        if (selectedHero) {
          return selectedHero;
        }
      }

      // 降级：如果roundInfo中没有球员信息，使用原有逻辑
      if (defenderTeam.heroes && defenderTeam.heroes.length > 0) {
        const availableHeroes = defenderTeam.heroes.filter((hero: BattleHero) => hero && hero.heroId);
        if (availableHeroes.length > 0) {
          // 对于防守，优先选择防守位置的球员
          const defenders = availableHeroes.filter((hero: any) => {
            const pos = hero.position || hero.originalData?.position1;
            return pos && ['GK', 'DC', 'DL', 'DR', 'DM'].includes(pos);
          });

          if (defenders.length > 0) {
            const randomIndex = Math.floor(Math.random() * defenders.length);
            return defenders[randomIndex];
          } else {
            // 如果没有防守球员，随机选择
            const randomIndex = Math.floor(Math.random() * availableHeroes.length);
            return availableHeroes[randomIndex];
          }
        }
      }
      return null;
    } catch (error) {
      this.logger.error('获取防守球员失败', error);
      return null;
    }
  }

}
