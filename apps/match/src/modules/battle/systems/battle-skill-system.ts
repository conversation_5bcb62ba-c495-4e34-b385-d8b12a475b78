import { TeamType } from '@match/modules/battle/types/battle-data.types';
import { Logger } from '@nestjs/common';
import { GameConfigFacade } from '@libs/game-config';
import { BattleTeam, BattleHero, RuntimePeriodInfo } from '../types/battle-data.types';
import { BattleType } from '@libs/game-constants';

// 技能对象接口 - 基于old项目的技能记录结构
interface SkillObject {
  skillId: number;
  heroId: string;
  skillLevel: number;
  skillConfig: HeroSkillDefinition; // 从gameConfig获取的配置
  buffList: BuffInfo[];     // Buff列表 - 基于old项目skillObj.buffList
  skillInfo: {              // 技能信息 - 基于old项目skillObj.skillInfo
    continueTime: number;   // 持续时间 (秒)
    skillType: number;      // 生效方式: 0.技能激活 1.被动 2.几率
    radio: number;          // 触发几率
    startTime: number;      // 开始时间
    endTime: number;        // 结束时间
    calcState: number;      // 计算状态
    isTriggered: boolean;   // 特殊触发标记（用于下次攻击技能等特殊情况）
    teamSide: string;       // 队伍
    hasTrigger: boolean;    // 通用触发标记（持续性和下次攻击技能触发后不再触发）
  };
  isActive: boolean;
  remainingDuration: number;
  // 基于old项目第2724-2725行和第2731-2732行的记录结构
  round?: number;    // 触发回合
  period?: number;   // 触发阶段
}

import { HeroSkillDefinition } from '@libs/game-config';

// Buff信息接口 - 基于old项目initSingleBuffInfo的结构
interface BuffInfo {
  opType: number;             // 触发类型
  effectType: number;         // 效果类型
  value: number;              // 参数
  atkTeamSide: string;        // 进攻方 (teamA or teamB)
  attackMode: number;         // 触发进攻方式
  period: number;             // 触发阶段: 0:士气槽阶段 1:突破 2:射门 3:突破+射门
  effectTeamSide: string;     // 生效哪一方
}

// 技能效果记录接口 - 基于old项目第3164-3167行的oneSkillRecord结构
interface SkillEffectRecord {
  skillId: number;
  teamType: string;
  addPer: number;
  heroId: string; // old项目中是heroUid，当前项目统一使用heroId
}

/**
 * 战斗技能系统
 *
 * 基于old项目room.js的技能系统架构，完整实现技能的初始化、触发、计算和效果应用
 *
 * 核心职责：
 * - 技能数据初始化: 从球员activeSkills读取并分类技能
 * - 技能触发检查: 基于period、attackMode、round条件触发技能
 * - 技能效果计算: 计算技能对成功率和属性的影响
 * - 技能效果记录: 填充skillEffectList供客户端回放使用
 * - 球员属性加成: 提供技能对球员属性的实时加成
 *
 * 🔧 skillEffectList vs skillRecords 的区别：
 *
 * 1. skillEffectList（实时效果记录）：
 *    - 位置：periodInfo[period].skillEffectList
 *    - 用途：记录当前阶段生效的技能效果，影响成功率计算
 *    - 格式：{skillId, teamType, addPer, heroId}
 *    - 生命周期：每个period重新生成
 *
 * 2. skillRecords（完整技能记录）：
 *    - 位置：battleResult.skillRecords
 *    - 用途：记录整场战斗的技能触发历史，用于回放和统计
 *    - 格式：[{durRecord, insRecord, nextAtkRecord}, {...}]
 *    - 生命周期：整场战斗累积
 *
 * 🔧 技能重复触发规则（基于old项目第2888行和2914行）：
 * - 持续性技能：触发后不再触发（hasTrigger=true后不再触发）
 * - 下次攻击技能：触发后不再触发（hasTrigger=true后不再触发）
 * - 瞬时技能：可以重复触发（每次满足条件都可能触发）
 *
 * 数据流向：
 * initSkillSystem → triggerSkillsInPeriod → calcSkillChangePer → fillSkillEffectList
 */
export class BattleSkillSystem {
  private readonly logger = new Logger(BattleSkillSystem.name);

  // 🔧 技能模板记录 - 存储球员的技能配置信息（初始化时从配置表加载）
  private durativeSkillRecord: SkillObject[][] = [[], []]; // [teamA, teamB] 持续性技能模板
  private instantSkillRecord: SkillObject[][] = [[], []];  // [teamA, teamB] 瞬时性技能模板
  private nextAtkSkillRecord: SkillObject[][] = [[], []];  // [teamA, teamB] 下次攻击技能模板

  // 🔧 技能触发记录 - 存储战斗过程中实际触发的技能实例（用于生成skillRecords）
  private triggeredDurativeSkillRecord: SkillObject[][] = [[], []]; // 实际触发的持续性技能
  private triggeredInstantSkillRecord: SkillObject[][] = [[], []];  // 实际触发的瞬时性技能
  private triggeredNextAtkSkillRecord: SkillObject[][] = [[], []];  // 实际触发的下次攻击技能

  constructor(private readonly gameConfig: GameConfigFacade) {}

  /**
   * 🔧 技能系统初始化
   * 基于old项目: Room.prototype.initAllSkillInfo
   */
  async initSkillSystem(teamA: any, teamB: any): Promise<void> {
    // 清空技能记录
    this.durativeSkillRecord = [[], []];
    this.instantSkillRecord = [[], []];
    this.nextAtkSkillRecord = [[], []];
    // 初始化触发记录数组，结构化拆分初始化模板与触发实例
    this.triggeredDurativeSkillRecord = [[], []];
    this.triggeredInstantSkillRecord = [[], []];
    this.triggeredNextAtkSkillRecord = [[], []];

    try {
      // 初始化队伍A的技能
      await this.initTeamSkills(teamA, 0);

      // 初始化队伍B的技能
      await this.initTeamSkills(teamB, 1);

      this.logger.debug('技能系统初始化完成');
    } catch (error) {
      this.logger.error('技能系统初始化失败', error);
    }
  }

  /**
   * 🔧 技能触发检查 - 阶段触发
   * 基于old项目: Room.prototype.doSkillTriggerWhenPeriod
   */
  triggerSkillsInPeriod(
    period: number,
    attackMode: number,
    roundIndex: number,
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    participatingPlayers: string[]
  ): number {
    // 基于old项目: Room.prototype.doSkillTriggerWhenPeriod
    this.logger.debug(`触发技能: 阶段=${period}, 攻击模式=${attackMode}, 回合=${roundIndex}`);

    try {
      // 构建参与球员列表 - 基于old项目第2790-2806行
      const heroList = [];

      // 攻击方球员
      if (attackerTeam.roundInfo.A1Info?.heroId) {
        heroList.push({
          heroId: attackerTeam.roundInfo.A1Info.heroId,
          teamSide: 'teamA',
          type: 'A1'
        });
      }
      if (attackerTeam.roundInfo.A2Info?.heroId) {
        heroList.push({
          heroId: attackerTeam.roundInfo.A2Info.heroId,
          teamSide: 'teamA',
          type: 'A2'
        });
      }

      // 防守方球员
      if (defenderTeam.roundInfo.BInfo?.heroId) {
        heroList.push({
          heroId: defenderTeam.roundInfo.BInfo.heroId,
          teamSide: 'teamB',
          type: 'B'
        });
      }
      if (defenderTeam.roundInfo.GKInfo?.heroId) {
        heroList.push({
          heroId: defenderTeam.roundInfo.GKInfo.heroId,
          teamSide: 'teamB',
          type: 'GK'
        });
      }

      // 为每个参与球员触发技能 - 基于old项目第2808-2816行
      for (const heroInfo of heroList) {
        this.triggerAndRecordSkillByType(roundIndex, period, heroInfo, 'durative', attackMode);
        this.triggerAndRecordSkillByType(roundIndex, period, heroInfo, 'instant', attackMode);
        this.triggerAndRecordSkillByType(roundIndex, period, heroInfo, 'nextAttack', attackMode);
      }

      this.logger.debug(`技能记录生成完成: 持续=${this.durativeSkillRecord.length}, 瞬时=${this.instantSkillRecord.length}, 下次攻击=${this.nextAtkSkillRecord.length}`);

      return 0; // 这里返回0，实际的技能加成在calcSkillChangePer中计算
    } catch (error) {
      this.logger.error('技能触发失败', error);
      return 0;
    }
  }

  /**
   * 🔧 触发并记录技能
   * 基于old项目: Room.prototype.triggerAndRecordSkillByType
   */
  private triggerAndRecordSkillByType(
    roundIndex: number,
    period: number,
    heroInfo: { heroId: string; teamSide: string; type: string },
    skillInterType: string,
    attackMode: number
  ): void {
    try {
      const teamIndex = heroInfo.teamSide === 'teamA' ? 0 : 1;
      let record: SkillObject[][];
      let sourceSkillRecord: SkillObject[][];

      // 选择对应的技能记录数组
      switch (skillInterType) {
        case 'durative':
          // 重构为触发记录数组，便于后续处理
          record = this.triggeredDurativeSkillRecord; // 触发记录数组
          sourceSkillRecord = this.durativeSkillRecord; // 源技能记录数组
          break;
        case 'instant':
          record = this.triggeredInstantSkillRecord; // 触发记录数组
          sourceSkillRecord = this.instantSkillRecord; // 源技能记录数组
          break;
        case 'nextAttack':
          record = this.triggeredNextAtkSkillRecord; // 触发记录数组
          sourceSkillRecord = this.nextAtkSkillRecord; // 源技能记录数组
          break;
        default:
          this.logger.error(`未知技能交互类型: ${skillInterType}`);
          return;
      }

      // 从初始化时的技能记录中查找该球员的技能
      const initialSkills = sourceSkillRecord[teamIndex] || [];

      for (const skillObj of initialSkills) {
        // 只处理该球员的技能
        if (skillObj.heroId !== heroInfo.heroId) {
          continue;
        }

        // 检查技能是否应该在当前阶段触发
        const shouldTrigger = this.checkSingleSkillTrigger(
          skillObj, skillInterType, roundIndex, heroInfo, period, attackMode
        );

        if (shouldTrigger.isTrigger) {
          this.logger.debug(`技能触发: 技能ID=${skillObj.skillId}, 球员=${heroInfo.heroId}`);

          // 创建技能记录对象 - 基于old项目第2719-2743行
          const recordObj: SkillObject = {
            skillId: skillObj.skillId,
            heroId: heroInfo.heroId,
            skillLevel: skillObj.skillLevel,
            skillConfig: skillObj.skillConfig,
            buffList: skillObj.buffList,
            skillInfo: {
              ...skillObj.skillInfo,
              startTime: shouldTrigger.startTime || 0,
              endTime: shouldTrigger.endTime || 0,
              hasTrigger: true
            },
            isActive: true,
            remainingDuration: skillObj.remainingDuration,
            round: roundIndex,    // 关键字段
            period: period        // 关键字段
          };

          // 触发后标记源技能避免重复触发（持续/下一次攻击）
          if (skillInterType !== 'instant') {
            // 标记源技能模板已触发
            skillObj.skillInfo.hasTrigger = true;
          }

          // 添加到对应的触发记录数组中（仅存放带period/round的实例）
          record[teamIndex].push(recordObj);

          this.logger.debug(`技能记录添加: 技能ID=${recordObj.skillId}, 球员=${heroInfo.heroId}, 类型=${skillInterType}, 回合=${roundIndex}, 阶段=${period}`);
        }
      }
    } catch (error) {
      this.logger.error(`触发技能记录失败: ${heroInfo.heroId}`, error);
    }
  }

  /**
   * 🔧 计算技能对成功率的影响
   * 基于old项目: Room.prototype.calcSkillChangePer (第3117-3175行)
   */
  calcSkillChangePer(period: number, attackMode: number, roundIndex: number): number {
    try {
      let totalPer = 0;

      // 基于old项目第3125-3137行：遍历三种技能类型
      // 重构为数组，便于后续扩展
      const skillInterTypes = [
        { record: this.triggeredDurativeSkillRecord },
        { record: this.triggeredInstantSkillRecord },
        { record: this.triggeredNextAtkSkillRecord }
      ];

      for (const { record } of skillInterTypes) {
        // 遍历两个队伍
        for (let teamIndex = 0; teamIndex < 2; teamIndex++) {
          const records = record[teamIndex] || [];

          // 遍历触发记录（已包含 period/round）
          for (const triggerRecord of records) {
            if (period !== triggerRecord.period || roundIndex !== triggerRecord.round) {
              continue;
            }

            const buffList = triggerRecord.buffList || [];
            for (const buffInfo of buffList) {
              if (this.getSkillEffectType(buffInfo) !== 'SuccessPer') {
                continue;
              }
              const tmpValue = this.getBuffEffectValue(buffInfo);
              if (tmpValue !== 0) {
                totalPer += tmpValue;
                this.logger.debug(`技能成功率加成: 技能ID=${triggerRecord.skillId}, 球员=${triggerRecord.heroId}, 加成=${tmpValue}`);
                break; // 找到有效效果后跳出
              }
            }
          }
        }
      }

      this.logger.debug(`技能成功率计算完成: 阶段=${period}, 进攻方式=${attackMode}, 回合=${roundIndex}, 总影响=${totalPer}`);
      return totalPer;
    } catch (error) {
      this.logger.error('技能成功率计算失败', error);
      return 0;
    }
  }

  /**
   * 🔧 获取球员战斗属性（包含技能加成）- 完整实现版本
   * 基于old项目: Room.prototype.getHeroBattleAttr
   *
   * 🚀 完整功能：
   * - 基础属性获取（Base + Cur）
   * - 信仰之战鼓舞加成
   * - 位置适配率计算
   * - 技能属性加成（持续性、瞬时性、下次攻击）
   * - 与room.js完全兼容的计算逻辑
   */
  /**
   * 🔧 获取英雄战斗属性 - 完整实现版本
   *
   * 📋 完整计算流程：
   *
   * 【第一阶段：基础属性获取】
   * 1. 从hero.oneLevelAttr获取基础属性值(Base)和当前值(Cur)
   * 2. 处理属性名称为'Default'的特殊情况
   *
   * 【第二阶段：信仰之战鼓舞加成】
   * 3. 检查战斗类型是否为PvpWarOfFaith(4)
   * 4. 根据鼓舞等级(inspireNum)计算加成百分比
   * 5. 应用鼓舞加成到当前属性值
   *
   * 【第三阶段：位置适配率计算】
   * 6. 获取英雄当前位置和原始位置
   * 7. 计算位置适配率(0.8-1.0)
   * 8. 应用位置适配率到属性值
   *
   * 【第四阶段：技能属性加成】
   * 9. 持续性技能效果计算
   * 10. 瞬时性技能效果计算
   * 11. 下次攻击技能效果计算
   * 12. 累加所有技能加成值
   *
   * @param teamSide 队伍方向 - 'A'或'B'，用于技能记录索引
   * @param hero 英雄数据 - 包含完整的属性和技能信息
   * @param attrName 属性名称 - 如'Attack', 'Defend', 'Speed'等
   * @param roundIndex 回合索引 - 用于技能触发判断
   * @param battleType 战斗类型 - 可选，用于特殊战斗加成
   * @param teamInfo 队伍信息 - 可选，包含鼓舞等级等信息
   * @returns 属性对象 - {cur: 当前值, add: 加成值}
   */
  getHeroBattleAttr(
    teamSide: string,
    hero: BattleHero,
    attrName: string,
    battleType: BattleType,
    teamInfo: BattleTeam,
    roundIndex: number = 0
  ): { cur: number; add: number } {
    if (attrName === 'default') {
      return { cur: 0, add: 0 };
    }

    // 🔧 基础属性值 - 从BattleHero的核心属性获取
    let base = 0;
    let cur = 0;

    // 直接使用BattleHero.attributes中真实存在的字段（基于hero.schema.ts HeroAttributes）
    switch (attrName) {
      case 'speed':
        cur = hero.attributes.speed.cur;
        base = hero.attributes.speed.base;
        break;
      case 'jumping':
        cur = hero.attributes.jumping.cur;
        base = hero.attributes.jumping.base;
        break;
      case 'strength':
        cur = hero.attributes.strength.cur;
        base = hero.attributes.strength.base;
        break;
      case 'stamina':
        cur = hero.attributes.stamina.cur;
        base = hero.attributes.stamina.base;
        break;
      case 'finishing':
        cur = hero.attributes.finishing.cur;
        base = hero.attributes.finishing.base;
        break;
      case 'dribbling':
        cur = hero.attributes.dribbling.cur;
        base = hero.attributes.dribbling.base;
        break;
      case 'passing':
        cur = hero.attributes.passing.cur;
        base = hero.attributes.passing.base;
        break;
      case 'heading':
        cur = hero.attributes.heading.cur;
        base = hero.attributes.heading.base;
        break;
      case 'standingTackle':
        cur = hero.attributes.standingTackle.cur;
        base = hero.attributes.standingTackle.base;
        break;
      case 'slidingTackle':
        cur = hero.attributes.slidingTackle.cur;
        base = hero.attributes.slidingTackle.base;
        break;
      case 'longPassing':
        cur = hero.attributes.longPassing.cur;
        base = hero.attributes.longPassing.base;
        break;
      case 'longShots':
        cur = hero.attributes.longShots.cur;
        base = hero.attributes.longShots.base;
        break;
      case 'penalties':
        cur = hero.attributes.penalties.cur;
        base = hero.attributes.penalties.base;
        break;
      case 'cornerKick':
        cur = hero.attributes.cornerKick.cur;
        base = hero.attributes.cornerKick.base;
        break;
      case 'freeKick':
        cur = hero.attributes.freeKick.cur;
        base = hero.attributes.freeKick.base;
        break;
      case 'explosiveForce':
        cur = hero.attributes.explosiveForce.cur;
        base = hero.attributes.explosiveForce.base;
        break;
      case 'attack':
        cur = hero.attributes.attack.cur;
        base = hero.attributes.attack.base;
        break;
      case 'volleys':
        cur = hero.attributes.volleys.cur;
        base = hero.attributes.volleys.base;
        break;
      case 'save':
        cur = hero.attributes.save.cur;
        base = hero.attributes.save.base;
        break;
      case 'resistanceDamage':
        cur = hero.attributes.resistanceDamage.cur;
        base = hero.attributes.resistanceDamage.base;
        break;
      default:
        cur = 50;
        base = 50;
        break;
    }

    try {
      // 🔧 信仰之战鼓舞加成 - 与room.js完全一致的逻辑
      if (battleType === BattleType.PVP_WAR_OF_FAITH && teamInfo) {
        let addNum = 0;
        if (teamInfo.inspireNum === 1) {
          addNum = 10; // INSPIRE_1 = 10%
          cur = cur + Math.floor(base * addNum / 100);
        } else if (teamInfo.inspireNum === 2) {
          addNum = 20; // INSPIRE_2 = 20%
          cur = cur + Math.floor(base * addNum / 100);
        }
      }

      // 🔧 位置适配率计算 - 基于old项目逻辑
      if (teamInfo && hero.heroId) {
        const heroPosition = this.getHeroPosition(teamInfo, hero.heroId);
        const matchRate = this.calculatePositionMatchRate(hero, heroPosition);
        // 位置适配率影响基础属性
        const adjustedBase = Math.floor(base * matchRate);
        cur = cur + (adjustedBase - base); // 调整当前值
      }

      // 🔧 技能属性加成计算 - 完整的三类技能支持
      let addFactor = 0;
      const teamIndex = teamSide === 'teamA' ? 0 : 1;
      const nowTime = this.getCurrentBattleTime(roundIndex);

      // 遍历三种技能类型：持续性、瞬时性、下次攻击
      // 重构为数组，便于后续扩展
      const skillTypes = [
        { record: this.triggeredDurativeSkillRecord, type: 'durative' },
        { record: this.triggeredInstantSkillRecord, type: 'instant' },
        { record: this.triggeredNextAtkSkillRecord, type: 'nextAttack' }
      ];

      for (const skillType of skillTypes) {
        const skillList = skillType.record[teamIndex] || [];

        for (const skill of skillList) {
          // 检查技能是否对该球员生效
          if (skill.heroId !== hero.heroId) {
            continue;
          }

          // 检查技能时间有效性
          if (skill.skillInfo.endTime && nowTime > skill.skillInfo.endTime) {
            continue;
          }
          if (skill.skillInfo.startTime && nowTime < skill.skillInfo.startTime) {
            continue;
          }

          // 基于old项目第3008行：检查技能效果类型
          for (const buffInfo of skill.buffList) {
            if (this.getSkillEffectType(buffInfo) === 'AttrPer') {
              const tmpValue = this.getBuffEffectValue(buffInfo);
              if (tmpValue !== 0) {
                addFactor += tmpValue;
                break; // 找到有效效果后跳出
              }
            }
          }
        }
      }

      // 🔧 应用技能加成 - 基于基础属性的百分比加成
      const skillAdd = Math.floor(base * addFactor / 100);

      if (addFactor !== 0) {
        this.logger.debug(`球员属性技能加成: ${hero.name || hero.heroId}, 属性=${attrName}, 加成=${addFactor}%, 数值=${skillAdd}`);
      }

      return { cur, add: skillAdd };
    } catch (error) {
      this.logger.warn(`获取球员战斗属性失败: ${hero.heroId}`, error);
      return { cur, add: 0 };
    }
  }

  /**
   * 🔧 获取球员位置
   * 基于队伍信息和球员ID获取位置
   */
  private getHeroPosition(teamInfo: BattleTeam, heroId: string): string {
    try {
      // 从heroes数组中查找
      if (teamInfo.heroes) {
        const hero = teamInfo.heroes.find((h: BattleHero) => h.heroId === heroId);
        return hero?.position || 'MC';
      }

      return 'MC'; // 默认中场
    } catch (error) {
      this.logger.warn(`获取球员位置失败: ${heroId}`, error);
      return 'MC';
    }
  }

  /**
   * 🔧 计算位置适配率
   * 基于old项目的位置匹配逻辑
   */
  private calculatePositionMatchRate(hero: any, currentPosition: string): number {
    try {
      // 获取球员的主位置和副位置
      const position1 = hero.position1 || hero.mainPosition;
      const position2 = hero.position2 || hero.secondaryPosition;

      // 主位置或副位置匹配，100%适配
      if (currentPosition === position1 || currentPosition === position2) {
        return 1.0;
      }

      // 位置兼容性映射 - 基于old项目逻辑
      const positionCompatibility: { [key: string]: { [key: string]: number } } = {
        'GK': { 'GK': 1.0 },
        'DC': { 'DC': 1.0, 'DL': 0.8, 'DR': 0.8, 'MC': 0.6 },
        'DL': { 'DL': 1.0, 'DC': 0.8, 'ML': 0.9, 'WL': 0.7 },
        'DR': { 'DR': 1.0, 'DC': 0.8, 'MR': 0.9, 'WR': 0.7 },
        'MC': { 'MC': 1.0, 'ML': 0.8, 'MR': 0.8, 'DC': 0.6, 'ST': 0.6 },
        'ML': { 'ML': 1.0, 'MC': 0.8, 'DL': 0.9, 'WL': 0.9 },
        'MR': { 'MR': 1.0, 'MC': 0.8, 'DR': 0.9, 'WR': 0.9 },
        'ST': { 'ST': 1.0, 'MC': 0.6, 'WL': 0.7, 'WR': 0.7 },
        'WL': { 'WL': 1.0, 'ML': 0.9, 'DL': 0.7, 'ST': 0.7 },
        'WR': { 'WR': 1.0, 'MR': 0.9, 'DR': 0.7, 'ST': 0.7 }
      };

      const compatibility = positionCompatibility[position1];
      return compatibility?.[currentPosition] || 0.5; // 不兼容位置50%适配
    } catch (error) {
      this.logger.warn(`计算位置适配率失败: ${hero.heroId}`, error);
      return 1.0; // 默认100%适配
    }
  }

  /**
   * 🔧 获取当前战斗时间
   * 基于回合索引计算当前战斗时间
   */
  private getCurrentBattleTime(roundIndex: number): number {
    // 简化实现：每回合约30秒
    return roundIndex * 30;
  }

  /**
   * 🔧 初始化单个技能信息
   * 基于old项目: Room.prototype.initSkillTriggerInfo
   */
  private async initSkillTriggerInfo(teamSide: string, heroId: string, skillResId: number): Promise<void> {
    try {
      // 从配置表读取技能信息
      const skillConfig = await this.getSkillConfig(skillResId);
      if (!skillConfig) {
        this.logger.warn(`技能配置不存在: ${skillResId}`);
        return;
      }

      // 基于old项目第2507行：skillObj.skillInfo = this.initSingleSkillInfo(skillConfig, teamSide)
      const skillInfo = {
        continueTime: skillConfig.duration * 60,    // 持续时间 (秒)
        skillType: skillConfig.pattern,             // 生效方式: 0.技能激活 1.被动 2.几率
        radio: skillConfig.pattern === 1 ? 100 : skillConfig.probability, // 触发几率
        startTime: 0,
        endTime: 0,
        calcState: 0, // commonEnum.BATTLE_SKILL_CALC_STATE.Default
        isTriggered: false,
        teamSide: teamSide,
        hasTrigger: false
      };

      // 基于old项目第2510-2528行：初始化Buff信息
      const buffList: BuffInfo[] = [];
      const ABCList = ['A', 'B', 'C', 'D'];
      let skillType = 0; // skillType: 1.非持续 2.持续 3.下一次攻击

      for (const param of ABCList) {
        const buffInfo = this.initSingleBuffInfo(skillConfig, param, teamSide);
        if (buffInfo.opType !== 0) {
          buffList.push(buffInfo);
          let tmpType = 0;
          if (buffInfo.effectType >= 1 && buffInfo.effectType <= 4) {
            tmpType = 1; // 瞬时技能
          } else if (buffInfo.effectType >= 71 && buffInfo.effectType <= 79) {
            tmpType = 3; // 下一次攻击技能
          } else if (buffInfo.effectType >= 11 && buffInfo.effectType <= 63) {
            tmpType = 2; // 持续性技能
          }
          if (skillType < tmpType) {
            skillType = tmpType;
          }
        }
      }

      // 创建技能对象 - 基于old项目第2501行
      const skillRecord: SkillObject = {
        heroId: heroId,
        skillId: skillResId,
        skillLevel: 1, // 默认等级
        skillConfig,
        buffList,
        skillInfo,
        isActive: false,
        remainingDuration: 0
      };

      // 根据技能类型分类 - 基于old项目第2531-2537行
      const teamIndex = teamSide === 'teamA' ? 0 : 1;
      if (skillType === 1) {
        this.instantSkillRecord[teamIndex].push(skillRecord);
        this.logger.debug(`瞬时技能初始化: ${heroId}, 技能ID: ${skillResId}`);
      } else if (skillType === 2) {
        this.durativeSkillRecord[teamIndex].push(skillRecord);
        this.logger.debug(`持续技能初始化: ${heroId}, 技能ID: ${skillResId}`);
      } else {
        this.nextAtkSkillRecord[teamIndex].push(skillRecord);
        this.logger.debug(`下一次攻击技能初始化: ${heroId}, 技能ID: ${skillResId}`);
      }
    } catch (error) {
      this.logger.error(`初始化技能信息失败: ${skillResId}`, error);
    }
  }

  /**
   * 🔧 初始化单个Buff信息
   * 基于old项目: Room.prototype.initSingleBuffInfo
   */
  private initSingleBuffInfo(skillConfig: HeroSkillDefinition, paramABC: string, atkTeamSide: string): BuffInfo {
    // 基于old项目第2562-2625行
    const buffInfo: BuffInfo = {
      opType: skillConfig[`type${paramABC}Opportunity` as keyof HeroSkillDefinition] as number || 0,
      value: skillConfig[`type${paramABC}Value` as keyof HeroSkillDefinition] as number || 0,
      effectType: skillConfig[`type${paramABC}` as keyof HeroSkillDefinition] as number || 0,
      atkTeamSide: atkTeamSide,
      attackMode: 0, // 默认值，后续根据opType设置
      period: 0,     // 默认值，后续根据opType设置
      effectTeamSide: atkTeamSide
    };

    // 设置触发阶段 - 基于old项目第2580-2589行
    if (buffInfo.opType === 2) {
      buffInfo.period = 0;    // 回合开始,几率触发
    } else if ((buffInfo.opType >= 11 && buffInfo.opType <= 15) || buffInfo.opType === 46) {
      buffInfo.period = 3;    // 突破+射门
    } else if ((buffInfo.opType >= 16 && buffInfo.opType <= 20) || (buffInfo.opType >= 41 && buffInfo.opType <= 45)) {
      buffInfo.period = 1;    // 突破
    } else if (buffInfo.opType >= 21 && buffInfo.opType <= 40) {
      buffInfo.period = 2;    // 射门
    }

    // 设置攻击方式 - 基于old项目第2590-2611行
    if (buffInfo.opType === 21 || buffInfo.opType === 31) {
      buffInfo.attackMode = 1; // 头球
    } else if (buffInfo.opType === 11 || buffInfo.opType === 16 || buffInfo.opType === 22 || buffInfo.opType === 32 || buffInfo.opType === 41) {
      buffInfo.attackMode = 2; // 远射
    } else if (buffInfo.opType === 12 || buffInfo.opType === 17 || buffInfo.opType === 23 || buffInfo.opType === 33 || buffInfo.opType === 42) {
      buffInfo.attackMode = 3; // 推射
    } else if (buffInfo.opType === 13 || buffInfo.opType === 18 || buffInfo.opType === 24 || buffInfo.opType === 34 || buffInfo.opType === 43) {
      buffInfo.attackMode = 4; // 抢点
    } else if (buffInfo.opType === 14 || buffInfo.opType === 19 || buffInfo.opType === 25 || buffInfo.opType === 35 || buffInfo.opType === 44) {
      buffInfo.attackMode = 5; // 吊射
    } else if (buffInfo.opType === 15 || buffInfo.opType === 20 || buffInfo.opType === 26 || buffInfo.opType === 36 || buffInfo.opType === 45) {
      buffInfo.attackMode = 6; // 单刀
    } else if (buffInfo.opType === 27 || buffInfo.opType === 37) {
      buffInfo.attackMode = 7; // 任意球
    } else if (buffInfo.opType === 28 || buffInfo.opType === 38) {
      buffInfo.attackMode = 8; // 角球
    } else if (buffInfo.opType === 29 || buffInfo.opType === 39) {
      buffInfo.attackMode = 9; // 点球
    } else if (buffInfo.opType === 30 || buffInfo.opType === 40 || buffInfo.opType === 46) {
      buffInfo.attackMode = 0; // 全模式
    }

    // 设置生效方 - 基于old项目第2612-2621行
    buffInfo.effectTeamSide = buffInfo.atkTeamSide;
    if (buffInfo.opType >= 31 && buffInfo.opType !== 46) {
      if (buffInfo.atkTeamSide === "teamA") {
        buffInfo.effectTeamSide = "teamB";
      } else {
        buffInfo.effectTeamSide = "teamA";
      }
    }

    return buffInfo;
  }

  /**
   * 🔧 检查单个技能是否触发
   * 基于old项目: Room.prototype.checkSingleSkillTrigger
   */
  private checkSingleSkillTrigger(
    skillObj: SkillObject,
    skillInterType: string,
    roundIndex: number,
    heroInfo: { heroId: string; teamSide: string; type: string },
    period: number,
    attackMode: number
  ): { isTrigger: boolean; startTime?: number; endTime?: number } {
    try {
      // 基于old项目第2845-2858行：检查技能类型和触发概率
      const skillInfo = skillObj.skillInfo;

      // 根据生效方式类型: 0: 技能激活, 1: 被动, 2: 几率触发
      if (skillInfo.skillType === 0) {
        return { isTrigger: false };
      }

      // 几率触发检查
      if (skillInfo.skillType === 2 || skillInfo.skillType === 3) {
        const randomNum = Math.floor(Math.random() * 100) + 1;
        if (randomNum > skillInfo.radio) {
          return { isTrigger: false };
        }
      }

      // 根据技能交互类型进行检查 - 基于old项目第2860-2935行
      switch (skillInterType) {
        case 'instant': // 瞬时技能 - 基于old项目第2861-2885行
          // 检查任一Buff是否触发
          let isTrigger = false;
          for (const buffInfo of skillObj.buffList) {
            // 时机检测
            if (period !== buffInfo.period && buffInfo.period !== 3) {
              continue;
            }

            // 攻击方式检测
            if (attackMode !== buffInfo.attackMode && buffInfo.attackMode !== 0) {
              continue;
            }

            // 作用效果检测 - 简化版本
            isTrigger = true;
            break;
          }
          return { isTrigger };

        case 'durative': // 持续性技能 - 基于old项目第2886-2902行
          // 已触发过的持续性技能不再触发
          if (skillInfo.hasTrigger) {
            return { isTrigger: false };
          }

          // 计算触发时间
          const startTime = Date.now();
          const endTime = startTime + skillInfo.continueTime * 1000;

          return {
            isTrigger: true,
            startTime,
            endTime
          };

        case 'nextAttack': // 下一次攻击技能 - 基于old项目第2903-2931行
          // 已触发过的不再触发
          if (skillInfo.hasTrigger) {
            return { isTrigger: false };
          }

          // 计算触发时间
          const nextStartTime = Date.now();
          const nextEndTime = nextStartTime + skillInfo.continueTime * 1000;

          return {
            isTrigger: true,
            startTime: nextStartTime,
            endTime: nextEndTime
          };

        default:
          this.logger.error(`未知技能交互类型: ${skillInterType}`);
          return { isTrigger: false };
      }
    } catch (error) {
      this.logger.error(`检查技能触发失败: ${skillObj.skillId}`, error);
      return { isTrigger: false };
    }
  }

  /**
   * 🔧 计算技能事件时间
   * 基于old项目: Room.prototype.calcSkillEventTime
   */
  private calcSkillEventTime(round: number, continueTime: number, lastEndTime: number): { startTime: number; endTime: number } {
    try {
      // 基于回合时间计算技能的开始和结束时间
      let startTime = round * 60; // 每回合60秒

      // 如果有上一个技能的结束时间，确保不重叠
      if (lastEndTime > 0 && startTime < lastEndTime) {
        startTime = lastEndTime;
      }

      // 计算结束时间
      const endTime = startTime + continueTime;

      // 确保时间在合理范围内（0-5400秒，即90分钟）
      const maxTime = 90 * 60;
      const finalStartTime = Math.max(0, Math.min(startTime, maxTime));
      const finalEndTime = Math.max(finalStartTime, Math.min(endTime, maxTime));

      this.logger.debug(`技能时间计算: 回合=${round}, 持续=${continueTime}秒, 开始=${finalStartTime}, 结束=${finalEndTime}`);

      return {
        startTime: finalStartTime,
        endTime: finalEndTime
      };
    } catch (error) {
      this.logger.error('计算技能事件时间失败', error);
      return { startTime: round * 60, endTime: round * 60 + continueTime };
    }
  }

  /**
   * 🔧 获取技能效果属性名
   * 基于old项目: Room.prototype.getSkillEffectAttrName
   */
  private getSkillEffectAttrName(buffInfo: any): string {
    try {
      const effectType = buffInfo.effectType || buffInfo.type;

      // effectType 11-29: 单项属性加成
      const singleAttributeMap: { [key: number]: string } = {
        11: 'speed',        // 速度
        12: 'shooting',     // 射门
        13: 'passing',      // 传球
        14: 'defending',    // 防守
        15: 'dribbling',    // 盘带
        16: 'physicality',  // 身体
        17: 'goalkeeping',  // 门将
        18: 'attack',       // 攻击力
        19: 'defend',       // 防守力
        20: 'power',        // 力量
        21: 'technique',    // 技术
        22: 'mental',       // 精神
        23: 'leadership',   // 领导力
        24: 'experience',   // 经验
        25: 'potential',    // 潜力
        26: 'form',         // 状态
        27: 'fitness',      // 体能
        28: 'morale',       // 士气
        29: 'confidence'    // 信心
      };

      if (singleAttributeMap[effectType]) {
        return singleAttributeMap[effectType];
      }

      // effectType 50-63: 全属性或位置属性加成
      const specialAttributeMap: { [key: number]: string } = {
        50: 'allAttributes',    // 全属性加成
        51: 'attackAttributes', // 攻击属性加成
        52: 'defendAttributes', // 防守属性加成
        53: 'physicalAttributes', // 身体属性加成
        54: 'mentalAttributes',   // 精神属性加成
        55: 'goalkeepingAttributes', // 门将属性加成
        56: 'forwardAttributes',     // 前锋属性加成
        57: 'midfielderAttributes',  // 中场属性加成
        58: 'defenderAttributes',    // 后卫属性加成
        59: 'wingsAttributes',       // 边路属性加成
        60: 'centerAttributes',      // 中路属性加成
        61: 'leftAttributes',        // 左路属性加成
        62: 'rightAttributes',       // 右路属性加成
        63: 'setpieceAttributes'     // 定位球属性加成
      };

      if (specialAttributeMap[effectType]) {
        return specialAttributeMap[effectType];
      }

      this.logger.warn(`未知的技能效果类型: ${effectType}`);
      return '';
    } catch (error) {
      this.logger.error('获取技能效果属性名失败', error);
      return '';
    }
  }

  /**
   * 🔧 获取技能对象
   * 基于old项目: Room.prototype.getSkillObjBySkillId
   */
  private getSkillObjBySkillId(teamSide: TeamType, interType: number, skillId: number, heroId: string): SkillObject | null {
    try {
      // 根据技能ID和球员ID获取技能对象
      const teamIndex = teamSide === 'teamA' ? 0 : 1;

      // 根据交互类型选择对应的技能记录
      let skillRecords: any[] = [];
      switch (interType) {
        case 1: // 持续性技能
          skillRecords = this.durativeSkillRecord[teamIndex];
          break;
        case 2: // 瞬时性技能
          skillRecords = this.instantSkillRecord[teamIndex];
          break;
        case 3: // 下一次攻击技能
          skillRecords = this.nextAtkSkillRecord[teamIndex];
          break;
        default:
          this.logger.warn(`未知的技能交互类型: ${interType}`);
          return null;
      }

      // 查找匹配的技能对象
      const skillObj = skillRecords.find(skill =>
        skill.skillId === skillId && skill.heroId === heroId
      );

      if (skillObj) {
        this.logger.debug(`找到技能对象: 技能ID=${skillId}, 球员=${heroId}, 类型=${interType}`);
        return skillObj;
      } else {
        this.logger.debug(`未找到技能对象: 技能ID=${skillId}, 球员=${heroId}, 类型=${interType}`);
        return null;
      }
    } catch (error) {
      this.logger.error(`获取技能对象失败: 技能ID=${skillId}, 球员=${heroId}`, error);
      return null;
    }
  }

  /**
   * 获取技能效果类型
   * 基于old项目: Room.prototype.getSkillEffectSuccessPerOrAttr (第3105-3116行)
   *
   * @param buffInfo Buff信息
   * @returns 效果类型: 'SuccessPer' | 'NextAtkPenaltyKick' | 'NextAtkCornerKick' | 'AttrPer' | 'Unknown'
   */
  private getSkillEffectType(buffInfo: BuffInfo): string {
    // 基于old项目第3106-3107行
    if (buffInfo.effectType === 1 || buffInfo.effectType === 2) {
      return 'SuccessPer'; // commonEnum.BATTLE_SKILL_EFFECT_TYPE.SuccessPer
    }
    // 基于old项目第3108-3109行
    else if (buffInfo.effectType >= 71 && buffInfo.effectType <= 79) {
      return 'NextAtkPenaltyKick'; // commonEnum.BATTLE_SKILL_EFFECT_TYPE.NextAtkPenaltyKick
    }
    // 基于old项目第3110行
    else if (buffInfo.effectType === 80) {
      return 'NextAtkCornerKick'; // commonEnum.BATTLE_SKILL_EFFECT_TYPE.NextAtkCornerKick
    }
    // 基于old项目第3111-3112行
    else if (buffInfo.effectType >= 11 && buffInfo.effectType <= 63) {
      return 'AttrPer'; // commonEnum.BATTLE_SKILL_EFFECT_TYPE.AttrPer
    }
    return 'Unknown';
  }

  /**
   * 获取Buff效果值
   * 基于old项目: Room.prototype.getBuffEffectValue (第2833-2839行)
   *
   * @param buffObj Buff信息对象
   * @returns 效果值，effectType为2时返回负值（减益效果）
   */
  private getBuffEffectValue(buffObj: BuffInfo): number {
    let value = buffObj.value || 0;
    // 基于old项目第2835-2837行：effectType为2时取负值
    if (buffObj.effectType === 2) {
      value = 0 - value;
    }
    return value;
  }

  /**
   * 🔧 填充技能效果列表到periodInfo
   * 基于old项目: calcSkillChangePer中的skillEffectList填充逻辑
   */
  fillSkillEffectList(period: number, attackMode: number, roundIndex: number, attackerTeam: BattleTeam): void {
    try {
      // 结构化修复：安全初始化 periodInfo，避免未定义索引错误。
      if (!attackerTeam.roundInfo) {
        (attackerTeam as any).roundInfo = { periodInfo: [] };
      }
      if (!Array.isArray(attackerTeam.roundInfo.periodInfo)) {
        attackerTeam.roundInfo.periodInfo = [] as any[];
      }

      // 初始化（或重置）指定 period 的 skillEffectList
      if (!attackerTeam.roundInfo.periodInfo[period]) {
        attackerTeam.roundInfo.periodInfo[period] = {
          actionId: 0,
          startCommentId: 0,
          resultCommentId: 0,
          percent: 0,
          result: 0,
          skillEffectList: []
        } as any;
      } else {
        attackerTeam.roundInfo.periodInfo[period].skillEffectList = [];
      }

      const skillList = attackerTeam.roundInfo.periodInfo[period].skillEffectList;

      // 遍历三种技能类型（使用“触发记录”数组，而非初始化模板数组）
      const skillTypes = [
        { type: 'durative', record: this.triggeredDurativeSkillRecord },
        { type: 'instant', record: this.triggeredInstantSkillRecord },
        { type: 'nextAttack', record: this.triggeredNextAtkSkillRecord }
      ];

      for (const skillType of skillTypes) {
        // 检查两个队伍
        for (let teamIndex = 0; teamIndex < 2; teamIndex++) {
          const teamSide = teamIndex === 0 ? 'teamA' : 'teamB';
          const records = skillType.record[teamIndex] || [];

          for (const skillRecord of records) {
            // 检查是否是当前阶段和回合的技能 - 基于old项目第3147行
            if (period !== skillRecord.period || roundIndex !== skillRecord.round) {
              this.logger.debug(`技能不匹配当前阶段: 期望period=${period}, round=${roundIndex}, 实际period=${skillRecord.period}, round=${skillRecord.round}`);
              continue;
            }

            // 计算技能效果值
            const effectValue = this.calculateSkillEffectValue(skillRecord);

            if (effectValue !== 0) {
              // 基于old项目第3164-3167行的oneSkillRecord结构
              const oneSkillRecord = {
                skillId: skillRecord.skillId,
                teamType: teamSide,
                addPer: effectValue,
                heroId: skillRecord.heroId  // old项目中是heroUid，当前项目统一使用heroId
              };

              skillList.push(oneSkillRecord);
              this.logger.debug(`填充技能效果: 技能ID=${skillRecord.skillId}, 效果值=${effectValue}, 球员=${skillRecord.heroId}, 阶段=${period}, 回合=${roundIndex}`);
            }
          }
        }
      }

      this.logger.debug(`技能效果列表填充完成: 阶段=${period}, 回合=${roundIndex}, 效果数量=${skillList.length}`);
    } catch (error) {
      this.logger.error('填充技能效果列表失败', error);
    }
  }

  /**
   * 获取技能交互类型
   */
  private getSkillInterType(type: string): number {
    switch (type) {
      case 'durative': return 1;
      case 'instant': return 2;
      case 'nextAttack': return 3;
      default: return 1;
    }
  }

  /**
   * 计算技能效果值
   */
  private calculateSkillEffectValue(skillObj: SkillObject): number {
    try {
      // 基于old项目的技能效果计算逻辑
      let value = 0;

      // 遍历技能的Buff列表，计算总效果值
      for (const buffInfo of skillObj.buffList) {
        // 基于old项目的效果类型判断
        if (buffInfo.effectType >= 1 && buffInfo.effectType <= 4) {
          // 成功率影响类型
          value += buffInfo.value;
        } else if (buffInfo.effectType >= 11 && buffInfo.effectType <= 63) {
          // 属性影响类型
          value += buffInfo.value * 0.1; // 属性影响转换为成功率影响
        } else if (buffInfo.effectType >= 71 && buffInfo.effectType <= 79) {
          // 下一次攻击特殊效果
          value += buffInfo.value;
        }
      }

      return Math.round(value);
    } catch (error) {
      this.logger.warn('计算技能效果值失败', error);
      return 0;
    }
  }

  /**
   * 获取技能记录（用于战报）
   */
  getSkillRecords(): any {
    return {
      durativeSkillRecord: this.durativeSkillRecord,
      instantSkillRecord: this.instantSkillRecord,
      nextAtkSkillRecord: this.nextAtkSkillRecord
    };
  }

  /**
   * 🔧 获取触发的技能记录
   * 用于BattleRecordGenerator生成技能记录
   */
  getTriggeredSkillRecords(): {
    durative: any[][];
    instant: any[][];
    nextAttack: any[][];
  } {
    return {
      durative: this.triggeredDurativeSkillRecord,
      instant: this.triggeredInstantSkillRecord,
      nextAttack: this.triggeredNextAtkSkillRecord
    };
  }

  /**
   * 重置技能系统
   */
  reset(): void {
    // 重置技能记录数组
    this.durativeSkillRecord = [[], []];
    this.instantSkillRecord = [[], []];
    this.nextAtkSkillRecord = [[], []];
    this.triggeredDurativeSkillRecord = [[], []];
    this.triggeredInstantSkillRecord = [[], []];
    this.triggeredNextAtkSkillRecord = [[], []];
  }

  /**
   * 🔧 初始化队伍技能
   * 扫描队伍中所有球员的技能并分类
   */
  private async initTeamSkills(team: any, teamIndex: number): Promise<void> {
    try {
      if (!team.heroes || !Array.isArray(team.heroes)) {
        return;
      }

      for (const hero of team.heroes) {
        await this.initHeroSkills(hero, teamIndex);
      }

      this.logger.debug(`队伍${teamIndex}技能初始化完成，球员数量: ${team.heroes.length}`);
    } catch (error) {
      this.logger.error(`初始化队伍${teamIndex}技能失败`, error);
    }
  }

  /**
   * 🔧 初始化球员技能
   * 基于old项目的球员技能分类逻辑
   */
  private async initHeroSkills(hero: BattleHero, teamIndex: number): Promise<void> {
    try {
      // 🔧 当前项目中，activeSkills是激活的技能ID数组（number[]）
      // 基于BattleDataService.convertHeroToBattleFormat的数据结构
      const heroSkills = hero.activeSkills || [];
      const heroId = hero.heroId;
      const teamSide = teamIndex === 0 ? 'teamA' : 'teamB';

      if (!Array.isArray(heroSkills) || heroSkills.length === 0) {
        this.logger.debug(`球员${heroId}没有激活技能`);
        return;
      }

      // 基于old项目第2443-2450行：遍历球员的技能列表
      for (const skillId of heroSkills) {
        if (typeof skillId === 'number' && skillId > 0) {
          // 基于old项目第2448行：调用initSkillTriggerInfo初始化技能
          await this.initSkillTriggerInfo(teamSide, heroId, skillId);
        }
      }

      this.logger.debug(`球员${heroId}技能初始化完成，激活技能数量: ${heroSkills.length}`);
    } catch (error) {
      this.logger.warn(`初始化球员技能失败: ${hero.heroId}`, error);
    }
  }



  /**
   * 🔧 获取技能配置
   * 基于old项目: dataApi.allData.data["FootballerSkill"][skillResId]
   */
  private async getSkillConfig(skillId: number): Promise<HeroSkillDefinition | null> {
    try {
      // 通过gameConfig获取技能配置
      const skillConfig = this.gameConfig.heroSkill.get(skillId);
      if (!skillConfig) {
        this.logger.warn(`技能配置不存在: ${skillId}`);
        return null;
      }

      return skillConfig;
    } catch (error) {
      this.logger.error(`获取技能配置失败: ${skillId}`, error);
      return null;
    }
  }
}
