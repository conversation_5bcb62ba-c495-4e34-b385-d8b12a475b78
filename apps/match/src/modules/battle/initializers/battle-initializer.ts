/**
 * 战斗初始化器
 * 负责各种战斗模式的数据初始化，从BattleEngine中拆分出来
 * 基于old项目room.js的初始化逻辑
 */

import { Injectable, Logger } from '@nestjs/common';
import { GameConfigFacade } from '@libs/game-config';
import { BattleTeam, BattleHero, TeamType } from '../types/battle-data.types';
import { BattleAttributeCalculator } from '../calculators/battle-attribute-calculator';
import { BattleSkillSystem } from '../systems/battle-skill-system';
import { BattleData } from '../types/battle-data.types';

import { BattleType } from '@libs/game-constants';

/**
 * 🔧 战斗初始化器类
 * 专门负责各种战斗模式的初始化逻辑
 */
@Injectable()
export class BattleInitializer {
  private readonly logger = new Logger(BattleInitializer.name);

  constructor(
    private readonly gameConfig: GameConfigFacade,
    private readonly attributeCalculator: BattleAttributeCalculator,
    private readonly skillSystem: BattleSkillSystem
  ) {}

  /**
   * 🔧 主初始化方法 - 从BattleTeam数组构建BattleData
   * 重构版本：直接从BattleTeam构建，消除BattleRoom中转
   *
   * 📋 完整初始化流程：
   *
   * 【第一阶段：数据结构构建】
   * 1. 从BattleTeam数组构建BattleData基础结构
   * 2. 设置战斗类型和基础配置
   * 3. 初始化战斗时间和回合计数器
   *
   * 【第二阶段：属性计算与初始化】
   * 4. 计算队伍总攻击力、总防守力等基础属性
   * 5. 初始化队伍士气值和士气槽位
   * 6. 计算球员个体属性和位置适配率
   *
   * 【第三阶段：技能系统初始化】
   * 7. 初始化所有球员的技能信息(initAllSkillInfo)
   * 8. 设置技能触发条件和冷却时间
   * 9. 初始化技能效果记录数组
   *
   * 【第四阶段：回合数据初始化】
   * 10. 初始化回合索引和回合状态
   * 11. 设置战斗记录和统计数据结构
   * 12. 初始化球员评分映射和动作记录
   *
   * @param battleTeams 战斗队伍数组 - [teamA, teamB]
   * @param battleType 战斗类型
   * @param battleId 战斗ID（可选）
   * @returns Promise<BattleData> 初始化完成的战斗数据对象
   */
  async initializeBattleData(
    battleTeams: BattleTeam[],
    battleType: string,
    battleId?: string
  ): Promise<BattleData> {
    this.logger.log(`初始化战斗数据 - 类型: ${battleType}, 队伍数: ${battleTeams.length}`);

    const [teamA, teamB] = battleTeams;
    this.logger.debug(`队伍A球员数量: ${teamA?.heroes?.length || 0}, 队伍B球员数量: ${teamB?.heroes?.length || 0}`);

    // 第一步：构建BattleData基础结构
    const battleData = this.buildBattleDataStructure(battleTeams, battleType, battleId);

    // 第二步：计算综合战力和士气
    await this.attributeCalculator.calculateTotalPower(battleData.teamA);
    await this.attributeCalculator.calculateTotalPower(battleData.teamB);
    this.attributeCalculator.calcMoraleAttr(battleData.teamA, battleData.teamB);

    // 第三步：初始化技能系统
    await this.skillSystem.initSkillSystem(battleData.teamA, battleData.teamB);

    // 第四步：初始化回合数据
    this.initRoundResultData(battleData);

    this.logger.log(`战斗数据初始化完成 - 队伍A: ${battleData.teamA.heroes?.length || 0}人, 队伍B: ${battleData.teamB.heroes?.length || 0}人`);

    return battleData;
  }

  /**
   * 构建BattleData基础结构
   * 从BattleTeam数组创建BattleData对象
   */
  private buildBattleDataStructure(
    battleTeams: BattleTeam[],
    battleType: string,
    battleId?: string
  ): BattleData {
    const [teamA, teamB] = battleTeams;

    // 确保teamSide正确设置
    teamA.teamSide = TeamType.TeamA;
    teamB.teamSide = TeamType.TeamB;

    return {
      battleId: battleId || `battle_${Date.now()}`,
      battleType: battleType as BattleType,
      battleTime: 0,
      roundIndex: 0,
      maxBattleTime: 5400, // 90分钟
      roundAttacker: TeamType.TeamA,

      teamA,
      teamB,

      battleRecord: {
        battleRoundInfo: [],
        totalTime: 0,
        totalRounds: 0
      }
    };
  }

  /**
   * 🔧 PVE战斗初始化
   * 基于old项目: Room.prototype.pveInit
   */
  async initPveBattle(battleData: BattleData): Promise<void> {
    try {
      // 初始化PVE战斗的基础数据
      battleData.battleType = battleData.battleType;
      battleData.battleTime = 0;
      battleData.roundIndex = 0;
      battleData.maxBattleTime = 5400; // 90分钟

      // 初始化队伍数据
      await this.initPveBattleData(battleData);

      // 初始化回合数据
      this.initRoundResultData(battleData);

      this.logger.debug('PVE战斗初始化完成');
    } catch (error) {
      this.logger.error('PVE战斗初始化失败', error);
      throw error;
    }
  }

  /**
   * 🔧 PVP战斗初始化
   * 基于old项目: Room.prototype.pvpInit
   */
  async initPvpBattle(battleData: BattleData, pvpConfig?: any): Promise<void> {
    try {
      // 初始化PVP战斗的基础数据
      battleData.battleType = battleData.battleType;
      battleData.battleTime = 0;
      battleData.roundIndex = 0;
      battleData.maxBattleTime = 5400; // 90分钟

      // 🔧 补充：PVP特殊场景处理（基于old项目pvpInit逻辑）
      if (pvpConfig) {
        // 特殊阵容处理
        if (pvpConfig.homeTfId) {
          battleData.teamA.fixLineupId = pvpConfig.homeTfId;
          this.logger.debug(`设置主队特殊阵容: ${pvpConfig.homeTfId}`);
        }
        if (pvpConfig.awayTfId) {
          battleData.teamB.fixLineupId = pvpConfig.awayTfId;
          this.logger.debug(`设置客队特殊阵容: ${pvpConfig.awayTfId}`);
        }

        // 商业赛奖励信息
        if (pvpConfig.businessRewardInfo) {
          battleData.teamA.businessRewardInfo = pvpConfig.businessRewardInfo;
          battleData.teamB.businessRewardInfo = pvpConfig.businessRewardInfo;
          this.logger.debug('设置商业赛奖励信息');
        }

        // 🔧 个人球场争夺战特殊处理 - 基于old项目room.js第101-104行逻辑
        // old项目: this.teamA.fixFormationUid = msg.homeTeamUid; this.teamB.fixFormationUid = msg.awayTeamUid;
        if (battleData.battleType === BattleType.PVP_GROUND_MATCH) {
          battleData.teamA.fixLineupId = pvpConfig.homeTeamUid;
          battleData.teamB.fixLineupId = pvpConfig.awayTeamUid;
          this.logger.debug(`个人球场争夺战特殊阵容设置: 主队=${pvpConfig.homeTeamUid}, 客队=${pvpConfig.awayTeamUid}`);
        }
      }

      // 初始化双方队伍数据
      await this.initPvpBattleData(battleData);

      // 初始化回合数据
      this.initRoundResultData(battleData);

      this.logger.debug('PVP战斗初始化完成');
    } catch (error) {
      this.logger.error('PVP战斗初始化失败', error);
      throw error;
    }
  }

  /**
   * 🔧 信仰之战特殊数据初始化
   * 基于old项目: Room.prototype.initWarOfFaithBattleData
   *
   * ⚠️ 注意：此方法仅处理信仰之战的特殊逻辑，不包含基础PVP初始化
   * 基础PVP初始化应在BattleEngine中先执行
   */
  async initWarOfFaithBattle(battleData: BattleData, faithLevel: number = 1): Promise<void> {
    try {
      // 设置信仰之战特殊属性
      battleData.battleType = BattleType.PVP_WAR_OF_FAITH;
      battleData.faithLevel = faithLevel;
      battleData.isSpecialMode = true;

      // 🔧 添加信仰之战特殊规则设置（基于新架构需求）
      battleData.specialRules = {
        faithBonus: true,
        enhancedSkills: true,
        extendedTime: true,
        maxBattleTime: 7200 // 120分钟，比普通战斗更长
      };

      // 🔧 仅处理信仰之战特殊逻辑：初始化双方队伍的信仰加成
      await this.initFaithBonus(battleData.teamA, faithLevel);
      await this.initFaithBonus(battleData.teamB, faithLevel);

      // ❌ 移除重复的基础初始化调用
      // await this.initPvpBattleData(battleData); // 已在BattleEngine中执行

      this.logger.debug('信仰之战特殊数据初始化完成');
    } catch (error) {
      this.logger.error('信仰之战特殊初始化失败', error);
      throw error;
    }
  }

  /**
   * 🔧 游客模式特殊数据初始化
   * 基于old项目: Room.prototype.initNoneAccountBattleData
   *
   * ⚠️ 注意：游客模式是独立的特殊战斗类型，不依赖基础PVE/PVP初始化
   * 此方法包含完整的游客模式初始化逻辑
   */
  async initGuestModeBattle(battleData: BattleData): Promise<void> {
    try {
      // 设置游客模式标识
      battleData.isGuestMode = true;
      battleData.battleType = BattleType.GUEST;

      // 🔧 游客模式特殊逻辑：创建游客队伍
      await this.createGuestTeam(battleData.teamA);
      await this.createGuestTeam(battleData.teamB);

      // 🔧 游客模式需要基础战斗数据初始化（因为是独立的特殊类型）
      await this.initPvpBattleData(battleData);

      this.logger.debug('游客模式特殊数据初始化完成');
    } catch (error) {
      this.logger.error('游客模式特殊初始化失败', error);
      throw error;
    }
  }

  /**
   * 🔧 测试模式初始化
   * 基于old项目: Room.prototype.initByTestConfig
   */
  async initTestBattle(testConfig: any): Promise<BattleData> {
    try {
      // 创建测试战斗数据
      const battleData: Partial<BattleData> = {
        battleId: `test_${Date.now()}`,
        battleType: BattleType.TEST,
        battleTime: 0,
        roundIndex: 0,
        maxBattleTime: 5400,
        battleRecord: {
          battleRoundInfo: [],
          totalTime: 0,
          totalRounds: 0
        }
      };

      // 初始化测试队伍数据
      await this.initTestTeamInfo(battleData.teamA);
      await this.initTestTeamInfo(battleData.teamB);

      this.logger.debug('测试配置初始化完成');
      return battleData as BattleData;
    } catch (error) {
      this.logger.error('测试模式初始化失败', error);
      throw error;
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 初始化回合结果数据
   * 基于old项目: Room.prototype.initRoundResultData
   * 🔧 公共接口 - 供BattleEngine调用
   */
  public initRoundResultData(battleData: BattleData): void {
    try {
      // 初始化战斗记录的回合信息数组
      if (!battleData.battleRecord) {
        battleData.battleRecord = {
          battleRoundInfo: [],
          totalTime: 0,
          totalRounds: 0
        };
      }

      // 初始化技能系统（如果需要）
      this.initAllSkillInfo(battleData);

      this.logger.debug('回合结果数据初始化完成');
    } catch (error) {
      this.logger.error('回合结果数据初始化失败', error);
    }
  }

  /**
   * 🔧 初始化所有技能信息
   * 基于old项目和BattleEngine的技能初始化逻辑
   * 确保基础的技能数据结构存在，真正的技能初始化由skillSystem.initSkillSystem完成
   */
  private initAllSkillInfo(battleData: BattleData): void {
    try {
      // 🔧 为A队和B队初始化技能状态 - 与BattleEngine保持一致
      if (battleData.teamA) {
        battleData.teamA.skills = battleData.teamA.skills || {};
        battleData.teamA.skillEffects = battleData.teamA.skillEffects || [];
      }

      if (battleData.teamB) {
        battleData.teamB.skills = battleData.teamB.skills || {};
        battleData.teamB.skillEffects = battleData.teamB.skillEffects || [];
      }

      this.logger.debug('技能信息初始化完成');
    } catch (error) {
      this.logger.error('技能信息初始化失败', error);
    }
  }

  /**
   * 初始化PVE战斗数据
   */
  private async initPveBattleData(battleData: BattleData): Promise<void> {
    try {
      // 初始化玩家队伍（A队）
      if (battleData.teamA) {
        await this.initPlayerTeamData(battleData.teamA);
      }

      // 初始化AI队伍（B队）
      if (battleData.teamB) {
        await this.initEnemyTeamData(battleData.teamB, battleData.pveConfig);
      }

      this.logger.debug('PVE战斗数据初始化完成');
    } catch (error) {
      this.logger.error('PVE战斗数据初始化失败', error);
      throw error;
    }
  }

  /**
   * 初始化PVP战斗数据
   */
  private async initPvpBattleData(battleData: BattleData): Promise<void> {
    try {
      // 初始化双方玩家队伍
      if (battleData.teamA) {
        await this.initPlayerTeamData(battleData.teamA);
      }

      if (battleData.teamB) {
        await this.initPlayerTeamData(battleData.teamB);
      }

      this.logger.debug('PVP战斗数据初始化完成');
    } catch (error) {
      this.logger.error('PVP战斗数据初始化失败', error);
      throw error;
    }
  }

  /**
   * 🔧 初始化玩家队伍数据
   * 基于old项目和BattleEngine的完整实现
   * 辅助方法：初始化玩家队伍的基础属性和状态
   * 🔧 公共接口 - 供BattleEngine调用
   *
   * @param team 队伍数据 - 包含球员和配置信息
   */
  public async initPlayerTeamData(team: BattleTeam): Promise<void> {
    try {
      // 🔧 初始化队伍基础属性 - 与BattleEngine保持一致
      team.score = 0;
      team.attr = team.attr || {
        morale: 500,
        moraleSlot: 0,
        moraleAcceleration: 1
      };
      team.attr.morale = team.attr.morale || 500;
      team.attr.moraleSlot = 0;
      // 基于old项目：moraleAcceleration = morale
      team.attr.moraleAcceleration = team.attr.morale;

      // 🔧 初始化统计数据（统一使用新架构命名）
      team.statistic = team.statistic || {
        shots: 0,
        shotsOnTarget: 0,
        possession: 50,
        passes: 0,
        fouls: 0,
        ballerScoreMap: new Map(),
        breaks: 0,
        successfulBreaks: 0,
        setPlays: 0,
        goalEventMap: new Map()
      };

      // 🔧 初始化回合信息
      team.roundInfo = {};

      // 🔧 初始化技能相关 - 与BattleEngine保持一致
      team.skills = team.skills || {};
      team.skillEffects = team.skillEffects || [];

      // 🔧 初始化球员评分 - 基于old项目逻辑
      this.initializePlayerRatings(team);

      this.logger.debug(`角色队伍数据初始化完成`);
    } catch (error) {
      this.logger.error('玩家队伍数据初始化失败', error);
      throw error;
    }
  }

  /**
   * 🔧 初始化球员评分
   * 基于old项目room.js的initPlayerByBattleData逻辑
   */
  private initializePlayerRatings(team: BattleTeam): void {
    try {
      if (!team.heroes || team.heroes.length === 0) return;

      // 基于old项目：初始化球员基础评分
      const BattleBaseScore = {
        base: 6.0        // 基础评分
      };

      // 确保评分映射存在
      if (!team.statistic.ballerScoreMap) {
        team.statistic.ballerScoreMap = new Map();
      }

      // 为每个球员设置基础评分
      team.heroes.forEach(hero => {
        team.statistic.ballerScoreMap.set(hero.heroId, BattleBaseScore.base);
      });

      this.logger.debug(`队伍${team.teamSide}球员评分初始化完成，共${team.heroes.length}名球员`);
    } catch (error) {
      this.logger.error(`初始化球员评分失败: ${team.teamSide}`, error);
    }
  }

  /**
   * 🔧 初始化敌人队伍数据
   * 基于old项目和BattleEngine的完整实现
   * 辅助方法：基于PVE配置初始化AI队伍
   *
   * @param team 队伍数据 - AI队伍信息
   * @param pveConfig PVE配置 - 包含AI队伍的特殊设置
   */
  private async initEnemyTeamData(team: BattleTeam, pveConfig: any): Promise<void> {
    try {
      // 🔧 基础属性初始化
      await this.initPlayerTeamData(team);

      // 🔧 根据PVE配置设置特殊属性 - 与BattleEngine保持一致
      if (pveConfig) {
        team.attr.attackTacticID = pveConfig.attackTactic || 101;
        team.attr.defendTacticID = pveConfig.defendTactic || 202;
        team.teamResId = pveConfig.teamId || 1;
        team.isEnemy = true;
        team.pveLevel = pveConfig.level || 1;
        team.difficultyBonus = pveConfig.difficultyBonus || 1.0;
      }

      this.logger.debug('敌人队伍数据初始化完成');
    } catch (error) {
      this.logger.error('敌人队伍数据初始化失败', error);
      throw error;
    }
  }

  /**
   * 🔧 初始化信仰加成
   * 基于old项目和BattleEngine的完整实现
   * 辅助方法：为队伍添加信仰之战的特殊加成
   *
   * @param team 队伍数据 - 需要添加信仰加成的队伍
   * @param faithLevel 信仰等级 - 决定加成幅度
   */
  private async initFaithBonus(team: BattleTeam, faithLevel: number): Promise<void> {
    try {
      // 基于信仰等级计算加成
      const faithBonus = {
        attackBonus: faithLevel * 0.1, // 每级10%攻击加成
        defendBonus: faithLevel * 0.1, // 每级10%防守加成
        moraleBonus: faithLevel * 50,  // 每级50点士气加成
        skillBonus: faithLevel * 0.05  // 每级5%技能效果加成
      };

      // 🔧 应用加成到队伍属性 - 与BattleEngine保持一致
      team.totalAttack = Math.floor((team.totalAttack || 0) * (1 + faithBonus.attackBonus));
      team.totalDefend = Math.floor((team.totalDefend || 0) * (1 + faithBonus.defendBonus));

      if (!team.attr) team.attr = {
        morale: 500,
        moraleSlot: 0,
        moraleAcceleration: 1
      };
      team.attr.morale = (team.attr.morale || 500) + faithBonus.moraleBonus;
      team.attr.faithLevel = faithLevel;
      team.attr.faithBonus = faithBonus;

      // 🔧 为每个英雄应用信仰加成
      if (team.heroes && Array.isArray(team.heroes)) {
        team.heroes.forEach((hero: any) => {
          if (hero.attack) {
            hero.attack = Math.floor(hero.attack * (1 + faithBonus.attackBonus));
          }
          if (hero.defend) {
            hero.defend = Math.floor(hero.defend * (1 + faithBonus.defendBonus));
          }
        });
      }

      this.logger.debug(`信仰加成初始化完成: 等级${faithLevel}, 攻击加成${faithBonus.attackBonus * 100}%, 防守加成${faithBonus.defendBonus * 100}%`);
    } catch (error) {
      this.logger.error('信仰加成初始化失败', error);
      throw error;
    }
  }

  /**
   * 创建游客队伍
   *
   * @param team 队伍数据 - 游客队伍信息
   */
  private async createGuestTeam(team: BattleTeam): Promise<void> {
    try {
      team.characterId = `guest_${Date.now()}`;
      team.isGuest = true;

      // 设置默认属性
      await this.initPlayerTeamData(team);

      this.logger.debug('游客队伍创建完成');
    } catch (error) {
      this.logger.error('游客队伍创建失败', error);
      throw error;
    }
  }

  /**
   * 初始化测试队伍信息
   *
   * @param team 队伍数据 - 测试队伍信息
   */
  private async initTestTeamInfo(team: BattleTeam): Promise<void> {
    try {
      // 设置默认测试数据
      team.characterId = team.characterId || 'test_character';
      team.level = 1;
      team.isTest = true;

      // 初始化基础数据
      await this.initPlayerTeamData(team);

      this.logger.debug('测试队伍信息初始化完成');
    } catch (error) {
      this.logger.error('测试队伍信息初始化失败', error);
      throw error;
    }
  }
}
