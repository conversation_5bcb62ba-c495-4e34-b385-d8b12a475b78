/**
 * 战斗系统数据类型定义
 * 基于old项目room.js的数据结构，提供完整的TypeScript类型支持
 */

// 导入统一的战斗类型定义
import { BattleType } from '@libs/game-constants';

// 队伍类型 - 使用schema中的枚举定义
import { TeamType } from '../../../common/schemas/battle.schema';

// 重新导出TeamType以供其他模块使用
export { TeamType };

// 攻击模式枚举
export enum AttackMode {
  Header = 1,      // 头球
  LongShot = 2,    // 远射
  Push = 3,        // 推射
  Scramble = 4,    // 抢点
  Lob = 5,         // 吊射
  OneOnOne = 6,    // 单刀
  FreeKick = 7,    // 任意球
  Corner = 8,      // 角球
  Penalty = 9      // 点球
}

// 使用统一的HeroPosition枚举，移除重复的PlayerPosition
import { HeroPosition } from '@libs/game-constants';

// 导入Schema中已定义的回放相关类型
import { PeriodInfo, HeroActionInfo } from '../../../common/schemas/battle.schema';

// 战斗技能结构 - 精简的技能信息
export interface BattleSkill {
  skillId: number;          // 技能ID
  level: number;            // 技能等级
}

// 英雄数据接口 - 为battle-engine优化的完整数据结构
export interface BattleHero {
  // 🔧 基础信息
  heroId: string;                   // 球员唯一标识
  name?: string;                    // 球员姓名
  resId?: number;                   // 球员资源ID
  level: number;                    // 球员等级
  position: HeroPosition;           // 球员位置
  mainPosition?: HeroPosition;      // 主位置（用于位置适配度计算）

  // 🔧 养成相关字段 - 影响属性计算的关键字段
  breakLevel: number;               // 突破等级（影响属性计算）
  starLevel: number;                // 星级（影响属性计算）
  evolutionStage: number;           // 进化阶段（影响属性计算）

  // 🔧 HeroAttributes中的20个属性 - 基于hero.schema.ts，提供base和cur值
  attributes: {
    // 基础身体属性
    speed: { base: number; cur: number };              // 速度
    jumping: { base: number; cur: number };            // 弹跳
    strength: { base: number; cur: number };           // 力量
    stamina: { base: number; cur: number };            // 耐力
    explosiveForce: { base: number; cur: number };     // 爆发力

    // 技术属性
    finishing: { base: number; cur: number };          // 射门
    dribbling: { base: number; cur: number };          // 盘带
    passing: { base: number; cur: number };            // 传球
    longPassing: { base: number; cur: number };        // 长传
    longShots: { base: number; cur: number };          // 远射
    heading: { base: number; cur: number };            // 头球
    volleys: { base: number; cur: number };            // 凌空

    // 防守属性
    standingTackle: { base: number; cur: number };     // 站立铲球
    slidingTackle: { base: number; cur: number };      // 滑铲

    // 特殊技术属性
    penalties: { base: number; cur: number };          // 点球
    cornerKick: { base: number; cur: number };         // 角球
    freeKick: { base: number; cur: number };           // 任意球

    // 门将属性
    attack: { base: number; cur: number };             // 出击
    save: { base: number; cur: number };               // 扑救

    // 特殊属性
    resistanceDamage: { base: number; cur: number };   // 抗伤性
  };

  // 🔧 战斗核心属性 - battle-engine实际使用的计算后综合值
  attack: number;                   // 攻击力综合值（BattleAttributeCalculator中使用）
  defend: number;                   // 防守力综合值（BattleAttributeCalculator中使用）
  speed: number;                    // 速度综合值（基于attributes.speed.cur计算）
  power: number;                    // 力量综合值（基于attributes.strength.cur计算）
  technique: number;                // 技术综合值（基于dribbling, passing等计算）

  // 🔧 技能系统 - 精简的技能信息
  skills: BattleSkill[];            // 技能列表
  activeSkills: number[];           // 激活的技能ID列表

  // 🔧 战斗状态
  currentStamina: number;           // 当前体力（区别于属性中的stamina）
  morale: number;                   // 士气
  fatigue: number;                  // 疲劳
  isEnemy: boolean;                 // 是否敌方
  rating: number;                   // 评分
}

// 队伍属性接口
export interface TeamAttributes {
  morale: number;                    // 士气值
  moraleSlot: number;               // 士气槽
  moraleAcceleration: number;       // 士气加速度
  attackTacticID?: number;          // 攻击战术ID
  defendTacticID?: number;          // 防守战术ID
  faithLevel?: number;              // 信仰等级
  faithBonus?: {                    // 信仰加成
    attackBonus: number;
    defendBonus: number;
    moraleBonus: number;
    skillBonus: number;
  };
}

// 队伍统计数据接口
export interface TeamStatistics {
  shots: number;                   // 射门次数
  shotsOnTarget: number;           // 射正次数
  possession: number;              // 控球率
  passes: number;                  // 传球次数
  fouls: number;                   // 犯规次数
  ballerScoreMap: Map<string, number>; // 球员评分映射

  // 🔧 补充：基于old项目battleTeam.js的真实属性（统一使用新架构命名）
  breaks?: number;                 // 突破次数（对应old项目breakNum）
  successfulBreaks?: number;       // 突破成功次数（对应old项目breakSucNum）
  setPlays?: number;               // 定位球次数（对应old项目placeKickNum）
  goalEventMap?: Map<string, any>; // 进球事件映射
}



// 运行时阶段信息接口（battle-engine使用）
export interface RuntimePeriodInfo {
  actionId: number;                         // 动作ID（当前项目命名）
  startCommentId: number;                   // 开始评论ID（当前项目命名）
  resultCommentId: number;                  // 结果评论ID（当前项目命名）
  percent: number;                          // 成功率
  result: number;                           // 结果
  skillEffectList?: any[];                  // 技能效果列表
}

// 运行时回合信息接口（battle-engine使用）
export interface RoundInfo {
  attackerType?: string;                    // 攻击方标识
  A1Info?: HeroActionInfo;                  // 主攻球员信息（统一使用Info命名）
  A2Info?: HeroActionInfo;                  // 协攻球员信息（统一使用Info命名）
  BInfo?: HeroActionInfo;                   // 防守球员信息（统一使用Info命名）
  GKInfo?: HeroActionInfo;                  // 门将信息（统一使用Info命名）
  attackMode?: AttackMode;                  // 攻击模式
  periodInfo?: RuntimePeriodInfo[];         // 3个阶段的临时计算数据
}

// 注意：PeriodInfo, PlayerInfo 等回放相关接口已移至 battle.schema.ts
// 此文件专注于battle-engine运行时使用的数据类型

// 技能效果接口
export interface SkillEffect {
  skillId: number;
  effectType: string;
  value: number;
  duration: number;
  targetType: 'self' | 'team' | 'enemy';
}

// 评论信息接口
export interface CommentInfo {
  commentID: number;
  actionID: number;
  period: number;
  result: boolean;
  type: string;
  text: string;
  players?: Array<{
    role: string;
    heroUid: string;
    name: string;
  }>;
  timestamp: number;
}

// 战斗回合信息接口 - 基于old项目room.js的battleRoundInfo结构
export interface BattleRoundInfo {
  eventTime: number;                // 事件时间
  moraleA: number;                  // A队士气
  moraleB: number;                  // B队士气
  attackerType: TeamType;           // 攻击方
  attackMode: AttackMode;           // 攻击模式
  periodInfo: PeriodInfo[];         // 阶段信息列表
  scoreA: number;                   // A队比分
  scoreB: number;                   // B队比分

  // 🔧 基于old项目的完整字段
  moraleSlotA?: number;             // A队士气槽
  moraleSlotB?: number;             // B队士气槽

  // 新架构字段（保持兼容）
  comments?: CommentInfo[];         // 评论列表
}

// 战斗记录接口 - 使用schema中的BattleRound
import { BattleRound } from '../../../common/schemas/battle.schema';

export interface BattleRecord {
  battleRoundInfo: BattleRound[];      // 回合信息列表 - 使用schema类型
  totalTime: number;                   // 总时间
  totalRounds: number;                 // 总回合数
}

// 队伍数据接口 - 基于characterId的新架构设计
export interface BattleTeam {
  teamSide: TeamType;               // 队伍方
  characterId: string;              // 角色ID（新架构核心标识）
  teamName: string;                 // 队伍名称（统一命名）
  level: number;                    // 等级
  teamResId?: number;               // 队伍资源ID

  // 阵型和战术
  formationId: number;              // 阵型ID
  tactic: number;                   // 战术ID
  fixLineupId?: string;             // 🔧 补充：特殊阵容ID（PVP特殊场景）
  lineupId?: string;                // 🔧 补充：当前阵容ID（对应old项目的formationUid）

  // 基础属性
  totalAttack: number;              // 总攻击力
  totalDefend: number;              // 总防守力
  score: number;                    // 比分

  // 🔧 补充：商业赛奖励信息
  businessRewardInfo?: {
    totalCash?: number;             // 总现金
    winCash?: number;               // 胜利现金
    loseCash?: number;              // 失败现金
    drawCash?: number;              // 平局现金
    matchWinRatio?: number;         // 胜利比例
    matchLoseRatio?: number;        // 失败比例
    matchDrawRatio?: number;        // 平局比例
    enemyFansCount?: number;        // 敌方球迷数量
    myFansCount?: number;           // 我方球迷数量
    FansQ?: number;                 // 球迷系数（基于old项目）
  };

  // 详细属性
  attr: TeamAttributes;             // 队伍属性
  heroes: BattleHero[];             // 英雄列表
  statistic: TeamStatistics;        // 统计数据
  roundInfo: RoundInfo;             // 回合信息
  skills?: { [key: string]: any };  // 技能状态
  skillEffects?: SkillEffect[];     // 技能效果列表

  // 🔧 补充：基于old项目battleTeam.js的真实属性
  isEnemy?: boolean;                // 是否为敌方队伍（PVE模式）
  pveLevel?: number;                // PVE等级
  difficultyBonus?: number;         // 难度加成
  takeCopyRewardProcess?: number;   // 副本奖励进度（联赛副本专用）
  leagueId?: number;                // 联赛ID（联赛副本专用）
  isGuest?: boolean;                // 是否为游客队伍
  isTest?: boolean;                 // 是否为测试队伍

  // 🔧 补充：定位球球员信息（从阵容数据传递）
  freeKickHero?: string;            // 任意球球员UID
  penaltiesHero?: string;           // 点球球员UID
  cornerKickHero?: string;          // 角球球员UID
  captainHeroId?: string;           // 队长球员UID
  viceCaptainHeroId?: string;       // 副队长球员UID

  // 🔧 补充：信仰之战相关属性（用于getHeroBattleAttr）
  inspireNum?: number;              // 鼓舞次数（信仰之战专用）

}

// 主战斗数据接口
export interface BattleData {
  // 基础信息
  battleId?: string;                // 战斗ID
  battleType: BattleType;           // 战斗类型
  battleTime: number;               // 战斗时间（秒）
  roundIndex: number;               // 当前回合索引
  maxBattleTime: number;            // 最大战斗时间
  roundAttacker?: TeamType;         // 当前回合攻击方
  
  // 队伍数据
  teamA: BattleTeam;                // A队数据
  teamB: BattleTeam;                // B队数据
  
  // 战斗记录
  battleRecord: BattleRecord;       // 战斗记录
  
  // 特殊模式标识
  isSpecialMode?: boolean;          // 是否特殊模式
  isGuestMode?: boolean;            // 是否游客模式
  faithLevel?: number;              // 信仰等级
  
  // 配置数据
  pveConfig?: any;                  // PVE配置
  firstBattleData?: any;            // 首场战斗数据
  
  // 特殊规则
  specialRules?: {
    faithBonus?: boolean;
    enhancedSkills?: boolean;
    extendedTime?: boolean;
    maxBattleTime?: number;
  };
  
  // 奖励信息
  rewards?: {
    winner?: any;
    loser?: any;
    experience?: number;
    coins?: number;
    items?: any[];
  };
}

// 战斗结果接口
export interface BattleResult {
  battleId: string;
  battleType: BattleType;
  winner: TeamType | 'draw';
  finalScore: {
    teamA: number;
    teamB: number;
  };
  battleRecord: BattleRecord;
  statistics: {
    totalTime: number;
    totalRounds: number;
    teamA: TeamStatistics;
    teamB: TeamStatistics;
  };
  skillRecords?: any[]; // 技能记录数组 [teamA, teamB]
  playerRatings?: {     // 球员最终评分
    teamA: Map<string, number>; // heroId -> rating
    teamB: Map<string, number>; // heroId -> rating
  };
  rewards?: any;
  timestamp: number;
}

// 决胜局结果接口
export interface TiebreakerResult {
  winner: TeamType;
  needTiebreaker: boolean;
  finalScoreA: number;
  finalScoreB: number;
  powerA?: number;
  powerB?: number;
}

// 事件计算结果接口
export interface EventCalculationResult {
  nextEventTime: number;
  attacker: TeamType;
}

// 奖励配置接口
export interface RewardConfig {
  baseExperience?: number;
  winBonus?: number;
  baseCoin?: number;
  winCoinBonus?: number;
  itemDropRate?: number;
  baseReputation?: number;
  winReputationBonus?: number;
}

// 奖励结果接口
export interface RewardResult {
  experience: number;
  coins: number;
  items: Array<{
    itemId: number;
    name: string;
    quantity: number;
    rarity: string;
  }>;
  reputation: number;
}

// 回合数据接口
export interface RoundData {
  roundIndex: number;
  attackerType: TeamType;
  attackMode: AttackMode;
  startPeriod: PeriodResult;
  middlePeriod: PeriodResult | null;
  endPeriod: PeriodResult | null;
  isGoal: boolean;
  battleTime: number;
  comments?: CommentInfo[];
}

// 阶段结果接口
export interface PeriodResult {
  success: boolean;
  rate: number;
  actionID?: number;
  A1Info?: HeroActionInfo;
  A2Info?: HeroActionInfo;
  BInfo?: HeroActionInfo;
  GKInfo?: HeroActionInfo;
  skillEffects?: SkillEffect[];
}

// 测试配置接口
export interface TestConfig {
  teamA: TestTeamConfig;
  teamB: TestTeamConfig;
  battleType?: BattleType;
  maxBattleTime?: number;
}

// 测试队伍配置接口
export interface TestTeamConfig {
  playerId?: string;
  playerName?: string;
  formationId?: number;
  tactic?: number;
  heroes?: BattleHero[];
  level?: number;
}

// PVE配置接口
export interface PveConfig {
  level: number;
  difficultyBonus: number;
  formation: number;
  offensiveID: number;
  heroes: Array<{
    heroId: number;
    level: number;
    position: HeroPosition;
  }>;
}

// 英雄查找结果接口
export interface HeroSearchResult {
  hero: BattleHero | null;
  found: boolean;
}

// 攻击模式配置接口
export interface AttackModeConfig {
  id: AttackMode;
  weight: number;
  name: string;
}
