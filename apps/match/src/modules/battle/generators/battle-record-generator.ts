import { Logger } from '@nestjs/common';
import { GameConfigFacade } from '@libs/game-config';
import { BattleRewardCalculator } from '../calculators/battle-reward-calculator';
import { BattleTeam, AttackMode, PeriodResult, BattleData } from '../types/battle-data.types';
import { BattleResult, BattleRound, PeriodInfo, HeroActionInfo } from '../../../common/schemas/battle.schema';
import { BattleType } from '@libs/game-constants';

/**
 * 战报生成器
 * 基于old项目room.js的战报生成逻辑
 * 
 * 职责：
 * - 战报数据结构生成
 * - 客户端协议转换
 * - 三阶段战报记录
 * - 战斗结果格式化
 */
export class BattleRecordGenerator {
  private readonly logger = new Logger(BattleRecordGenerator.name);

  constructor(
    private readonly gameConfig: GameConfigFacade,
    private readonly battleRewardCalculator: BattleRewardCalculator
  ) {}

  /**
   * 🔧 记录发起阶段数据
   * 基于old项目: Room.prototype.recordStartPeriod
   *
   * 📋 完整记录流程：
   *
   * 【第一阶段：基础信息记录】
   * 1. 获取当前回合的战报记录对象
   * 2. 记录攻击模式(attackMode)和士气槽位信息
   * 3. 验证回合记录的有效性
   *
   * 【第二阶段：球员信息记录】
   * 4. 记录A1攻击球员信息(位置、属性、技能状态)
   * 5. 记录A2辅助球员信息(特定攻击模式需要)
   * 6. 记录B防守球员信息(位置、属性、防守策略)
   *
   * 【第三阶段：阶段结果记录】
   * 7. 记录发起阶段的成功率和实际结果
   * 8. 记录动作ID和特殊效果
   * 9. 更新回合统计数据
   *
   * @param battleRecord 战报记录对象 - 包含所有回合的详细记录
   * @param roundIndex 回合索引 - 当前回合的编号(0-based)
   * @param attackerTeam 攻击方队伍 - 包含球员、属性、士气等信息
   * @param defenderTeam 防守方队伍 - 包含球员、属性、防守策略等信息
   * @param attackMode 攻击模式 - 枚举值，决定攻击类型和球员选择
   * @param periodResult 阶段结果 - 包含成功率、实际结果、加成信息等
   */
  recordStartPeriod(
    battleRounds: BattleRound[],
    roundIndex: number,
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    attackMode: AttackMode,
    periodResult: PeriodResult
  ): void {
    try {
      const roundRecord = battleRounds[roundIndex];
      if (!roundRecord) {
        this.logger.error(`回合记录不存在: ${roundIndex}`);
        return;
      }

      roundRecord.attackMode = attackMode;
      roundRecord.moraleSlotA = attackerTeam.attr?.moraleSlot || 0;
      roundRecord.moraleSlotB = defenderTeam.attr?.moraleSlot || 0;

      const periodInfo: PeriodInfo = {
        actionId: attackerTeam.roundInfo?.periodInfo?.[0]?.actionId || 0,
        A1Info: this.recordHeroInfo(attackerTeam, 'A1Info'),
        A2Info: this.recordHeroInfo(attackerTeam, 'A2Info'),
        BInfo: this.recordHeroInfo(defenderTeam, 'BInfo'),

        // 🔧 使用当前项目命名规范
        startCommentId: attackerTeam.roundInfo?.periodInfo?.[0]?.startCommentId || 0,
        resultCommentId: attackerTeam.roundInfo?.periodInfo?.[0]?.resultCommentId || 0,
        actionPer: (() => {
          const rate = periodResult.rate;
          if (isNaN(rate)) {
            this.logger.error(`发起阶段rate为NaN: periodResult=${JSON.stringify(periodResult)}, 回合=${roundIndex}`);
            return 0;
          }
          return Math.round(rate * 1000);
        })(), // 转换为千分制，记录NaN来源
        actionResult: periodResult.success ? 1 : 2, // 1=成功，2=失败
        moraleA: attackerTeam.attr.morale || 0,
        moraleB: defenderTeam.attr.morale || 0,
        skillEffectList: attackerTeam.roundInfo?.periodInfo?.[0]?.skillEffectList || []
      };

      roundRecord.periodInfo.push(periodInfo);
      this.logger.debug(`发起阶段记录完成: 回合=${roundIndex}, 成功率=${periodInfo.actionPer}`);
    } catch (error) {
      this.logger.error('记录发起阶段失败', error);
    }
  }

  /**
   * 🔧 记录推进阶段数据
   * 基于old项目: Room.prototype.recordMiddlePeriod
   */
  recordMiddlePeriod(
    battleRecord: { battleRoundInfo: BattleRound[] },
    roundIndex: number,
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    attackMode: number,
    periodResult: PeriodResult
  ): void {
    try {
      const roundRecord = battleRecord.battleRoundInfo[roundIndex];
      if (!roundRecord) {
        this.logger.error(`回合记录不存在: ${roundIndex}`);
        return;
      }

      const periodInfo: PeriodInfo = {
        actionId: attackerTeam.roundInfo?.periodInfo?.[1]?.actionId || 0,
        A1Info: this.recordHeroInfo(attackerTeam, 'A1Info'),
        A2Info: this.recordHeroInfo(attackerTeam, 'A2Info'),
        BInfo: this.recordHeroInfo(defenderTeam, 'BInfo'),
        startCommentId: attackerTeam.roundInfo?.periodInfo?.[1]?.startCommentId || 0,
        resultCommentId: attackerTeam.roundInfo?.periodInfo?.[1]?.resultCommentId || 0,
        actionPer: (() => {
          const rate = periodResult.rate;
          if (isNaN(rate)) {
            this.logger.error(`推进阶段rate为NaN: periodResult=${JSON.stringify(periodResult)}, 回合=${roundIndex}, 攻击模式=${attackMode}`);
            return 0;
          }
          return Math.round(rate * 1000);
        })(), // 转换为千分制，记录NaN来源
        actionResult: periodResult.success ? 1 : 0,
        moraleA: attackerTeam.attr?.morale || 0,
        moraleB: defenderTeam.attr?.morale || 0,
        skillEffectList: attackerTeam.roundInfo?.periodInfo?.[1]?.skillEffectList || []
      };

      roundRecord.periodInfo.push(periodInfo);
      this.logger.debug(`推进阶段记录完成: 回合=${roundIndex}, 成功率=${periodInfo.actionPer}, 结果=${periodInfo.actionResult}`);
    } catch (error) {
      this.logger.error('记录推进阶段失败', error);
    }
  }

  /**
   * 🔧 记录射门阶段数据
   * 基于old项目: Room.prototype.recordEndPeriod
   */
  recordEndPeriod(
    battleRecord: { battleRoundInfo: BattleRound[] },
    roundIndex: number,
    attackerTeam: BattleTeam,
    defenderTeam: BattleTeam,
    attackMode: number,
    periodResult: PeriodResult,
    currentScore: { teamA: number; teamB: number }
  ): void {
    try {
      const roundRecord = battleRecord.battleRoundInfo[roundIndex];
      if (!roundRecord) {
        this.logger.error(`回合记录不存在: ${roundIndex}`);
        return;
      }

      // 更新比分
      roundRecord.scoreA = currentScore.teamA;
      roundRecord.scoreB = currentScore.teamB;

      const periodInfo: PeriodInfo = {
        actionId: attackerTeam.roundInfo?.periodInfo?.[2]?.actionId || 0,
        A1Info: this.recordHeroInfo(attackerTeam, 'A1Info'),
        A2Info: this.recordHeroInfo(attackerTeam, 'A2Info'),
        BInfo: this.recordHeroInfo(defenderTeam, 'BInfo'),
        GKInfo: this.recordHeroInfo(defenderTeam, 'GKInfo'),
        startCommentId: attackerTeam.roundInfo?.periodInfo?.[2]?.startCommentId || 0,
        resultCommentId: attackerTeam.roundInfo?.periodInfo?.[2]?.resultCommentId || 0,
        actionPer: (() => {
          const rate = periodResult.rate;
          if (isNaN(rate)) {
            this.logger.error(`射门阶段rate为NaN: periodResult=${JSON.stringify(periodResult)}, 回合=${roundIndex}, 攻击模式=${attackMode}`);
            return 0;
          }
          return Math.round(rate * 1000);
        })(), // 转换为千分制，记录NaN来源
        actionResult: periodResult.success ? 1 : 0,
        moraleA: attackerTeam.attr?.morale || 0,
        moraleB: defenderTeam.attr?.morale || 0,
        skillEffectList: attackerTeam.roundInfo?.periodInfo?.[2]?.skillEffectList || []
      };

      // 某些模式A1没有参与射门
      if (attackMode === 4 || attackMode === 3) {
        periodInfo.A1Info = undefined;
      }

      roundRecord.periodInfo.push(periodInfo);
      this.logger.debug(`射门阶段记录完成: 回合=${roundIndex}, 成功率=${periodInfo.actionPer}, 进球=${periodInfo.actionResult}`);
    } catch (error) {
      this.logger.error('记录射门阶段失败', error);
    }
  }

  /**
   * 🔧 记录球员信息
   * 基于old项目: Room.prototype.recordHeroInfo
   */
  private recordHeroInfo(team: BattleTeam, actionHeroType: 'A1Info' | 'A2Info' | 'BInfo' | 'GKInfo'): HeroActionInfo | undefined {
    try {
      const roundInfo = team.roundInfo?.[actionHeroType];

      if (!roundInfo || !roundInfo.heroId) {
        return undefined;
      }

      const heroInfo: HeroActionInfo = {
        heroId: roundInfo.heroId || '', // 使用当前项目命名：heroId
        attrType1: roundInfo.attrType1 || 0,
        attrValue1: roundInfo.attrValue1 || 0,
        addValue1: roundInfo.addValue1 || 0,
        attrType2: roundInfo.attrType2 || 0,
        attrValue2: roundInfo.attrValue2 || 0,
        addValue2: roundInfo.addValue2 || 0
      };

      return heroInfo;
    } catch (error) {
      this.logger.warn(`记录球员信息失败: ${actionHeroType}`, error);
      return undefined;
    }
  }



  /**
   * 🔧 生成赛前信息
   * 基于old项目: Room.prototype.preBattleInfoJsonToClient
   */
  private async generatePreBattleInfo(battleData: any, leagueMsg?: any): Promise<any[]> {
    try {
      const preBattleInfo: any[] = [];
      const teams = [battleData.teamA, battleData.teamB];

      for (let i = 0; i < 2; i++) {
        const teamInfo = teams[i];
        const teamSide = i === 0 ? 'teamA' : 'teamB';

        preBattleInfo[i] = {
          type: teamSide,
          teamName: await this.getTeamDisplayName(teamInfo, battleData.battleType),
          rating: await this.getTeamRating(teamInfo),
          formationID: teamInfo.formationId || 0,
          attackTacticID: teamInfo.attr?.attackTacticID || teamInfo.tactic || 0,
          defendTacticID: teamInfo.attr?.defendTacticID || teamInfo.tactic || 0,
          battleType: battleData.battleType,
          faceUrl: await this.getTeamFaceUrl(teamInfo, battleData.battleType),
          battleName: this.getBattleName(battleData.battleType),
          memberInfo: await this.generateMemberInfo(teamInfo, battleData.battleType),

          // 🔧 战术克制关系信息
          atkTacticsAState: 0, // 默认无克制
          atkTacticsBState: 0, // 默认无克制

          // 原始属性值
          srcAtkValue: teamInfo.totalAttack || 0,
          srcDefValue: teamInfo.totalDefend || 0,

          // 加成因子（需要从战术克制计算中获取）
          atkValueFactor: 10000, // 默认1.0 * 10000
          defValueFactor: 10000, // 默认1.0 * 10000
          atkTrainerFactor: 0,   // 教练加成因子
          defTrainerFactor: 0    // 教练加成因子
        };

        // 🔧 联赛额外信息
        if (leagueMsg) {
          preBattleInfo[i].leagueName = leagueMsg.leagueName || "";
          preBattleInfo[i].roundName = leagueMsg.roundName || "";

          const rankInfo = i === 0 ? leagueMsg.homeRank : leagueMsg.awayRank;
          if (rankInfo) {
            preBattleInfo[i].winCount = rankInfo.winCount || 0;
            preBattleInfo[i].lossCount = rankInfo.lossCount || 0;
            preBattleInfo[i].drawCount = rankInfo.drawCount || 0;
            preBattleInfo[i].rank = rankInfo.rank || 0;
            preBattleInfo[i].totalValue = rankInfo.totalValue || 0;
          }
        }
      }

      // 🔧 获取战术克制关系并更新加成信息
      await this.updateTacticsRestrainInfo(preBattleInfo, battleData);

      return preBattleInfo;
    } catch (error) {
      this.logger.error('生成赛前信息失败', error);
      // 返回简化版本作为兜底
      return [
        {
          type: 'teamA',
          teamName: battleData.teamA?.playerName || 'Team A',
          rating: 0,
          memberInfo: []
        },
        {
          type: 'teamB',
          teamName: battleData.teamB?.playerName || 'Team B',
          rating: 0,
          memberInfo: []
        }
      ];
    }
  }

  /**
   * 🔧 生成战斗结束信息
   * 基于old项目: battleEndInfoJsonToClient
   */
  private async generateBattleEndInfo(battleData: BattleData, teamAScore: number, teamBScore: number): Promise<any> {
    try {
      const battleEndInfo: any = {
        winner: this.determineWinner(teamAScore, teamBScore),
        finalScore: {
          teamA: teamAScore,
          teamB: teamBScore
        },
        battleTime: battleData.battleTime || 90,
        totalRounds: battleData.battleRecord.battleRoundInfo?.length || 0,
        goalRecord: [] // 进球记录
      };

      // 🔧 生成进球记录（基于old项目逻辑）
      if (battleData.battleRecord.battleRoundInfo) {
        battleData.battleRecord.battleRoundInfo.forEach((round, index: number) => {
          // 检查比分变化来判断是否有进球
          if (index > 0) {
            const prevRound = battleData.battleRecord.battleRoundInfo[index - 1];
            if (round.scoreA > prevRound.scoreA || round.scoreB > prevRound.scoreB) {
              const goalInfo = {
                roundIndex: index,
                eventTime: round.eventTime || (index + 1) * 60,
                scorer: round.attackerType,
                goalType: 'normal'
              };
              battleEndInfo.goalRecord.push(goalInfo);
            }
          }
        });
      }

      // 🔧 奖励系统集成（基于old项目逻辑）
      await this.integrateRewardSystem(battleEndInfo, battleData, teamAScore, teamBScore);

      return battleEndInfo;
    } catch (error) {
      this.logger.error('生成战斗结束信息失败', error);
      return {
        winner: this.determineWinner(teamAScore, teamBScore),
        finalScore: { teamA: teamAScore, teamB: teamBScore },
        battleTime: battleData.battleTime || 90,
        totalRounds: battleData.battleRecord.battleRoundInfo?.length || 0,
        goalRecord: []
      };
    }
  }

  /**
   * 🔧 奖励系统集成
   * 委托给BattleRewardService处理所有奖励计算逻辑
   * 保持战报生成器的单一职责
   */
  private async integrateRewardSystem(
    battleEndInfo: any,
    battleData: BattleData,
    teamAScore: number,
    teamBScore: number
  ): Promise<void> {
    try {
      // 🔧 委托给专门的奖励计算器处理
      const rewardResult = await this.battleRewardCalculator.calculateBattleRewards(
        battleData,
        teamAScore,
        teamBScore
      );

      if (rewardResult.success && rewardResult.data) {
        const rewards = rewardResult.data;

        // 🔧 将奖励数据集成到战斗结束信息中
        if (rewards.lootItemList) {
          battleEndInfo.lootItemList = rewards.lootItemList;
        }

        if (rewards.businessReward) {
          battleEndInfo.businessReward = rewards.businessReward;
        }

        if (rewards.generalRewards) {
          battleEndInfo.rewards = rewards.generalRewards;
        }
      }

      this.logger.debug(`奖励系统集成完成: ${battleData.battleType}`);
    } catch (error) {
      this.logger.error('奖励系统集成失败', error);
      // 确保基础结构存在
      if (!battleEndInfo.lootItemList) battleEndInfo.lootItemList = [];
      if (!battleEndInfo.businessReward) battleEndInfo.businessReward = {};
    }
  }





  /**
   * 🔧 生成技能记录
   * 基于old项目: Room.prototype.toJsonClientSkillRecord
   */
  private generateSkillRecord(battleData: BattleData, skillSystem?: any): any[] {
    try {
      const skillRecord: any[] = [];

      // 如果有skillSystem，从中获取技能记录
      if (skillSystem && typeof skillSystem.getTriggeredSkillRecords === 'function') {
        const triggeredRecords = skillSystem.getTriggeredSkillRecords();

        // 🔧 基于old项目的技能记录格式
        for (let i = 0; i < 2; i++) {
          skillRecord[i] = {
            durRecord: triggeredRecords.durative[i] || [],
            insRecord: triggeredRecords.instant[i] || [],
            nextAtkRecord: triggeredRecords.nextAttack[i] || []
          };
        }
      } else {
        // 降级方案：从battleData中提取
        for (let i = 0; i < 2; i++) {
          skillRecord[i] = {
            durRecord: this.extractDurativeSkillRecord(battleData, i),
            insRecord: this.extractInstantSkillRecord(battleData, i),
            nextAtkRecord: this.extractNextAttackSkillRecord(battleData, i)
          };
        }
      }

      this.logger.debug(`技能记录生成完成: teamA=${skillRecord[0]?.durRecord?.length || 0}条, teamB=${skillRecord[1]?.durRecord?.length || 0}条`);
      return skillRecord;
    } catch (error) {
      this.logger.error('生成技能记录失败', error);
      return [
        { durRecord: [], insRecord: [], nextAtkRecord: [] }, // teamA
        { durRecord: [], insRecord: [], nextAtkRecord: [] }  // teamB
      ];
    }
  }

  /**
   * 🔧 提取持续性技能记录
   * 基于old项目: durativeSkillRecord数据结构
   */
  private extractDurativeSkillRecord(battleData: BattleData, teamIndex: number): any[] {
    try {
      const records: any[] = [];

      // 从队伍技能效果中提取持续性技能记录
      const team = teamIndex === 0 ? battleData.teamA : battleData.teamB;
      if (team.skillEffects) {
        team.skillEffects.forEach((effect: any) => {
          if (effect.type === 'durative' && effect.skillId) {
            records.push({
              skillId: effect.skillId,
              startTime: effect.startTime || 0,
              endTime: effect.endTime || 0,
              heroUid: effect.heroUid || ''
            });
          }
        });
      }

      // 从回合记录中提取持续技能效果
      if (battleData.battleRecord.battleRoundInfo) {
        battleData.battleRecord.battleRoundInfo.forEach((round, roundIndex: number) => {
          // 从periodInfo中提取技能效果
          round.periodInfo.forEach((period) => {
            if (period.skillEffectList) {
              period.skillEffectList.forEach((skill: any) => {
                // 基于log.txt中的实际数据结构，检查teamIndex匹配
                const skillTeamIndex = skill.teamType === 'teamA' ? 0 : 1;
                if (skillTeamIndex === teamIndex) {
                  // 判断是否为持续技能（基于技能ID范围或其他特征）
                  // 暂时将所有技能都当作持续技能处理
                  records.push({
                    skillId: skill.skillId,
                    startTime: round.eventTime || roundIndex * 60,
                    endTime: (round.eventTime || roundIndex * 60) + 1800, // 假设持续30分钟
                    heroUid: skill.heroId || ''
                  });
                }
              });
            }
          });
        });
      }

      return records;
    } catch (error) {
      this.logger.error(`提取持续性技能记录失败: team${teamIndex}`, error);
      return [];
    }
  }

  /**
   * 🔧 提取瞬时技能记录
   * 基于old项目: instantSkillRecord数据结构
   */
  private extractInstantSkillRecord(battleData: BattleData, teamIndex: number): any[] {
    try {
      const records: any[] = [];

      // 从回合记录中提取瞬时技能
      if (battleData.battleRecord.battleRoundInfo) {
        battleData.battleRecord.battleRoundInfo.forEach((round, roundIndex: number) => {
          round.periodInfo.forEach((period) => {
            if (period.skillEffectList) {
              period.skillEffectList.forEach((skill: any) => {
                // 基于log.txt中的实际数据结构，检查teamIndex匹配
                const skillTeamIndex = skill.teamType === 'teamA' ? 0 : 1;
                if (skillTeamIndex === teamIndex) {
                  records.push({
                    skillId: skill.skillId,
                    round: roundIndex,
                    startTime: round.eventTime || roundIndex * 60,
                    endTime: round.eventTime || roundIndex * 60,
                    heroUid: skill.heroId || '',
                    effectValue: skill.addPer || 0
                  });
                }
              });
            }
          });
        });
      }

      return records;
    } catch (error) {
      this.logger.error(`提取瞬时技能记录失败: team${teamIndex}`, error);
      return [];
    }
  }

  /**
   * 🔧 提取下次攻击技能记录
   * 基于old项目: nextAtkSkillRecord数据结构
   */
  private extractNextAttackSkillRecord(battleData: BattleData, teamIndex: number): any[] {
    try {
      const records: any[] = [];

      // 从回合记录中提取下次攻击技能
      if (battleData.battleRecord.battleRoundInfo) {
        battleData.battleRecord.battleRoundInfo.forEach((round, roundIndex: number) => {
          round.periodInfo.forEach((period) => {
            if (period.skillEffectList) {
              period.skillEffectList.forEach((skill: any) => {
                // 基于log.txt中的实际数据结构，检查teamIndex匹配
                const skillTeamIndex = skill.teamType === 'teamA' ? 0 : 1;
                if (skillTeamIndex === teamIndex) {
                  // 暂时不处理下次攻击技能，因为log.txt中没有这类数据
                  // 保持空数组
                }
              });
            }
          });
        });
      }

      return records;
    } catch (error) {
      this.logger.error(`提取下次攻击技能记录失败: team${teamIndex}`, error);
      return [];
    }
  }

  /**
   * 🔧 生成战斗统计
   * 基于old项目: battleEndInfoJsonToClient中的stInfos统计数据
   */
  private generateBattleStatistic(battleData: BattleData, battleRoundInfo: any[]): any {
    try {
      // 🔧 基于old项目的统计数据结构
      const stInfos = [
        this.generateTeamStatistic(battleData, 'teamA', battleRoundInfo),
        this.generateTeamStatistic(battleData, 'teamB', battleRoundInfo)
      ];

      return {
        stInfos,
        totalRounds: battleRoundInfo.length,
        battleTime: battleData.battleTime || 90,
        // 额外的统计信息
        teamComparison: this.generateTeamComparison(battleData)
      };
    } catch (error) {
      this.logger.error('生成战斗统计失败', error);
      return {
        stInfos: [
          { teamType: 'teamA', shotNum: 0, ctrlBallPer: 50, breakPer: 0, placeKickNum: 0, bestBaller: '' },
          { teamType: 'teamB', shotNum: 0, ctrlBallPer: 50, breakPer: 0, placeKickNum: 0, bestBaller: '' }
        ],
        totalRounds: 0,
        battleTime: 90
      };
    }
  }

  /**
   * 🔧 生成队伍统计数据
   * 基于old项目: stInfos数据结构
   */
  private generateTeamStatistic(battleData: BattleData, teamType: 'teamA' | 'teamB', battleRoundInfo: any[]): any {
    try {
      const teamData = battleData[teamType];
      const opponentType = teamType === 'teamA' ? 'teamB' : 'teamA';
      const opponentData = battleData[opponentType];

      // 🔧 计算控球率（基于士气值）
      const teamMorale = teamData.attr?.morale || 50;
      const opponentMorale = opponentData.attr?.morale || 50;
      const totalMorale = teamMorale + opponentMorale;
      const ctrlBallPer = totalMorale > 0 ? Math.round((teamMorale / totalMorale) * 100) : 50;

      // 🔧 计算突破成功率
      const breakNum = teamData.statistic?.breaks || 0;
      const breakSucNum = teamData.statistic?.successfulBreaks || 0;
      const breakPer = breakNum > 0 ? Math.round((breakSucNum / breakNum) * 100) : 0;

      // 🔧 获取最佳球员
      const bestBaller = this.getBestPlayer(teamData, teamType, battleData.battleType);

      return {
        teamType,
        shotNum: teamData.statistic?.shots || 0,
        ctrlBallPer,
        breakPer,
        placeKickNum: teamData.statistic?.setPlays || 0,
        bestBaller,
        // 额外统计数据
        passNum: this.calculatePassNum(battleRoundInfo, teamType),
        tackleNum: this.calculateTackleNum(battleRoundInfo, teamType),
        foulNum: this.calculateFoulNum(battleRoundInfo, teamType)
      };
    } catch (error) {
      this.logger.error(`生成${teamType}统计数据失败`, error);
      return {
        teamType,
        shotNum: 0,
        ctrlBallPer: 50,
        breakPer: 0,
        placeKickNum: 0,
        bestBaller: ''
      };
    }
  }

  /**
   * 🔧 获取最佳球员
   * 基于old项目: getTheBestHeroUid和特殊战斗类型处理
   */
  private getBestPlayer(teamData: BattleTeam, teamType: string, battleType: string): string {
    try {
      // 🔧 从球员评分Map中找到最高分球员
      let maxScore = 0;
      let bestUid = '';

      if (teamData.statistic?.ballerScoreMap) {
        for (const [heroUid, score] of teamData.statistic.ballerScoreMap) {
          if (score > maxScore) {
            maxScore = score;
            bestUid = heroUid;
          }
        }
      }

      // 🔧 获取球员名称（特殊战斗类型处理）
      if (bestUid && teamType === 'teamB' &&
          ['MiddleEast', 'GulfCup', 'MLS'].includes(battleType)) {
        return this.getSpecialBattlePlayerName(bestUid, teamData, battleType);
      }

      // 🔧 普通情况：从英雄数据获取名称
      if (bestUid && teamData.heroes) {
        const hero = teamData.heroes.find((h: any) => h.heroId === bestUid || h.uid === bestUid);
        return hero?.name || `Player ${bestUid}`;
      }

      return '';
    } catch (error) {
      this.logger.error('获取最佳球员失败', error);
      return '';
    }
  }

  /**
   * 🔧 获取特殊战斗类型球员名称
   * 基于old项目: 特殊战斗类型的球员名称处理逻辑
   */
  private getSpecialBattlePlayerName(heroUid: string, _teamData: any, _battleType: string): string {
    try {
      // 这里需要根据配置表获取特殊战斗类型的球员名称
      // 由于架构限制，暂时返回默认名称
      return `Special Player ${heroUid}`;
    } catch (error) {
      this.logger.error('获取特殊战斗类型球员名称失败', error);
      return `Player ${heroUid}`;
    }
  }

  /**
   * 🔧 计算传球次数
   * 基于回合信息统计传球数据
   */
  private calculatePassNum(battleRoundInfo: any[], teamType: string): number {
    try {
      let passNum = 0;
      battleRoundInfo.forEach(round => {
        if (round.attacker === teamType && round.periodInfo?.length > 0) {
          // 发起阶段成功视为传球成功
          if (round.periodInfo[0]?.result === 1) {
            passNum++;
          }
        }
      });
      return passNum;
    } catch (error) {
      this.logger.error(`计算${teamType}传球次数失败`, error);
      return 0;
    }
  }

  /**
   * 🔧 计算抢断次数
   * 基于回合信息统计抢断数据
   */
  private calculateTackleNum(battleRoundInfo: any[], teamType: string): number {
    try {
      let tackleNum = 0;
      const opponentType = teamType === 'teamA' ? 'teamB' : 'teamA';

      battleRoundInfo.forEach(round => {
        if (round.attacker === opponentType && round.periodInfo?.length > 1) {
          // 对手推进阶段失败视为己方抢断成功
          if (round.periodInfo[1]?.result === 0) {
            tackleNum++;
          }
        }
      });
      return tackleNum;
    } catch (error) {
      this.logger.error(`计算${teamType}抢断次数失败`, error);
      return 0;
    }
  }

  /**
   * 🔧 计算犯规次数
   * 基于回合信息统计犯规数据
   */
  private calculateFoulNum(battleRoundInfo: any[], teamType: string): number {
    try {
      let foulNum = 0;
      battleRoundInfo.forEach(round => {
        if (round.attacker === teamType && round.foulType) {
          foulNum++;
        }
      });
      return foulNum;
    } catch (error) {
      this.logger.error(`计算${teamType}犯规次数失败`, error);
      return 0;
    }
  }

  /**
   * 🔧 生成队伍对比数据
   * 额外的统计对比信息
   */
  private generateTeamComparison(battleData: BattleData): any {
    try {
      const teamAScore = battleData.teamA.score || 0;
      const teamBScore = battleData.teamB.score || 0;

      return {
        scoreDifference: teamAScore - teamBScore,
        totalGoals: teamAScore + teamBScore,
        winner: teamAScore > teamBScore ? 'teamA' : teamBScore > teamAScore ? 'teamB' : 'draw',
        dominantTeam: this.calculateDominantTeam(battleData)
      };
    } catch (error) {
      this.logger.error('生成队伍对比数据失败', error);
      return {
        scoreDifference: 0,
        totalGoals: 0,
        winner: 'draw',
        dominantTeam: 'balanced'
      };
    }
  }

  /**
   * 🔧 计算优势队伍
   * 基于多项统计数据综合判断
   */
  private calculateDominantTeam(battleData: BattleData): string {
    try {
      const teamAShots = battleData.teamA.statistic?.shots || 0;
      const teamBShots = battleData.teamB.statistic?.shots || 0;
      const teamAMorale = battleData.teamA.attr?.morale || 50;
      const teamBMorale = battleData.teamB.attr?.morale || 50;

      const teamAAdvantage = teamAShots + (teamAMorale - 50) * 0.1;
      const teamBAdvantage = teamBShots + (teamBMorale - 50) * 0.1;

      if (Math.abs(teamAAdvantage - teamBAdvantage) < 2) {
        return 'balanced';
      }

      return teamAAdvantage > teamBAdvantage ? 'teamA' : 'teamB';
    } catch (error) {
      this.logger.error('计算优势队伍失败', error);
      return 'balanced';
    }
  }

  /**
   * 🔧 确定胜者
   */
  private determineWinner(teamAScore: number, teamBScore: number): string {
    if (teamAScore > teamBScore) {
      return 'teamA';
    } else if (teamBScore > teamAScore) {
      return 'teamB';
    } else {
      return 'draw';
    }
  }

  /**
   * 🔧 初始化回合记录
   */
  initRoundRecord(roundIndex: number, eventTime: number, attacker: string): any {
    return {
      roundIndex,
      eventTime,
      attacker,
      attackMode: 0,
      scoreA: 0,
      scoreB: 0,
      moraleSlotA: 0,
      moraleSlotB: 0,
      periodInfo: []
    };
  }

  /**
   * 🔧 更新球员评分
   * 基于old项目的BattleBaseScore逻辑
   */
  updatePlayerScore(team: BattleTeam, heroUid: string, scoreType: string, value: number): void {
    try {
      if (!team.statistic?.ballerScoreMap) {
        return;
      }

      const currentScore = team.statistic.ballerScoreMap.get(heroUid) || 0;
      team.statistic.ballerScoreMap.set(heroUid, currentScore + value);

      this.logger.debug(`更新球员评分: ${heroUid}, 类型=${scoreType}, 分数=${value}`);
    } catch (error) {
      this.logger.warn(`更新球员评分失败: ${heroUid}`, error);
    }
  }

  /**
   * 🔧 获取英雄名称
   * 基于old项目: Room.prototype.getHeroName
   * 用于战报和评论系统显示球员姓名
   */
  getHeroName(battleData: BattleData, teamType: string, heroUid: string): string {
    try {
      const team = battleData[teamType];
      if (!team || !team.heroes) {
        return '未知球员';
      }

      const hero = team.heroes.find((h: any) => h.uid === heroUid || h.id === heroUid);
      if (!hero) {
        return '未知球员';
      }

      return hero.name || hero.cnName || `球员${heroUid}`;
    } catch (error) {
      this.logger.warn(`获取英雄名称失败: ${heroUid}`, error);
      return '未知球员';
    }
  }

  /**
   * 🔧 战斗结束信息JSON输出
   * 基于old项目: Room.prototype.battleEndInfoJsonToClient
   * 生成完整的战斗结束信息供客户端使用
   */
  battleEndInfoJsonToClient(battleData: BattleData): any {
    try {
      const result = {
        // 基础战斗信息
        battleId: battleData.battleId || '',
        battleType: battleData.battleType || 'PVP',
        battleTime: battleData.battleTime || 0,
        totalRounds: battleData.roundIndex + 1 || 0,

        // 比赛结果
        finalScore: {
          teamA: battleData.teamA.score || 0,
          teamB: battleData.teamB.score || 0
        },

        // 获胜方信息
        winner: this.determineWinner(battleData.teamA.score || 0, battleData.teamB.score || 0),

        // 队伍信息
        teams: {
          teamA: this.formatTeamInfo(battleData.teamA, 'teamA'),
          teamB: this.formatTeamInfo(battleData.teamB, 'teamB')
        },

        // 战斗记录
        battleRecord: this.formatBattleRecord(battleData.battleRecord),

        // 统计信息
        statistics: this.formatBattleStatistics(battleData),

        // 奖励信息（如果有）
        rewards: this.formatRewards(battleData),

        // 时间戳
        timestamp: Date.now()
      };

      this.logger.debug('战斗结束信息JSON生成完成');
      return result;

    } catch (error) {
      this.logger.error('生成战斗结束信息JSON失败', error);
      return {
        battleId: battleData.battleId || '',
        error: '数据生成失败',
        timestamp: Date.now()
      };
    }
  }



  /**
   * 🔧 格式化队伍信息
   * 辅助方法：格式化队伍信息供客户端使用
   */
  private formatTeamInfo(team: any, teamType: string): any {
    try {
      return {
        teamType,
        playerId: team.playerId || team.characterId || '',
        playerName: team.playerName || team.characterName || '未知玩家',
        level: team.level || 1,
        score: team.score || 0,
        formation: team.formationId || 0,
        tactic: team.tactic || 101,
        totalAttack: team.totalAttack || 0,
        totalDefend: team.totalDefend || 0,
        heroes: this.formatHeroList(team.heroes || []),
        statistics: team.statistic || {}
      };
    } catch (error) {
      this.logger.warn(`格式化队伍信息失败: ${teamType}`, error);
      return { teamType, error: '队伍信息格式化失败' };
    }
  }

  /**
   * 🔧 格式化英雄列表
   * 辅助方法：格式化英雄列表供客户端显示
   */
  private formatHeroList(heroes: any[]): any[] {
    try {
      return heroes.map(hero => ({
        uid: hero.heroid || hero.id,
        name: hero.name || hero.cnName || '未知球员',
        position: hero.position || 'ST',
        level: hero.level || 1,
        attack: hero.attack || 0,
        defend: hero.defend || 0,
        rating: hero.rating || 0
      }));
    } catch (error) {
      this.logger.warn('格式化英雄列表失败', error);
      return [];
    }
  }

  /**
   * 🔧 格式化战斗记录
   * 辅助方法：格式化战斗记录供客户端回放
   */
  private formatBattleRecord(battleRecord: any): any {
    try {
      if (!battleRecord || !battleRecord.battleRoundInfo) {
        return { rounds: [] };
      }

      return {
        rounds: battleRecord.battleRoundInfo.map((round: any, index: number) => ({
          roundIndex: index,
          eventTime: round.eventTime || 0,
          attackerType: round.attackerType || 'teamA',
          attackMode: round.attackMode || 0,
          scoreA: round.scoreA || 0,
          scoreB: round.scoreB || 0,
          periods: round.periodInfo || [],
          comments: round.comments || []
        }))
      };
    } catch (error) {
      this.logger.warn('格式化战斗记录失败', error);
      return { rounds: [] };
    }
  }

  /**
   * 🔧 格式化战斗统计
   * 辅助方法：格式化战斗统计信息
   */
  private formatBattleStatistics(battleData: BattleData): any {
    try {
      return {
        totalTime: battleData.battleTime || 0,
        totalRounds: (battleData.roundIndex || 0) + 1,
        teamA: {
          shots: battleData.teamA.statistic?.shots || 0,
          shotsOnTarget: battleData.teamA.statistic?.shotsOnTarget || 0,
          possession: battleData.teamA.statistic?.possession || 50,
          passes: battleData.teamA.statistic?.passes || 0,
          fouls: battleData.teamA.statistic?.fouls || 0
        },
        teamB: {
          shots: battleData.teamB.statistic?.shots || 0,
          shotsOnTarget: battleData.teamB.statistic?.shotsOnTarget || 0,
          possession: battleData.teamB.statistic?.possession || 50,
          passes: battleData.teamB.statistic?.passes || 0,
          fouls: battleData.teamB.statistic?.fouls || 0
        }
      };
    } catch (error) {
      this.logger.warn('格式化战斗统计失败', error);
      return {};
    }
  }

  /**
   * 🔧 格式化奖励信息
   * 辅助方法：格式化战斗奖励信息
   */
  private formatRewards(battleData: BattleData): any {
    try {
      if (!battleData.rewards) {
        return null;
      }

      return {
        winner: battleData.rewards.winner || null,
        loser: battleData.rewards.loser || null,
        experience: battleData.rewards.experience || 0,
        coins: battleData.rewards.coins || 0,
        items: battleData.rewards.items || []
      };
    } catch (error) {
      this.logger.warn('格式化奖励信息失败', error);
      return null;
    }
  }

  /**
   * 🔧 获取联赛副本奖励
   * 基于old项目: Room.prototype.getLeagueCopyReward
   * 计算联赛副本的奖励内容
   */
  async getLeagueCopyReward(battleData: BattleData): Promise<any[]> {
    try {
      const reward: any[] = [];

      // 🔧 检查战斗类型（基于old项目逻辑）
      if (battleData.battleType !== BattleType.PVE_LEAGUE) {
        return reward;
      }

      const teamInfo = battleData.teamB;

      // 🔧 计算完成星级（基于射门次数差）
      let finishedStar = (battleData.teamA.statistic?.shots || 0) - (battleData.teamB.statistic?.shots || 0);
      if (finishedStar > 3) {
        finishedStar = 3;
      }
      if (finishedStar < 0) {
        finishedStar = 0;
      }

      // 🔧 获取PVE通用配置
      const config = await this.getPveCommonConfig(battleData.battleType, String(teamInfo.teamResId));
      if (!config) {
        this.logger.warn('获取战斗奖励配置失败', battleData.battleType, teamInfo.teamResId);
        return reward;
      }

      // 🔧 计算奖励（基于星级进度）
      if (finishedStar > (teamInfo.takeCopyRewardProcess || 0)) {
        let totalExp = 0;

        for (let i = (teamInfo.takeCopyRewardProcess || 0) + 1; i <= finishedStar; i++) {
          const lootItem: any = {};
          lootItem.resId = config[`Reward${i}`];
          lootItem.num = config[`RewardNum${i}`];
          totalExp += config[`Exp${i}`] || 0;

          if (lootItem.resId > 0 && lootItem.num > 0) {
            reward.push(lootItem);
          }
        }

        // 🔧 添加经验奖励（基于old项目逻辑）
        if (totalExp > 0) {
          reward.push({
            resId: 1001, // FAME/经验的资源ID
            num: totalExp
          });
        }
      }

      this.logger.debug(`联赛副本奖励计算完成: 星级=${finishedStar}, 奖励数量=${reward.length}`);
      return reward;

    } catch (error) {
      this.logger.error('获取联赛副本奖励失败', error);
      return [];
    }
  }

  /**
   * 🔧 获取PVE通用配置
   * 基于old项目: getPveCommonConfig方法
   */
  private async getPveCommonConfig(battleType: string, teamResId: string): Promise<any> {
    try {
      // 根据战斗类型获取对应的配置表
      switch (battleType) {
        case 'PveLeagueCopy':
          // 从联赛副本配置表获取
          const leagueConfigs = await this.gameConfig.leagueCopy.getAll();
          return leagueConfigs.find((config: any) => config.id === teamResId);

        default:
          this.logger.warn(`未支持的PVE战斗类型: ${battleType}`);
          return null;
      }
    } catch (error) {
      this.logger.error('获取PVE通用配置失败', error);
      return null;
    }
  }

  // ==================== 赛前信息生成辅助方法 ====================

  /**
   * 🔧 获取队伍显示名称
   */
  private async getTeamDisplayName(teamInfo: any, _battleType: string): Promise<string> {
    return teamInfo.playerName || teamInfo.teamName || 'Unknown Team';
  }

  /**
   * 🔧 获取队伍评分
   */
  private async getTeamRating(teamInfo: any): Promise<number> {
    const totalAttack = teamInfo.totalAttack || 0;
    const totalDefend = teamInfo.totalDefend || 0;
    return Math.round((totalAttack + totalDefend) / 2);
  }

  /**
   * 🔧 获取队伍头像URL
   */
  private async getTeamFaceUrl(teamInfo: any, _battleType: string): Promise<string> {
    return teamInfo.faceUrl || '0';
  }

  /**
   * 🔧 获取战斗名称
   */
  private getBattleName(battleType: string): string {
    const battleNames: { [key: string]: string } = {
      'PVE': '巡回赛',
      'League': '联赛',
      'Cup': '杯赛',
      'Tournament': '锦标赛',
      'PVP': '商业赛',
      'PvpGroundMatch': '球场争夺战',
      'WarOfFaith': '信仰之战',
      'MiddleEast': '中东杯赛',
      'GulfCup': '海湾杯赛',
      'MLS': '美职联赛',
      'FirstBattle': '首场战斗',
      'Test': '测试战斗',
      'Guest': '游客模式'
    };
    return battleNames[battleType] || '未知战斗';
  }

  /**
   * 🔧 生成球员信息
   */
  private async generateMemberInfo(teamInfo: any, _battleType: string): Promise<any[]> {
    const memberInfo: any[] = [];
    if (!teamInfo.heroes || teamInfo.heroes.length === 0) {
      return memberInfo;
    }

    for (const hero of teamInfo.heroes) {
      memberInfo.push({
        heroUid: hero.heroId || hero.heroid,
        name: hero.name || `Player ${hero.position}`,
        position: hero.position,
        rating: hero.rating || 0,
        resId: hero.resId || hero.heroId
      });
    }
    return memberInfo;
  }

  /**
   * 🔧 更新战术克制信息
   */
  private async updateTacticsRestrainInfo(preBattleInfo: any[], _battleData: BattleData): Promise<void> {
    // 简化实现：设置默认的战术克制信息
    for (let i = 0; i < preBattleInfo.length; i++) {
      preBattleInfo[i].atkTacticsAState = 0;
      preBattleInfo[i].atkTacticsBState = 0;
      preBattleInfo[i].atkValueFactor = 10000;
      preBattleInfo[i].defValueFactor = 10000;
      preBattleInfo[i].atkTrainerFactor = 0;
      preBattleInfo[i].defTrainerFactor = 0;
    }
  }
}
