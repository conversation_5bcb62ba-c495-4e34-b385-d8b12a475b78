import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { GroundMatchController } from './ground-match.controller';
import { GroundMatchService } from './ground-match.service';
import { GroundMatchRepository } from '../../common/repositories/ground-match.repository';
import { GroundMatch, GroundMatchSchema } from '../../common/schemas/ground-match.schema';

import { BattleModule } from '../battle/battle.module';
import { BattleDataService } from '@match/common/services/battle-data.service';

/**
 * 个人球场争夺战模块
 * 基于old项目footballGround.js和groundMatchService.js的功能实现
 * 
 * 功能特性：
 * - 训练场占领系统
 * - 抢夺和驱赶机制  
 * - 举报和保护系统
 * - 搜索和匹配功能
 * - 排行榜管理
 * - 奖励计算和发放
 * 
 * 技术特性：
 * - 完整的Result模式集成
 * - 微服务架构支持
 * - Redis缓存优化
 * - MongoDB数据持久化
 * - 游戏配置系统集成
 * - 统一的日志和监控
 */
@Module({
  imports: [
    // MongoDB Schema注册
    MongooseModule.forFeature([
      { name: GroundMatch.name, schema: GroundMatchSchema }
    ]),
    
    // 导入BattleModule以使用BattleService
    BattleModule,
  ],
  controllers: [
    GroundMatchController  // 微服务控制器
  ],
  providers: [
    GroundMatchService,    // 业务逻辑服务
    GroundMatchRepository,  // 数据访问层
    BattleDataService, // 战斗数据转换共享服务
  ],
  exports: [
    GroundMatchService,    // 导出服务供其他模块使用
    GroundMatchRepository  // 导出Repository供其他模块使用
  ]
})
export class GroundMatchModule {
  constructor() {}
}
