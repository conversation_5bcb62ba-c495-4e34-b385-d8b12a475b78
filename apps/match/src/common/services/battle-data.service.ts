import { Lineup } from '@character/common/schemas/lineup.schema';
import { Injectable, Logger } from '@nestjs/common';
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@libs/shared/constants';
import { HeroPosition, HERO_POSITION_INDEX_MAP } from '@libs/game-constants';
import { BattleHero, BattleTeam, TeamType, TeamAttributes, TeamStatistics, RoundInfo } from '@match/modules/battle/types/battle-data.types';

// Result模式相关导入
import { BaseService } from '@libs/common/service';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 战斗数据转换共享服务 - 数据层纯化版本
 *
 * 🎯 核心职责（数据获取层）：
 * - 统一的球员数据获取（微服务调用）
 * - 统一的数据格式转换（Hero → BattleHero格式）
 * - 统一的阵容数据处理（Lineup → BattleTeam格式）
 * - 消除各模块间的重复代码
 *
 * 🚫 不负责的职责（由Battle模块处理）：
 * - 属性计算逻辑（由BattleAttributeCalculator处理）
 * - 位置加成计算（由BattleSkillSystem处理）
 * - 战斗数据初始化（由BattleInitializer处理）
 *
 * 使用模块：
 * - League 联赛系统
 * - Business 商业赛系统
 * - Trophy 杯赛系统
 * - Tournament 锦标赛系统
 *
 * 🚀 性能优化：
 * - 缓存球员数据转换结果
 * - 批量数据获取优化
 * - 智能缓存管理，防止内存泄漏
 *
 * Result模式适配：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public方法返回XResult<T>类型
 * - 标准化的微服务调用和错误处理
 * - 移除try-catch块，使用Result模式处理错误
 */
@Injectable()
export class BattleDataService extends BaseService {

  // 🚀 性能优化：缓存转换结果
  private readonly heroConversionCache = new Map<string, any>();
  private readonly maxCacheSize = 500;
  private cacheAccessCount = 0;

  constructor(
    microserviceClient: MicroserviceClientService, // 传递给BaseService
  ) {
    // 调用BaseService构造函数，传入服务名称和微服务客户端
    super('BattleDataService', microserviceClient);
  }

  /**
   * 🚀 缓存管理 - 防止内存泄漏
   */
  private manageHeroCache(): void {
    this.cacheAccessCount++;

    // 每500次访问清理一次缓存
    if (this.cacheAccessCount % 500 === 0) {
      if (this.heroConversionCache.size > this.maxCacheSize) {
        // 清理最旧的50%缓存条目
        const entries = Array.from(this.heroConversionCache.entries());
        const keepCount = Math.floor(this.maxCacheSize * 0.5);

        this.heroConversionCache.clear();

        // 保留最近的条目
        for (let i = entries.length - keepCount; i < entries.length; i++) {
          this.heroConversionCache.set(entries[i][0], entries[i][1]);
        }

        this.logger.debug(`球员转换缓存清理完成，保留 ${keepCount} 个条目`);
      }
    }
  }

  /**
   * 从Hero服务获取完整的球员数据并转换为战斗格式
   * 统一实现，消除各模块重复代码
   * 已适配Result模式：使用callMicroservice和XResult返回类型
   */
  async getHeroesData(characterId: string, heroIds: string[]): Promise<XResult<any[]>> {
    if (!heroIds || heroIds.length === 0) {
      return XResultUtils.error('球员ID列表为空', 'HERO_IDS_EMPTY');
    }

    this.logger.log(`开始获取球员数据: 角色=${characterId}, 球员数=${heroIds.length}`);

    // 使用BaseService的标准化微服务调用
    const heroesResult = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'hero.getBatch',
      {
        heroIds: heroIds,
        serverId: 'server_001'
      }
    );

    if (XResultUtils.isFailure(heroesResult)) {
      return XResultUtils.error(`获取球员数据失败: ${heroesResult.message}`, heroesResult.code);
    }

    const rawHeroes = heroesResult.data || [];
    this.logger.log(`球员数据获取成功: 返回${rawHeroes.length}个球员`);

    // 🔍 调试：记录第一个球员的完整数据结构
    if (rawHeroes.length > 0) {
      this.logger.debug(`第一个球员的完整数据结构:`, JSON.stringify(rawHeroes[0], null, 2));
    }

    // 🚀 优化：批量转换为Battle Schema期望的格式，使用缓存
    const battleHeroes = rawHeroes.map((hero: any, index: number) => this.convertHeroToBattleFormat(hero, index));

    this.logger.log(`球员数据格式转换完成: ${battleHeroes.length}个球员`);

    // 🔍 调试：记录第一个转换后的战斗数据
    if (battleHeroes.length > 0) {
      this.logger.debug(`第一个转换后的战斗数据:`, JSON.stringify(battleHeroes[0], null, 2));
    }

    return XResultUtils.ok(battleHeroes);
  }

  /**
   * 🚀 将Hero服务返回的球员数据转换为Battle Schema期望的格式 - 数据层纯化版本
   * 统一实现，使用项目统一的类型定义，确保架构一致性
   *
   * 🎯 数据层职责：
   * - 数据格式转换（Hero → BattleHero结构）
   * - 保留完整原始数据（attributes, oneLevelAttr等）
   * - 位置映射和基础验证
   * - 缓存转换结果，提升性能
   *
   * 🚫 不负责：
   * - 属性计算逻辑（由Battle模块处理）
   * - 位置加成计算（由BattleSkillSystem处理）
   * - 技能效果计算（由BattleSkillSystem处理）
   *
   * 🚀 性能优化：
   * - 缓存转换结果，避免重复转换
   * - 智能缓存管理，防止内存泄漏
   * - 与新战斗引擎数据格式完全兼容
   */
  convertHeroToBattleFormat(hero: any, index: number): BattleHero {
    try {
      this.logger.log(`Hero 原始数据结构：${JSON.stringify(hero, null, 2)}`);
      // 🚀 优化：生成缓存键
      const heroId = hero.heroId || hero.uid || hero.id || `hero_${index}`;
      const cacheKey = `${heroId}_${hero.level || 1}_${JSON.stringify(hero.attributes || {})}`;

      // 🚀 优化：检查缓存
      if (this.heroConversionCache.has(cacheKey)) {
        this.manageHeroCache(); // 缓存管理
        const cachedResult = this.heroConversionCache.get(cacheKey);
        // 更新位置信息（位置可能因index变化）
        cachedResult.position = HERO_POSITION_INDEX_MAP[index % 12] || HeroPosition.MC;
        return cachedResult;
      }

      // 使用统一的位置映射常量
      const position = HERO_POSITION_INDEX_MAP[index % 12] || HeroPosition.MC;

      // 🔍 调试：记录原始Hero数据结构
      this.logger.debug(`转换球员数据 ${hero.name || `hero_${index}`}:`, {
        heroId: hero.heroId || hero.uid || hero.id,
        原始数据结构: {
          hasAttributes: !!hero.attributes,
          attributesType: typeof hero.attributes,
          directAttributes: {
            speed: hero.speed,
            shooting: hero.shooting,
            attack: hero.attack,
            defend: hero.defend
          },
          nestedAttributes: hero.attributes
        }
      });

      // 🔧 从新的hero.schema.ts中提取属性并计算战斗属性
      const attrs = hero.attributes || {};

      // 计算战斗核心属性
      const attack = this.calculateAttackValue(attrs);
      const defend = this.calculateDefendValue(attrs);
      const speed = attrs.speed?.cur || 50;
      const power = attrs.strength?.cur || 50;
      const technique = this.calculateTechniqueValue(attrs);

      // 构造优化后的BattleHero数据格式（适配新的BattleHero接口）
      const battleHero = {
        // 基础信息
        heroId: hero.heroId || hero.uid || hero.id || `hero_${index}`,
        name: hero.name,
        resId: hero.resId,
        level: hero.level || 1,
        position: position,
        mainPosition: hero.mainPosition || position,

        // 养成相关字段
        breakLevel: hero.breakLevel || 0,
        starLevel: hero.starLevel || hero.star || 0,
        evolutionStage: hero.evolutionStage || 0,

        // HeroAttributes中的20个属性 - 基于hero.schema.ts
        attributes: {
          // 基础身体属性
          speed: { base: attrs.speed?.base || 50, cur: attrs.speed?.cur || 50 },
          jumping: { base: attrs.jumping?.base || 50, cur: attrs.jumping?.cur || 50 },
          strength: { base: attrs.strength?.base || 50, cur: attrs.strength?.cur || 50 },
          stamina: { base: attrs.stamina?.base || 50, cur: attrs.stamina?.cur || 50 },
          explosiveForce: { base: attrs.explosiveForce?.base || 50, cur: attrs.explosiveForce?.cur || 50 },

          // 技术属性
          finishing: { base: attrs.finishing?.base || 50, cur: attrs.finishing?.cur || 50 },
          dribbling: { base: attrs.dribbling?.base || 50, cur: attrs.dribbling?.cur || 50 },
          passing: { base: attrs.passing?.base || 50, cur: attrs.passing?.cur || 50 },
          longPassing: { base: attrs.longPassing?.base || 50, cur: attrs.longPassing?.cur || 50 },
          longShots: { base: attrs.longShots?.base || 50, cur: attrs.longShots?.cur || 50 },
          heading: { base: attrs.heading?.base || 50, cur: attrs.heading?.cur || 50 },
          volleys: { base: attrs.volleys?.base || 50, cur: attrs.volleys?.cur || 50 },

          // 防守属性
          standingTackle: { base: attrs.standingTackle?.base || 50, cur: attrs.standingTackle?.cur || 50 },
          slidingTackle: { base: attrs.slidingTackle?.base || 50, cur: attrs.slidingTackle?.cur || 50 },

          // 特殊技术属性
          penalties: { base: attrs.penalties?.base || 50, cur: attrs.penalties?.cur || 50 },
          cornerKick: { base: attrs.cornerKick?.base || 50, cur: attrs.cornerKick?.cur || 50 },
          freeKick: { base: attrs.freeKick?.base || 50, cur: attrs.freeKick?.cur || 50 },

          // 门将属性
          attack: { base: attrs.attack?.base || 50, cur: attrs.attack?.cur || 50 },
          save: { base: attrs.save?.base || 50, cur: attrs.save?.cur || 50 },

          // 特殊属性
          resistanceDamage: { base: attrs.resistanceDamage?.base || 50, cur: attrs.resistanceDamage?.cur || 50 }
        },

        // 战斗核心属性 - battle-engine实际使用的计算后综合值
        attack,
        defend,
        speed,
        power,
        technique,

        // 技能系统
        skills: this.convertToSimpleSkills(hero.skills || []),
        activeSkills: hero.activeSkills || [],

        // 战斗状态
        currentStamina: attrs.stamina?.cur || 100,
        morale: 500,
        fatigue: 0,
        isEnemy: hero.isEnemy || false,
        rating: hero.rating || 0
      };

      this.logger.debug(`球员 ${hero.name} 数据转换完成:`, {
        heroId: battleHero.heroId,
        position: battleHero.position,
        level: battleHero.level,
        attack: battleHero.attack,
        defend: battleHero.defend,
        speed: battleHero.speed,
        power: battleHero.power,
        technique: battleHero.technique
      });

      // 🚀 优化：缓存转换结果
      this.heroConversionCache.set(cacheKey, battleHero);

      return battleHero;
    } catch (error) {
      this.logger.error(`转换球员数据失败: ${JSON.stringify(hero)}`, error);

      // 返回默认格式，确保不会中断战斗流程
      return {
        heroId: `fallback_hero_${index}`,
        name: `默认球员${index}`,
        resId: 0,
        level: 1,
        position: HeroPosition.MC,
        mainPosition: HeroPosition.MC,
        breakLevel: 0,
        starLevel: 0,
        evolutionStage: 0,
        attributes: {
          speed: { base: 50, cur: 50 },
          jumping: { base: 50, cur: 50 },
          strength: { base: 50, cur: 50 },
          stamina: { base: 50, cur: 50 },
          explosiveForce: { base: 50, cur: 50 },
          finishing: { base: 50, cur: 50 },
          dribbling: { base: 50, cur: 50 },
          passing: { base: 50, cur: 50 },
          longPassing: { base: 50, cur: 50 },
          longShots: { base: 50, cur: 50 },
          heading: { base: 50, cur: 50 },
          volleys: { base: 50, cur: 50 },
          standingTackle: { base: 50, cur: 50 },
          slidingTackle: { base: 50, cur: 50 },
          penalties: { base: 50, cur: 50 },
          cornerKick: { base: 50, cur: 50 },
          freeKick: { base: 50, cur: 50 },
          attack: { base: 50, cur: 50 },
          save: { base: 50, cur: 50 },
          resistanceDamage: { base: 50, cur: 50 }
        },
        attack: 50,
        defend: 50,
        speed: 50,
        power: 50,
        technique: 50,
        skills: [],
        activeSkills: [],
        currentStamina: 100,
        morale: 500,
        fatigue: 0,
        isEnemy: false,
        rating: 0
      };
    }
  }

  /**
   * 计算攻击力综合值
   * 基于finishing, longShots, heading等属性
   */
  private calculateAttackValue(attributes: any): number {
    const finishing = attributes.finishing?.cur || 0;
    const longShots = attributes.longShots?.cur || 0;
    const heading = attributes.heading?.cur || 0;
    const volleys = attributes.volleys?.cur || 0;

    // 加权平均计算攻击力
    return Math.round((finishing * 0.4 + longShots * 0.3 + heading * 0.2 + volleys * 0.1));
  }

  /**
   * 计算防守力综合值
   * 基于standingTackle, slidingTackle等属性
   */
  private calculateDefendValue(attributes: any): number {
    const standingTackle = attributes.standingTackle?.cur || 0;
    const slidingTackle = attributes.slidingTackle?.cur || 0;
    const strength = attributes.strength?.cur || 0;

    // 加权平均计算防守力
    return Math.round((standingTackle * 0.5 + slidingTackle * 0.3 + strength * 0.2));
  }

  /**
   * 计算技术综合值
   * 基于dribbling, passing等技术属性
   */
  private calculateTechniqueValue(attributes: any): number {
    const dribbling = attributes.dribbling?.cur || 0;
    const passing = attributes.passing?.cur || 0;
    const longPassing = attributes.longPassing?.cur || 0;

    // 加权平均计算技术值
    return Math.round((dribbling * 0.4 + passing * 0.4 + longPassing * 0.2));
  }



  /**
   * 转换为BattleSkill格式
   * 从hero.schema.ts的HeroSkill转换为精简的BattleSkill
   */
  private convertToSimpleSkills(skills: any[]): any[] {
    this.logger.debug(`convertToSimpleSkills: ${JSON.stringify(skills)}`);

    return skills.map(skill => ({
      skillId: skill.skillId || 0,
      level: skill.level || 1
    }));
  }



  /**
   * 将阵容数据转换为战斗数据格式
   * 统一实现，消除各模块重复代码
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  async convertLineupToBattleData(lineupsData: any, characterId: string): Promise<XResult<BattleTeam>> {
    if (!lineupsData || !lineupsData.lineups || lineupsData.lineups.length === 0) {
      this.logger.error('阵容数据为空或格式错误', lineupsData);
      return XResultUtils.error('阵容数据为空或格式错误', 'LINEUP_DATA_INVALID');
    }

    // 获取当前使用的阵容
    const currentLineupId = lineupsData.activeLineupId;
    const currentLineup:Lineup = lineupsData.lineups.find((f: any) => f.uid === currentLineupId);

    if (!currentLineup) {
      this.logger.error('未找到当前阵容', { currentLineupId, lineups: lineupsData.lineups });
      return XResultUtils.error('未找到当前阵容', 'CURRENT_LINEUP_NOT_FOUND');
    }

    // 提取上场球员ID列表
    const heroIds = this.extractHeroIds(currentLineup);
    if (!heroIds || heroIds.length === 0) {
      this.logger.error('球员ID列表为空');
      this.logger.error(`阵容数据结构: ${JSON.stringify(currentLineup)}`);
      return XResultUtils.error('球员ID列表为空', 'HERO_IDS_EMPTY');
    }

    this.logger.log(`提取到上场球员ID: ${JSON.stringify(heroIds)}`);

    // 使用统一的球员数据获取方法
    const heroesDataResult = await this.getHeroesData(characterId, heroIds);

    if (XResultUtils.isFailure(heroesDataResult)) {
      return XResultUtils.error(`获取球员数据失败: ${heroesDataResult.message}`, heroesDataResult.code);
    }

    const heroesData = heroesDataResult.data;
    if (!heroesData || heroesData.length === 0) {
      return XResultUtils.error('获取球员数据为空', 'HEROES_DATA_EMPTY');
    }

    // 构造BattleTeam格式数据
    const battleData: BattleTeam = {
      teamSide: TeamType.TeamA,  // 默认为A队，实际使用时会被重新设置
      characterId: lineupsData.characterId || lineupsData.uid || characterId,
      teamName: `队伍${lineupsData.uid || characterId}`,
      level: 1,  // 默认等级，实际使用时会被重新设置
      formationId: currentLineup.formationId || 442101,
      tactic: currentLineup.activeAttackTacticId || 101,
      lineupId: currentLineup.uid,
      totalAttack: 0,  // 由战斗引擎计算
      totalDefend: 0,  // 由战斗引擎计算
      score: 0,
      heroes: heroesData,

      // 队伍属性
      attr: {
        morale: 500,
        moraleSlot: 0,
        moraleAcceleration: 0
      },

      // 统计数据
      statistic: {
        shots: 0,
        shotsOnTarget: 0,
        possession: 0,
        passes: 0,
        fouls: 0,
        ballerScoreMap: new Map()
      },

      // 回合信息
      roundInfo: {},

      // 定位球球员信息
      freeKickHero: currentLineup.freeKickHero || '',
      penaltiesHero: currentLineup.penaltiesHero || '',
      cornerKickHero: currentLineup.cornerKickHero || '',
      captainHeroId: currentLineup.captainHeroId || '',
      viceCaptainHeroId: currentLineup.viceCaptainHeroId || ''
    };

    this.logger.log(`转换后的战斗数据: 球员数量=${battleData.heroes.length}, 阵型=${battleData.formationId}`);
    return XResultUtils.ok(battleData);
  }

  /**
   * 从阵容数据中提取球员ID列表
   * 统一的球员ID提取逻辑，支持positionToHeroes格式
   * 已适配Result模式：移除try-catch，使用纯函数逻辑
   */
  private extractHeroIds(lineup: Lineup): string[] {
    const heroIds: string[] = [];

    // 优先处理positionToHeroes格式（当前项目使用的格式）
    if (lineup.positionToHeroes && typeof lineup.positionToHeroes === 'object') {
      this.logger.log('使用positionToHeroes格式提取球员ID');

      // 遍历所有位置
      Object.keys(lineup.positionToHeroes).forEach(position => {
        const heroArr = lineup.positionToHeroes[position];
        if (Array.isArray(heroArr)) {
          heroArr.forEach(heroId => {
            if (heroId && typeof heroId === 'string' && heroId.trim() !== '') {
              heroIds.push(heroId.trim());
            }
          });
        }
      });

      this.logger.log(`从positionToHeroes提取到${heroIds.length}个球员ID: ${JSON.stringify(heroIds)}`);
    }
    
    const filteredIds = heroIds.filter(id => id && id.trim() !== '');
    this.logger.log(`最终提取到${filteredIds.length}个有效球员ID`);

    return filteredIds;
  }
}
